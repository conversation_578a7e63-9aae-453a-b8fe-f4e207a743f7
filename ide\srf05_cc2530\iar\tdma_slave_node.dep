<?xml version="1.0" encoding="iso-8859-1"?>

<project>
  <fileVersion>2</fileVersion>
  <fileChecksum>3567628037</fileChecksum>
  <configuration>
    <name>srf05_cc2530</name>
    <outputs>
      <file>$PROJ_DIR$\..\..\..\source\Components\utils\util.h</file>
      <file>$PROJ_DIR$\..\..\..\source\Components\targets\srf05_soc\hal_joystick.c</file>
      <file>$PROJ_DIR$\..\..\..\source\Components\targets\srf05_soc\hal_board.c</file>
      <file>$PROJ_DIR$\..\..\..\source\Components\utils\util.c</file>
      <file>$PROJ_DIR$\..\..\..\source\Components\targets\srf05_soc\hal_button.c</file>
      <file>$PROJ_DIR$\..\..\..\source\Components\targets\srf05_soc\hal_digio.c</file>
      <file>$PROJ_DIR$\..\..\..\source\Components\targets\srf05_soc\hal_led.c</file>
      <file>$PROJ_DIR$\..\..\..\source\Components\targets\srf05_soc\hal_assert.c</file>
      <file>$PROJ_DIR$\..\..\..\source\Components\targets\srf05_soc\hal_board.h</file>
      <file>$PROJ_DIR$\..\..\..\source\apps\tdma_sensor_network\tdma_slave_node.c</file>
      <file>$PROJ_DIR$\..\..\..\source\Components\basicrf\basic_rf.c</file>
      <file>$PROJ_DIR$\..\..\..\source\Components\targets\interface\hal_rf_security.h</file>
      <file>$PROJ_DIR$\..\..\..\source\Components\basicrf\basic_rf.h</file>
      <file>$PROJ_DIR$\..\..\..\source\Components\basicrf\basic_rf_security.c</file>
      <file>$PROJ_DIR$\..\..\..\source\Components\targets\interface\hal_button.h</file>
      <file>$PROJ_DIR$\..\..\..\source\Components\targets\interface\hal_lcd.h</file>
      <file>$PROJ_DIR$\..\..\..\source\Components\targets\interface\hal_led.h</file>
      <file>$PROJ_DIR$\..\..\..\source\Components\radios\cc2530\hal_rf.c</file>
      <file>$PROJ_DIR$\..\..\..\source\Components\targets\common\hal_lcd_srf05.c</file>
      <file>$PROJ_DIR$\..\..\..\source\Components\targets\interface\hal_assert.h</file>
      <file>$PROJ_DIR$\..\..\..\source\Components\targets\interface\hal_rf.h</file>
      <file>$PROJ_DIR$\..\..\..\source\Components\common\hal_types.h</file>
      <file>$PROJ_DIR$\..\..\..\source\Components\radios\cc2530\adc.h</file>
      <file>$PROJ_DIR$\..\..\..\source\Components\radios\cc2530\hal_mcu.c</file>
      <file>$PROJ_DIR$\..\..\..\source\Components\common\hal_int.c</file>
      <file>$PROJ_DIR$\..\..\..\source\components\common\hal_uart.h</file>
      <file>$PROJ_DIR$\..\..\..\source\Components\targets\interface\hal_mcu.h</file>
      <file>$PROJ_DIR$\..\..\..\source\Components\radios\cc2530\clock.c</file>
      <file>$PROJ_DIR$\..\..\..\source\Components\common\hal_defs.h</file>
      <file>$PROJ_DIR$\..\..\..\source\Components\radios\cc2530\clock.h</file>
      <file>$PROJ_DIR$\..\..\..\source\Components\radios\cc2530\hal_rf_security.c</file>
      <file>$PROJ_DIR$\..\..\..\source\Components\radios\cc2530\adc.c</file>
      <file>$PROJ_DIR$\..\..\..\source\Components\targets\interface\hal_int.h</file>
      <file>$PROJ_DIR$\..\..\..\source\Components\targets\interface\hal_joystick.h</file>
      <file>$PROJ_DIR$\..\..\..\source\Components\targets\interface\hal_digio.h</file>
      <file>$PROJ_DIR$\..\..\..\source\components\common\hal_uart.c</file>
      <file>$PROJ_DIR$\..\..\..\source\Components\common\cc8051\hal_cc8051.h</file>
      <file>$PROJ_DIR$\temp\light_switch\tdma_slave_node.pbi</file>
      <file>$PROJ_DIR$\temp\light_switch\clock.pbi</file>
      <file>$PROJ_DIR$\temp\light_switch\tdma_slave_node.pbd</file>
      <file>$PROJ_DIR$\temp\light_switch\basic_rf.pbi</file>
      <file>$TOOLKIT_DIR$\config\devices\Texas Instruments\lnk51ew_cc2530F256_banked.xcl</file>
      <file>$PROJ_DIR$\temp\light_switch\hal_assert.pbi</file>
      <file>$PROJ_DIR$\temp\light_switch\adc.r51</file>
      <file>$PROJ_DIR$\temp\tdma_slave_node.map</file>
      <file>$PROJ_DIR$\temp\light_switch\hal_rf_security.r51</file>
      <file>$TOOLKIT_DIR$\inc\ioCC2530.h</file>
      <file>$TOOLKIT_DIR$\inc\clib\sysmac.h</file>
      <file>$PROJ_DIR$\temp\light_switch\hal_led.r51</file>
      <file>$PROJ_DIR$\temp\light_switch\util_buffer.pbi</file>
      <file>$PROJ_DIR$\temp\light_switch\tdma_slave_node.r51</file>
      <file>$PROJ_DIR$\temp\light_switch\hal_rf_security.pbi</file>
      <file>$PROJ_DIR$\temp\light_switch\adc.pbi</file>
      <file>$PROJ_DIR$\temp\light_switch\clock.r51</file>
      <file>$PROJ_DIR$\temp\light_switch\hal_int.pbi</file>
      <file>$PROJ_DIR$\temp\light_switch\util_lcd.pbi</file>
      <file>$PROJ_DIR$\temp\light_switch\util.pbi</file>
      <file>$PROJ_DIR$\temp\light_switch\hal_lcd_srf05.r51</file>
      <file>$PROJ_DIR$\temp\light_switch\hal_assert.r51</file>
      <file>$PROJ_DIR$\temp\light_switch\hal_board.r51</file>
      <file>$PROJ_DIR$\temp\light_switch\hal_lcd_srf05.pbi</file>
      <file>$PROJ_DIR$\temp\light_switch\hal_rf.r51</file>
      <file>$PROJ_DIR$\temp\light_switch\hal_joystick.pbi</file>
      <file>$PROJ_DIR$\temp\light_switch\hal_int.r51</file>
      <file>$PROJ_DIR$\temp\light_switch\hal_button.r51</file>
      <file>$PROJ_DIR$\temp\light_switch\basic_rf_security.pbi</file>
      <file>$TOOLKIT_DIR$\inc\clib\string.h</file>
      <file>$TOOLKIT_DIR$\lib\clib\cl-pli-blxd-1e16x01.r51</file>
      <file>$PROJ_DIR$\temp\light_switch\hal_joystick.r51</file>
      <file>$PROJ_DIR$\tdma_slave_node.d51</file>
      <file>$PROJ_DIR$\temp\light_switch\hal_mcu.r51</file>
      <file>$PROJ_DIR$\temp\light_switch\hal_uart.r51</file>
      <file>$PROJ_DIR$\temp\light_switch\hal_mcu.pbi</file>
      <file>$PROJ_DIR$\temp\light_switch\util.r51</file>
      <file>$PROJ_DIR$\temp\light_switch\util_buffer.r51</file>
      <file>$PROJ_DIR$\temp\light_switch\hal_digio.pbi</file>
      <file>$PROJ_DIR$\temp\light_switch\basic_rf_security.r51</file>
      <file>$PROJ_DIR$\temp\light_switch\hal_uart.pbi</file>
      <file>$PROJ_DIR$\temp\light_switch\basic_rf.r51</file>
      <file>$PROJ_DIR$\tdma_slave_node.hex</file>
      <file>$PROJ_DIR$\..\..\..\source\Components\utils\util_lcd.h</file>
      <file>$PROJ_DIR$\temp\light_switch\hal_button.pbi</file>
      <file>$PROJ_DIR$\..\..\..\source\Components\utils\util_buffer.c</file>
      <file>$PROJ_DIR$\temp\light_switch\hal_rf.pbi</file>
      <file>$PROJ_DIR$\..\..\..\source\Components\utils\util_buffer.h</file>
      <file>$PROJ_DIR$\temp\light_switch\hal_led.pbi</file>
      <file>$PROJ_DIR$\temp\light_switch\hal_digio.r51</file>
      <file>$PROJ_DIR$\..\..\..\source\Components\utils\util_lcd.c</file>
      <file>$PROJ_DIR$\temp\light_switch\util_lcd.r51</file>
      <file>$PROJ_DIR$\temp\light_switch\hal_board.pbi</file>
    </outputs>
    <file>
      <name>$PROJ_DIR$\..\..\..\source\Components\targets\srf05_soc\hal_joystick.c</name>
      <outputs>
        <tool>
          <name>BICOMP</name>
          <file> 62</file>
        </tool>
        <tool>
          <name>ICC8051</name>
          <file> 68</file>
        </tool>
      </outputs>
      <inputs>
        <tool>
          <name>BICOMP</name>
          <file> 33 21 8 46 36 28 26 22 34 32</file>
        </tool>
      </inputs>
    </file>
    <file>
      <name>$PROJ_DIR$\..\..\..\source\Components\targets\srf05_soc\hal_board.c</name>
      <outputs>
        <tool>
          <name>BICOMP</name>
          <file> 89</file>
        </tool>
        <tool>
          <name>ICC8051</name>
          <file> 59</file>
        </tool>
      </outputs>
      <inputs>
        <tool>
          <name>BICOMP</name>
          <file> 21 28 34 32 26 8 46 36 15 33 29 25</file>
        </tool>
      </inputs>
    </file>
    <file>
      <name>$PROJ_DIR$\..\..\..\source\Components\utils\util.c</name>
      <outputs>
        <tool>
          <name>BICOMP</name>
          <file> 56</file>
        </tool>
        <tool>
          <name>ICC8051</name>
          <file> 73</file>
        </tool>
      </outputs>
      <inputs>
        <tool>
          <name>BICOMP</name>
          <file> 0 21 28 20</file>
        </tool>
      </inputs>
    </file>
    <file>
      <name>$PROJ_DIR$\..\..\..\source\Components\targets\srf05_soc\hal_button.c</name>
      <outputs>
        <tool>
          <name>BICOMP</name>
          <file> 81</file>
        </tool>
        <tool>
          <name>ICC8051</name>
          <file> 64</file>
        </tool>
      </outputs>
      <inputs>
        <tool>
          <name>BICOMP</name>
          <file> 21 8 46 36 28 26 14</file>
        </tool>
      </inputs>
    </file>
    <file>
      <name>$PROJ_DIR$\..\..\..\source\Components\targets\srf05_soc\hal_digio.c</name>
      <outputs>
        <tool>
          <name>BICOMP</name>
          <file> 75</file>
        </tool>
        <tool>
          <name>ICC8051</name>
          <file> 86</file>
        </tool>
      </outputs>
      <inputs>
        <tool>
          <name>BICOMP</name>
          <file> 21 28 8 46 36 26 32 34</file>
        </tool>
      </inputs>
    </file>
    <file>
      <name>$PROJ_DIR$\..\..\..\source\Components\targets\srf05_soc\hal_led.c</name>
      <outputs>
        <tool>
          <name>BICOMP</name>
          <file> 85</file>
        </tool>
        <tool>
          <name>ICC8051</name>
          <file> 48</file>
        </tool>
      </outputs>
      <inputs>
        <tool>
          <name>BICOMP</name>
          <file> 21 16 8 46 36 28 26</file>
        </tool>
      </inputs>
    </file>
    <file>
      <name>$PROJ_DIR$\..\..\..\source\Components\targets\srf05_soc\hal_assert.c</name>
      <outputs>
        <tool>
          <name>BICOMP</name>
          <file> 42</file>
        </tool>
        <tool>
          <name>ICC8051</name>
          <file> 58</file>
        </tool>
      </outputs>
      <inputs>
        <tool>
          <name>BICOMP</name>
          <file> 19 28 16 21 26</file>
        </tool>
      </inputs>
    </file>
    <file>
      <name>$PROJ_DIR$\..\..\..\source\apps\tdma_sensor_network\tdma_slave_node.c</name>
      <outputs>
        <tool>
          <name>BICOMP</name>
          <file> 37</file>
        </tool>
        <tool>
          <name>ICC8051</name>
          <file> 50</file>
        </tool>
      </outputs>
      <inputs>
        <tool>
          <name>BICOMP</name>
          <file> 22 21 28 46 12 29 14 26 20 80 15 0 19 8 36 32 33 16</file>
        </tool>
      </inputs>
    </file>
    <file>
      <name>$PROJ_DIR$\..\..\..\source\Components\basicrf\basic_rf.c</name>
      <outputs>
        <tool>
          <name>BICOMP</name>
          <file> 40</file>
        </tool>
        <tool>
          <name>ICC8051</name>
          <file> 78 76 63 43 53 70 61 45 57 58 59 64 86 68 48 73 74 88 50 71</file>
        </tool>
      </outputs>
      <inputs>
        <tool>
          <name>BICOMP</name>
          <file> 32 21 26 20 12 28 0 66 47</file>
        </tool>
        <tool>
          <name>ICC8051</name>
          <file> 32 21 26 20 12 28 0 66 47 13 24 8 46 36 31 22 27 29 23 19 17 30 11 18 15 7 16 2 34 33 25 4 14 5 1 6 3 82 84 87 80 9 35</file>
        </tool>
      </inputs>
    </file>
    <file>
      <name>$PROJ_DIR$\..\..\..\source\Components\basicrf\basic_rf_security.c</name>
      <outputs>
        <tool>
          <name>BICOMP</name>
          <file> 65</file>
        </tool>
        <tool>
          <name>ICC8051</name>
          <file> 76</file>
        </tool>
      </outputs>
    </file>
    <file>
      <name>$PROJ_DIR$\..\..\..\source\Components\radios\cc2530\hal_rf.c</name>
      <outputs>
        <tool>
          <name>BICOMP</name>
          <file> 83</file>
        </tool>
        <tool>
          <name>ICC8051</name>
          <file> 61</file>
        </tool>
      </outputs>
      <inputs>
        <tool>
          <name>BICOMP</name>
          <file> 8 46 36 21 28 26 32 19 20 0</file>
        </tool>
      </inputs>
    </file>
    <file>
      <name>$PROJ_DIR$\..\..\..\source\Components\targets\common\hal_lcd_srf05.c</name>
      <outputs>
        <tool>
          <name>BICOMP</name>
          <file> 60</file>
        </tool>
        <tool>
          <name>ICC8051</name>
          <file> 57</file>
        </tool>
      </outputs>
      <inputs>
        <tool>
          <name>BICOMP</name>
          <file> 21 15 26 8 46 36 28 66 47 0</file>
        </tool>
      </inputs>
    </file>
    <file>
      <name>$PROJ_DIR$\..\..\..\source\Components\radios\cc2530\hal_mcu.c</name>
      <outputs>
        <tool>
          <name>BICOMP</name>
          <file> 72</file>
        </tool>
        <tool>
          <name>ICC8051</name>
          <file> 70</file>
        </tool>
      </outputs>
      <inputs>
        <tool>
          <name>BICOMP</name>
          <file> 21 26 29 28 46 19</file>
        </tool>
      </inputs>
    </file>
    <file>
      <name>$PROJ_DIR$\..\..\..\source\Components\common\hal_int.c</name>
      <outputs>
        <tool>
          <name>BICOMP</name>
          <file> 54</file>
        </tool>
        <tool>
          <name>ICC8051</name>
          <file> 63</file>
        </tool>
      </outputs>
      <inputs>
        <tool>
          <name>BICOMP</name>
          <file> 21 28 32 8 46 36 26</file>
        </tool>
      </inputs>
    </file>
    <file>
      <name>$PROJ_DIR$\..\..\..\source\Components\radios\cc2530\clock.c</name>
      <outputs>
        <tool>
          <name>BICOMP</name>
          <file> 38</file>
        </tool>
        <tool>
          <name>ICC8051</name>
          <file> 53</file>
        </tool>
      </outputs>
      <inputs>
        <tool>
          <name>BICOMP</name>
          <file> 29 21 28 46 26</file>
        </tool>
      </inputs>
    </file>
    <file>
      <name>$PROJ_DIR$\..\..\..\source\Components\radios\cc2530\hal_rf_security.c</name>
      <outputs>
        <tool>
          <name>BICOMP</name>
          <file> 51</file>
        </tool>
        <tool>
          <name>ICC8051</name>
          <file> 45</file>
        </tool>
      </outputs>
      <inputs>
        <tool>
          <name>BICOMP</name>
          <file> 8 46 36 21 28 26 20 11 0 66 47</file>
        </tool>
      </inputs>
    </file>
    <file>
      <name>$PROJ_DIR$\..\..\..\source\Components\radios\cc2530\adc.c</name>
      <outputs>
        <tool>
          <name>BICOMP</name>
          <file> 52</file>
        </tool>
        <tool>
          <name>ICC8051</name>
          <file> 43</file>
        </tool>
      </outputs>
      <inputs>
        <tool>
          <name>BICOMP</name>
          <file> 22 21 28 46</file>
        </tool>
      </inputs>
    </file>
    <file>
      <name>$PROJ_DIR$\..\..\..\source\components\common\hal_uart.c</name>
      <outputs>
        <tool>
          <name>BICOMP</name>
          <file> 77</file>
        </tool>
        <tool>
          <name>ICC8051</name>
          <file> 71</file>
        </tool>
      </outputs>
      <inputs>
        <tool>
          <name>BICOMP</name>
          <file> 28 8 46 36 21 26 25</file>
        </tool>
      </inputs>
    </file>
    <file>
      <name>$PROJ_DIR$\temp\light_switch\tdma_slave_node.pbd</name>
      <inputs>
        <tool>
          <name>BILINK</name>
          <file> 52 40 65 38 42 89 81 75 54 62 60 85 72 83 51 77 37 56 49 55</file>
        </tool>
      </inputs>
    </file>
    <file>
      <name>$PROJ_DIR$\tdma_slave_node.d51</name>
      <outputs>
        <tool>
          <name>XLINK</name>
          <file> 44 79</file>
        </tool>
      </outputs>
      <inputs>
        <tool>
          <name>XLINK</name>
          <file> 41 43 78 76 53 58 59 64 86 63 68 57 48 70 61 45 71 50 73 74 88 67</file>
        </tool>
      </inputs>
    </file>
    <file>
      <name>$PROJ_DIR$\..\..\..\source\Components\utils\util_buffer.c</name>
      <outputs>
        <tool>
          <name>BICOMP</name>
          <file> 49</file>
        </tool>
        <tool>
          <name>ICC8051</name>
          <file> 74</file>
        </tool>
      </outputs>
      <inputs>
        <tool>
          <name>BICOMP</name>
          <file> 84 21 8 46 36 28 26 32 19</file>
        </tool>
      </inputs>
    </file>
    <file>
      <name>$PROJ_DIR$\..\..\..\source\Components\utils\util_lcd.c</name>
      <outputs>
        <tool>
          <name>BICOMP</name>
          <file> 55</file>
        </tool>
        <tool>
          <name>ICC8051</name>
          <file> 88</file>
        </tool>
      </outputs>
      <inputs>
        <tool>
          <name>BICOMP</name>
          <file> 8 46 36 21 28 26 15 33 14 19 80 0 20 66 47</file>
        </tool>
      </inputs>
    </file>
    <file>
      <name>[ROOT_NODE]</name>
      <outputs>
        <tool>
          <name>XLINK</name>
          <file> 44 79 69</file>
        </tool>
      </outputs>
    </file>
  </configuration>
</project>


