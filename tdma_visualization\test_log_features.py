#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试增强版本的日志功能
验证清除日志、过滤、右键菜单等功能
"""

import tkinter as tk
from tkinter import ttk, messagebox
import time
import threading

def test_log_features():
    """测试日志功能"""
    print("🧪 测试增强版本日志功能")
    print("=" * 40)
    
    try:
        # 导入增强版本
        from enhanced_monitor import EnhancedTDMAMonitor
        
        # 创建测试窗口
        root = tk.Tk()
        app = EnhancedTDMAMonitor(root)
        
        # 添加测试日志
        def add_test_logs():
            """添加测试日志"""
            test_messages = [
                "[SERIAL] Node 1: Temp=25.6, Vib=0, Sync=1, Slot=1",
                "[DEMO] 演示数据生成中...",
                "[DEBUG] 系统状态检查",
                "[ERROR] 模拟错误信息",
                "[SERIAL] Node 2: Temp=26.1, Vib=1, Sync=1, Slot=2",
                "[DEBUG] 图表更新完成",
                "[SERIAL] Node 3: Temp=24.8, Vib=0, Sync=1, Slot=3",
                "[ERROR] 模拟连接错误",
                "[DEMO] 演示模式运行正常",
                "[DEBUG] 数据库保存成功"
            ]
            
            for i, msg in enumerate(test_messages):
                app.add_log(msg)
                time.sleep(0.5)  # 间隔0.5秒添加
                
        # 启动测试日志生成线程
        log_thread = threading.Thread(target=add_test_logs, daemon=True)
        log_thread.start()
        
        # 显示测试说明
        def show_test_instructions():
            instructions = """
🧪 日志功能测试说明

已添加的功能：
✅ 清除日志按钮
✅ 保存日志按钮  
✅ 日志过滤功能（全部/SERIAL/DEBUG/ERROR/DEMO）
✅ 右键菜单（清除/保存/复制选中）
✅ 快捷键支持（Ctrl+L/S/D）

测试步骤：
1. 观察自动生成的测试日志
2. 尝试使用过滤下拉框过滤不同类型的日志
3. 右键点击日志区域测试右键菜单
4. 选中部分日志文本，右键复制
5. 使用快捷键 Ctrl+L 清除日志
6. 使用快捷键 Ctrl+S 保存日志

注意：
- 过滤功能会实时显示符合条件的日志
- 清除日志后会显示"日志已清除"消息
- 复制功能需要先选中文本
            """
            messagebox.showinfo("测试说明", instructions)
            
        # 添加测试按钮
        test_frame = ttk.Frame(root)
        test_frame.pack(side=tk.TOP, fill=tk.X, padx=10, pady=5)
        
        ttk.Button(test_frame, text="显示测试说明", 
                  command=show_test_instructions).pack(side=tk.LEFT, padx=5)
        
        ttk.Button(test_frame, text="添加更多测试日志", 
                  command=lambda: log_thread.start() if not log_thread.is_alive() else None).pack(side=tk.LEFT, padx=5)
        
        # 显示初始说明
        root.after(2000, show_test_instructions)
        
        print("✅ 测试环境启动成功")
        print("📝 请按照弹出的说明进行测试")
        
        # 运行测试
        root.mainloop()
        
    except ImportError as e:
        print(f"❌ 导入失败: {e}")
        print("请确保 enhanced_monitor.py 文件存在")
        return False
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False
        
    return True

def compare_with_simple_version():
    """对比简化版本的功能"""
    print("\n📊 功能对比")
    print("=" * 40)
    
    features = [
        ("清除日志按钮", "✅", "✅"),
        ("保存日志按钮", "✅", "✅"),
        ("日志过滤功能", "✅", "✅"),
        ("右键菜单", "✅", "✅"),
        ("复制选中日志", "✅", "✅"),
        ("快捷键支持", "✅", "✅"),
        ("日志类型标记", "基础", "增强"),
        ("图表功能", "❌", "✅"),
        ("数据库存储", "❌", "✅"),
        ("性能优化", "❌", "✅")
    ]
    
    print(f"{'功能':<15} {'简化版':<8} {'增强版':<8}")
    print("-" * 35)
    for feature, simple, enhanced in features:
        print(f"{feature:<15} {simple:<8} {enhanced:<8}")
    
    print("\n🎯 增强版本新增功能:")
    print("  • 日志类型自动标记 ([SERIAL], [DEBUG], [ERROR], [DEMO])")
    print("  • 智能日志过滤")
    print("  • 性能优化的日志处理")
    print("  • 与图表和数据库集成的日志")

def main():
    """主函数"""
    print("🚀 TDMA增强版本日志功能测试")
    print("基于简化版本的日志功能，添加到增强版本中")
    print()
    
    # 对比功能
    compare_with_simple_version()
    
    print("\n" + "=" * 50)
    input("按回车键开始测试...")
    
    # 运行测试
    success = test_log_features()
    
    if success:
        print("\n✅ 测试完成！")
        print("增强版本现在具有与简化版本相同的日志功能")
    else:
        print("\n❌ 测试失败！")
        print("请检查错误信息并修复问题")

if __name__ == "__main__":
    main()
