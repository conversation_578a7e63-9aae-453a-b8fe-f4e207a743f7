# TDMA可视化系统数据格式更新说明

## 修改概述

根据用户需求，已成功修改TDMA可视化监控系统，以支持新的数据格式并移除同步状态列的显示。

## 新数据格式支持

### 原始格式
```
Node 1: Temp=25.6, Vib=0, Sync=1, Slot=1
```

### 新格式（已支持）
```
[SERIAL] Node 2: Temp=25.0, Vib=0,Slot=1
```

**主要变化：**
- 缺少 `Sync=` 字段
- 温度值直接为小数格式（无需转换）
- 可能包含 `[SERIAL]` 前缀

## 修改的文件

### 1. enhanced_monitor.py
- **表格列定义**：移除"同步状态"列
- **数据解析**：添加新格式解析模式（格式3）
- **数据存储**：移除sync_status相关存储
- **数据库查询**：更新查询结果显示
- **数据导出**：更新CSV导出格式

### 2. simple_monitor.py  
- **表格列定义**：移除"同步状态"列
- **数据解析**：添加新格式解析模式（格式3）
- **数据存储**：移除sync字段存储
- **数据导出**：更新CSV导出格式

### 3. data_parser.py
- **解析模式**：添加新格式解析支持
- **返回数据**：为新格式设置默认sync_status=1

## 解析逻辑更新

### 支持的数据格式

1. **格式1**：`Node 1: Temp=25.6, Vib=0, Sync=1, Slot=1`
2. **格式2**：`Node 1: Temp=25.0, Vib=0, Sync=1, Slot=1`  
3. **格式3（新）**：`Node 2: Temp=25.0, Vib=0,Slot=1`
4. **格式4（兼容）**：`MASTER: Received from Node 3 - Temp=298, Vib=0`

### 新格式处理逻辑
```python
# 格式3: 新格式 "Node 2: Temp=25.0, Vib=0,Slot=1" (缺少Sync字段)
sensor_pattern3 = r'Node\s+(\d+):\s*Temp=([0-9.]+),\s*Vib=(\d+),\s*Slot=(\d+)'
match3 = re.search(sensor_pattern3, line, re.IGNORECASE)

if match3:
    node_id = int(match3.group(1))
    temperature = float(match3.group(2))  # 直接使用小数格式
    vibration = int(match3.group(3))
    sync_status = 1  # 默认认为已同步
    slot_number = int(match3.group(4))
```

## 界面更新

### 表格显示
- **之前**：时间 | 节点ID | 温度(°C) | 振动 | 同步状态 | 时隙
- **现在**：时间 | 节点ID | 温度(°C) | 振动 | 时隙

### 数据库存储
- 保持原有数据库结构不变
- 新数据的sync_status字段自动设置为1
- 确保向后兼容性

### 数据导出
- **CSV格式更新**：移除同步状态列
- **导出内容**：时间,节点ID,温度,振动,时隙

## 测试验证

### 测试数据
```
[SERIAL] Node 2: Temp=25.0, Vib=0,Slot=1
```

### 解析结果
```
✓ 解析成功:
  节点ID: 2
  温度: 25.0°C
  振动: 0
  时隙: 1
  格式: new_format
```

## 兼容性说明

1. **向后兼容**：仍支持所有旧格式数据
2. **数据库兼容**：现有数据库数据不受影响
3. **功能完整**：除同步状态显示外，所有功能正常

## 使用方法

### 启动系统
```bash
# 简化版本
python run_simple.py

# 增强版本  
python run_enhanced.py

# 或使用批处理文件
run.bat
```

### 数据格式
系统现在自动识别并解析以下格式：
- `[SERIAL] Node 2: Temp=25.0, Vib=0,Slot=1`
- `Node 1: Temp=25.6, Vib=0, Sync=1, Slot=1`
- 其他兼容格式

## 注意事项

1. **同步状态**：新格式数据默认认为已同步
2. **温度精度**：新格式直接支持小数温度值
3. **时隙信息**：从数据中直接提取时隙号
4. **日志记录**：所有解析过程都有详细日志记录

## 更新完成

✅ 数据解析逻辑已更新  
✅ 界面显示已调整  
✅ 数据存储已优化  
✅ 兼容性已验证  
✅ 测试已通过

系统现在可以正确解析和显示新的数据格式：`[SERIAL] Node 2: Temp=25.0, Vib=0,Slot=1`
