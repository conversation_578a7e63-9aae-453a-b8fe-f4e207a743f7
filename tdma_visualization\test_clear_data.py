#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试增强版本的清除数据功能
验证清除实时数据和清除所有数据功能
"""

import tkinter as tk
from tkinter import ttk, messagebox
import time
import threading
from datetime import datetime

def test_clear_data_functionality():
    """测试清除数据功能"""
    print("🧪 测试增强版本清除数据功能")
    print("=" * 50)
    
    try:
        # 导入增强版本
        from enhanced_monitor import EnhancedTDMAMonitor
        
        # 创建测试窗口
        root = tk.Tk()
        app = EnhancedTDMAMonitor(root)
        
        # 生成测试数据
        def generate_test_data():
            """生成测试数据"""
            test_data = [
                "Node 1: Temp=25.6, Vib=0, Sync=1, Slot=1",
                "Node 2: Temp=26.1, Vib=1, Sync=1, Slot=2", 
                "Node 3: Temp=24.8, Vib=0, Sync=1, Slot=3",
                "Node 1: Temp=25.8, Vib=0, Sync=1, Slot=1",
                "Node 2: Temp=26.3, Vib=0, Sync=1, Slot=2",
                "Node 3: Temp=25.0, Vib=1, Sync=1, Slot=3",
                "Node 1: Temp=26.0, Vib=0, Sync=1, Slot=1",
                "Node 2: Temp=26.5, Vib=0, Sync=1, Slot=2",
                "Node 3: Temp=25.2, Vib=0, Sync=1, Slot=3",
                "Node 1: Temp=26.2, Vib=1, Sync=1, Slot=1"
            ]
            
            for i, data in enumerate(test_data):
                app.parse_and_display_data(data, "TEST")
                time.sleep(0.3)  # 间隔0.3秒
                
            app.add_log("[TEST] 测试数据生成完成")
            
        # 检查数据状态
        def check_data_status():
            """检查数据状态"""
            try:
                # 检查表格数据
                table_count = len(app.sensor_tree.get_children())
                
                # 检查内存数据
                memory_count = len(app.sensor_data_list)
                
                # 检查图表数据
                chart_nodes = len(app.node_data)
                chart_points = sum(len(data['timestamps']) for data in app.node_data.values())
                
                # 检查数据库数据
                cursor = app.db_conn.cursor()
                cursor.execute("SELECT COUNT(*) FROM sensor_readings")
                db_count = cursor.fetchone()[0]
                
                status = f"""
📊 当前数据状态:
• 表格显示: {table_count} 条记录
• 内存列表: {memory_count} 条记录  
• 图表节点: {chart_nodes} 个节点
• 图表数据点: {chart_points} 个数据点
• 数据库记录: {db_count} 条记录
                """
                
                app.add_log(f"[TEST] 数据状态检查完成")
                return status
                
            except Exception as e:
                return f"数据状态检查失败: {e}"
        
        # 测试清除实时数据
        def test_clear_realtime():
            """测试清除实时数据"""
            app.add_log("[TEST] 开始测试清除实时数据...")
            
            # 检查清除前状态
            before_status = check_data_status()
            
            # 执行清除
            app.clear_data()
            
            # 检查清除后状态
            after_status = check_data_status()
            
            # 显示结果
            result = f"""
🧪 清除实时数据测试结果:

清除前:{before_status}

清除后:{after_status}

预期结果:
• 表格数据应该被清空
• 内存数据应该被清空
• 图表数据应该被清空
• 数据库数据应该保留
            """
            
            messagebox.showinfo("测试结果", result)
            
        # 测试清除所有数据
        def test_clear_all():
            """测试清除所有数据"""
            app.add_log("[TEST] 开始测试清除所有数据...")
            
            # 先生成一些数据
            generate_test_data()
            time.sleep(2)
            
            # 检查清除前状态
            before_status = check_data_status()
            
            # 执行清除所有数据
            app.clear_all_data()
            
            # 检查清除后状态
            after_status = check_data_status()
            
            # 显示结果
            result = f"""
🧪 清除所有数据测试结果:

清除前:{before_status}

清除后:{after_status}

预期结果:
• 所有数据都应该被清空
• 数据库记录应该为0
• 节点选择列表应该重置
            """
            
            messagebox.showinfo("测试结果", result)
        
        # 创建测试控制面板
        test_frame = ttk.LabelFrame(root, text="清除数据功能测试", padding=10)
        test_frame.pack(side=tk.TOP, fill=tk.X, padx=10, pady=5)
        
        # 测试按钮
        ttk.Button(test_frame, text="1. 生成测试数据", 
                  command=lambda: threading.Thread(target=generate_test_data, daemon=True).start()).pack(side=tk.LEFT, padx=5)
        
        ttk.Button(test_frame, text="2. 检查数据状态", 
                  command=lambda: messagebox.showinfo("数据状态", check_data_status())).pack(side=tk.LEFT, padx=5)
        
        ttk.Button(test_frame, text="3. 测试清除实时数据", 
                  command=test_clear_realtime).pack(side=tk.LEFT, padx=5)
        
        ttk.Button(test_frame, text="4. 测试清除所有数据", 
                  command=test_clear_all).pack(side=tk.LEFT, padx=5)
        
        # 显示测试说明
        def show_test_instructions():
            instructions = """
🧪 清除数据功能测试说明

修复内容:
✅ 清除表格显示数据
✅ 清除内存数据列表 (sensor_data_list)
✅ 清除图表数据 (node_data)
✅ 清除数据队列 (data_queue)
✅ 更新图表显示
✅ 更新数据统计
✅ 添加清除所有数据功能（包括数据库）

测试步骤:
1. 点击"生成测试数据"按钮
2. 点击"检查数据状态"查看当前数据
3. 测试"清除实时数据"功能
4. 测试"清除所有数据"功能

清除方式:
• 按钮: 控制面板的"清除数据"按钮
• 右键菜单: "清除实时数据" / "清除所有数据"
• 快捷键: Ctrl+D (清除实时数据)

注意:
- "清除实时数据"只清除显示和内存数据，保留数据库
- "清除所有数据"会清除包括数据库在内的所有数据
            """
            messagebox.showinfo("测试说明", instructions)
            
        # 显示初始说明
        root.after(1000, show_test_instructions)
        
        print("✅ 测试环境启动成功")
        print("📝 请按照弹出的说明进行测试")
        
        # 运行测试
        root.mainloop()
        
    except ImportError as e:
        print(f"❌ 导入失败: {e}")
        print("请确保 enhanced_monitor.py 文件存在")
        return False
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False
        
    return True

def compare_clear_functions():
    """对比清除功能"""
    print("\n📊 清除功能对比")
    print("=" * 40)
    
    print("修复前的问题:")
    print("  ❌ 只清除表格和sensor_data_list")
    print("  ❌ 不清除图表数据(node_data)")
    print("  ❌ 不清除数据队列")
    print("  ❌ 图表不更新")
    print("  ❌ 没有清除所有数据选项")
    
    print("\n修复后的功能:")
    print("  ✅ 清除表格显示数据")
    print("  ✅ 清除内存数据列表")
    print("  ✅ 清除图表数据")
    print("  ✅ 清除数据队列")
    print("  ✅ 标记图表需要更新")
    print("  ✅ 更新数据统计")
    print("  ✅ 添加清除所有数据功能")
    print("  ✅ 数据库清除选项")
    print("  ✅ 用户确认对话框")

def main():
    """主函数"""
    print("🚀 TDMA增强版本清除数据功能测试")
    print("修复清除数据功能无效的问题")
    print()
    
    # 对比功能
    compare_clear_functions()
    
    print("\n" + "=" * 50)
    input("按回车键开始测试...")
    
    # 运行测试
    success = test_clear_data_functionality()
    
    if success:
        print("\n✅ 测试完成！")
        print("清除数据功能已修复并增强")
    else:
        print("\n❌ 测试失败！")
        print("请检查错误信息并修复问题")

if __name__ == "__main__":
    main()
