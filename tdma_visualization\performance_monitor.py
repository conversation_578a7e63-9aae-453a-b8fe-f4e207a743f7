#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
TDMA监控系统性能监控工具
实时监控系统性能并提供优化建议
"""

import time
import psutil
import threading
from datetime import datetime
import tkinter as tk
from tkinter import ttk

class PerformanceMonitor:
    """性能监控器"""
    
    def __init__(self):
        self.monitoring = False
        self.stats = {
            'cpu_usage': [],
            'memory_usage': [],
            'update_times': [],
            'chart_updates': 0,
            'data_processed': 0
        }
        
    def start_monitoring(self):
        """开始性能监控"""
        self.monitoring = True
        self.monitor_thread = threading.Thread(target=self._monitor_loop, daemon=True)
        self.monitor_thread.start()
        
    def stop_monitoring(self):
        """停止性能监控"""
        self.monitoring = False
        
    def _monitor_loop(self):
        """监控循环"""
        while self.monitoring:
            try:
                # 获取CPU使用率
                cpu_percent = psutil.cpu_percent(interval=1)
                self.stats['cpu_usage'].append(cpu_percent)
                
                # 获取内存使用率
                memory = psutil.virtual_memory()
                self.stats['memory_usage'].append(memory.percent)
                
                # 限制历史数据长度
                if len(self.stats['cpu_usage']) > 60:  # 保留最近60秒
                    self.stats['cpu_usage'] = self.stats['cpu_usage'][-60:]
                    self.stats['memory_usage'] = self.stats['memory_usage'][-60:]
                    
            except Exception as e:
                print(f"性能监控错误: {e}")
                
            time.sleep(1)
            
    def get_performance_report(self):
        """获取性能报告"""
        if not self.stats['cpu_usage']:
            return "暂无性能数据"
            
        avg_cpu = sum(self.stats['cpu_usage']) / len(self.stats['cpu_usage'])
        max_cpu = max(self.stats['cpu_usage'])
        avg_memory = sum(self.stats['memory_usage']) / len(self.stats['memory_usage'])
        max_memory = max(self.stats['memory_usage'])
        
        report = f"""
📊 性能监控报告
{'='*40}
CPU使用率:
  平均: {avg_cpu:.1f}%
  峰值: {max_cpu:.1f}%
  
内存使用率:
  平均: {avg_memory:.1f}%
  峰值: {max_memory:.1f}%
  
图表更新次数: {self.stats['chart_updates']}
数据处理条数: {self.stats['data_processed']}

性能评估:
"""
        
        # 性能评估
        if avg_cpu > 50:
            report += "  ⚠️ CPU使用率较高，建议启用高性能模式\n"
        elif avg_cpu < 20:
            report += "  ✅ CPU使用率正常\n"
        else:
            report += "  ℹ️ CPU使用率中等，可考虑优化\n"
            
        if avg_memory > 70:
            report += "  ⚠️ 内存使用率较高，建议减少数据缓存\n"
        elif avg_memory < 40:
            report += "  ✅ 内存使用率正常\n"
        else:
            report += "  ℹ️ 内存使用率中等\n"
            
        return report
        
    def get_optimization_suggestions(self):
        """获取优化建议"""
        if not self.stats['cpu_usage']:
            return []
            
        suggestions = []
        avg_cpu = sum(self.stats['cpu_usage']) / len(self.stats['cpu_usage'])
        avg_memory = sum(self.stats['memory_usage']) / len(self.stats['memory_usage'])
        
        if avg_cpu > 50:
            suggestions.extend([
                "增加图表更新间隔到8秒",
                "减少数据处理频率到300ms",
                "限制同时显示的数据点到30个"
            ])
            
        if avg_memory > 70:
            suggestions.extend([
                "减少内存中缓存的数据点",
                "定期清理旧数据",
                "使用数据库存储代替内存缓存"
            ])
            
        if len(self.stats['cpu_usage']) > 30:  # 运行超过30秒
            update_frequency = self.stats['chart_updates'] / (len(self.stats['cpu_usage']) / 60)
            if update_frequency > 20:  # 每分钟更新超过20次
                suggestions.append("图表更新过于频繁，建议启用智能更新")
                
        return suggestions

class PerformanceMonitorGUI:
    """性能监控GUI"""
    
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("TDMA系统性能监控")
        self.root.geometry("600x500")
        
        self.monitor = PerformanceMonitor()
        self.create_widgets()
        
    def create_widgets(self):
        """创建界面组件"""
        # 控制面板
        control_frame = ttk.LabelFrame(self.root, text="监控控制", padding=10)
        control_frame.pack(fill=tk.X, padx=10, pady=5)
        
        self.start_btn = ttk.Button(control_frame, text="开始监控", command=self.start_monitoring)
        self.start_btn.pack(side=tk.LEFT, padx=5)
        
        self.stop_btn = ttk.Button(control_frame, text="停止监控", command=self.stop_monitoring)
        self.stop_btn.pack(side=tk.LEFT, padx=5)
        
        self.report_btn = ttk.Button(control_frame, text="生成报告", command=self.show_report)
        self.report_btn.pack(side=tk.LEFT, padx=5)
        
        # 实时状态
        status_frame = ttk.LabelFrame(self.root, text="实时状态", padding=10)
        status_frame.pack(fill=tk.X, padx=10, pady=5)
        
        self.cpu_var = tk.StringVar(value="CPU: 0%")
        self.memory_var = tk.StringVar(value="内存: 0%")
        self.status_var = tk.StringVar(value="状态: 未开始")
        
        ttk.Label(status_frame, textvariable=self.cpu_var).pack(anchor=tk.W)
        ttk.Label(status_frame, textvariable=self.memory_var).pack(anchor=tk.W)
        ttk.Label(status_frame, textvariable=self.status_var).pack(anchor=tk.W)
        
        # 报告显示
        report_frame = ttk.LabelFrame(self.root, text="性能报告", padding=10)
        report_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)
        
        self.report_text = tk.Text(report_frame, wrap=tk.WORD)
        scrollbar = ttk.Scrollbar(report_frame, orient=tk.VERTICAL, command=self.report_text.yview)
        self.report_text.configure(yscrollcommand=scrollbar.set)
        
        self.report_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # 启动状态更新
        self.update_status()
        
    def start_monitoring(self):
        """开始监控"""
        self.monitor.start_monitoring()
        self.start_btn.config(state=tk.DISABLED)
        self.stop_btn.config(state=tk.NORMAL)
        self.status_var.set("状态: 监控中...")
        
    def stop_monitoring(self):
        """停止监控"""
        self.monitor.stop_monitoring()
        self.start_btn.config(state=tk.NORMAL)
        self.stop_btn.config(state=tk.DISABLED)
        self.status_var.set("状态: 已停止")
        
    def show_report(self):
        """显示报告"""
        report = self.monitor.get_performance_report()
        suggestions = self.monitor.get_optimization_suggestions()
        
        full_report = report
        if suggestions:
            full_report += "\n🔧 优化建议:\n"
            for i, suggestion in enumerate(suggestions, 1):
                full_report += f"  {i}. {suggestion}\n"
        else:
            full_report += "\n✅ 系统性能良好，无需优化\n"
            
        self.report_text.delete("1.0", tk.END)
        self.report_text.insert("1.0", full_report)
        
    def update_status(self):
        """更新状态显示"""
        try:
            if self.monitor.stats['cpu_usage']:
                current_cpu = self.monitor.stats['cpu_usage'][-1]
                current_memory = self.monitor.stats['memory_usage'][-1]
                
                self.cpu_var.set(f"CPU: {current_cpu:.1f}%")
                self.memory_var.set(f"内存: {current_memory:.1f}%")
        except:
            pass
            
        self.root.after(1000, self.update_status)
        
    def run(self):
        """运行GUI"""
        self.root.mainloop()

def main():
    """主函数"""
    print("🔍 TDMA系统性能监控工具")
    print("=" * 40)
    print("1. 实时监控CPU和内存使用率")
    print("2. 分析系统性能瓶颈")
    print("3. 提供优化建议")
    print("4. 生成性能报告")
    print()
    
    try:
        # 启动GUI
        gui = PerformanceMonitorGUI()
        gui.run()
    except Exception as e:
        print(f"启动性能监控工具失败: {e}")
        print("请确保已安装psutil: pip install psutil")

if __name__ == "__main__":
    main()
