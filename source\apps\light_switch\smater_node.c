/***********************************************************************************
  Filename:    master_node.c
  Description: TDMA Master Node Implementation.
               Handles discovery, slot allocation, synchronization, and data
               reception.
***********************************************************************************/

#include "basic_rf.h"
#include "hal_board.h"
#include "hal_led.h"
#include "hal_mcu.h"
#include "hal_rf.h"
#include "hal_types.h"
#include "hal_uart.h"
#include <stdlib.h> // For standard library functions
#include <string.h> // For strlen, memset

#if (chip == 2530 || chip == 2531)
#include <ioCC2530.h>
#endif

// Include shared protocol definitions
#include "tdma_protocol.h"

// Master node network state machine
typedef enum {
  STATE_INIT,
  STATE_DISCOVERY,
  STATE_SLOT_ALLOCATION,
  STATE_SYNC_RUNNING
} NetworkState;

// Master node internal structure for managing slave nodes
typedef struct {
  uint8 id;
  uint8 slot;
  uint8 used;
} SlaveInfo;

/***********************************************************************************
 * LOCAL VARIABLES
 */
static basicRfCfg_t basicRfConfig;
static uint8 rxBuffer[128];

// Master node state variables
static volatile uint32 timer_ms_count = 0;
static SlaveInfo slaveList[MAX_SLAVES];
static uint8 numSlavesFound = 0;

static char enable_send_sync = 0;
static uint8 current_slot = BEACON_SLOT;

static uint16 sync_timer_cnt = 0;
/***********************************************************************************
 * FUNCTION DECLARATIONS
 */
void configure_timer3_for_ms_tick(void);
void master_app(void);

/***********************************************************************************
 * HELPER & DEBUG FUNCTIONS
 */
static void debugPrint(const char *str) {
  halUartWrite((uint8 *)str, strlen(str));
}

static void debugPrintValue(const char *label, uint32 value) {
  //  Create a sufficiently large static buffer to construct the complete
  //  message
  // Static variables can prevent the allocation of large arrays on the stack
  // with each call
  char msg_buf[80];
  char *p = msg_buf;

  // 1. copy label to buffer
  while (*label) {
    *p++ = *label++;
  }

  // 2. convert num and append to buffer
  char val_buf[12];
  char *v = val_buf + 10;
  *v = '\0';
  if (value == 0) {
    *--v = '0';
  } else {
    while (value > 0) {
      *--v = (value % 10) + '0';
      value /= 10;
    }
  }
  while (*v) {
    *p++ = *v++;
  }

  // 3. append newline
  *p++ = '\r';
  *p++ = '\n';
  *p = '\0'; // end of string

  // 4. automatic send data
  halUartWrite((uint8 *)msg_buf, p - msg_buf);
  halMcuWaitMs(10);
}

/***********************************************************************************
 * CORE PROTOCOL FUNCTIONS
 */

// Send roll call request to discover slave nodes
static void master_send_roll_call(void) {
  PacketHeader req;
  req.type = PKT_TYPE_ROLL_CALL_REQ;
  req.src_id = MASTER_ID;
  basicRfSendPacket(BROADCAST_ADDR, (uint8 *)&req, sizeof(req));
  debugPrint("MASTER: Sent Roll Call request.\r\n");
}

// Process roll call response from slave nodes
static void master_process_roll_call_response(uint8 *pRcv) {
  RollCallRspPacket *rsp = (RollCallRspPacket *)pRcv;
  uint8 slave_id = rsp->hdr.src_id;

  for (int i = 0; i < numSlavesFound; i++) {
    if (slaveList[i].id == slave_id)
      return; // Already exists
  }

  if (numSlavesFound < MAX_SLAVES) {
    slaveList[numSlavesFound].id = slave_id;
    slaveList[numSlavesFound].used = 1;
    numSlavesFound++;
    debugPrintValue("MASTER: Discovered new slave, ID=", slave_id);
  }
}

// Allocate time slots and broadcast to slaves
static void master_allocate_and_send_slots(void) {
  SlotAllocPacket pkt;
  pkt.hdr.type = PKT_TYPE_SLOT_ALLOC;
  pkt.hdr.src_id = MASTER_ID;
  pkt.num_slaves = numSlavesFound;

  debugPrint("MASTER: Allocating slots...\r\n");
  for (int i = 0; i < numSlavesFound; i++) {
    slaveList[i].slot = SLOT_ALLOC_START_NUM + i;
    pkt.slave_ids[i] = slaveList[i].id;
    pkt.slave_slots[i] = slaveList[i].slot;
    // ... can add logging here ...
    debugPrintValue("allocated id", slaveList[i].id);
    debugPrintValue("allocated slot", slaveList[i].slot);
  }
  debugPrintValue("MASTER: Total slaves found: ", numSlavesFound);
  basicRfSendPacket(BROADCAST_ADDR, (uint8 *)&pkt, sizeof(pkt));

  debugPrint("MASTER: Broadcasted slot allocation.\r\n");
}

// Send periodic synchronization beacon
static void master_send_sync_beacon(uint8 current_slot) {
  SyncBeaconPacket beacon;
  beacon.hdr.type = PKT_TYPE_SYNC_BEACON;
  beacon.hdr.src_id = MASTER_ID;
  beacon.current_slot = current_slot;
  basicRfSendPacket(BROADCAST_ADDR, (uint8 *)&beacon, sizeof(beacon));
}

/***********************************************************************************
 * APPLICATION LOGIC
 */
void master_app() {
  uint32 discovery_start_time;

  // --- Discovery Phase ---
  debugPrint("MASTER: Starting discovery phase...\r\n");
  memset(slaveList, 0, sizeof(slaveList));
  numSlavesFound = 0;
  discovery_start_time = timer_ms_count;

  while (timer_ms_count - discovery_start_time < DISCOVERY_DURATION_MS) {
    master_send_roll_call();
    uint32 listen_start_time = timer_ms_count;
    while (timer_ms_count - listen_start_time < 500) { // Listen for 500ms
      WDCTL = 0xA0;
      WDCTL = 0x50;
      if (basicRfPacketIsReady()) {
        if (basicRfReceive(rxBuffer, 128, NULL) > 0) {
          if (((PacketHeader *)rxBuffer)->type == PKT_TYPE_ROLL_CALL_RSP) {
            master_process_roll_call_response(rxBuffer);
          }
        }
      }
      halMcuWaitMs(1);
    }
  }

  // --- Slot Allocation Phase ---
  if (numSlavesFound > 0) {
    master_allocate_and_send_slots();
  } else {
    debugPrint("MASTER: No slaves found. Will retry after reboot.\r\n");
    while (1) {
      WDCTL = 0xA0;
      WDCTL = 0x50;
      halLedSet(4);
      halMcuWaitMs(500);
      halLedClear(4);
      halMcuWaitMs(500);
    }
  }
  halMcuWaitMs(200); // Wait for slaves to receive slot allocation
  // --- Synchronized TDMA Phase ---
  // Start receiving data

  // Current slot

  debugPrint("\n--- MASTER: Entering Normal Operation ---\n");
  // Enable interrupt counter
  enable_send_sync = 1;
  // Trigger interrupt immediately


  while (1) {
    WDCTL = 0xA0;
    WDCTL = 0x50;

    // Master sends sync signal including current slot, using Timer3 interrupt
    // for slot sync Slaves receive sync signal, get current slot, compare with
    // allocated slot to check if it's their turn If slave receives its own
    // slot, send data packet If not their slot, wait for next sync signal

    // Process one buffered packet at a time (buffer size 128 bytes)
    if (basicRfPacketIsReady()) {

      // debugPrint("MASTER: Processing received packet...\r\n");

      if (basicRfReceive(rxBuffer, 128, NULL) > 0) {
        PacketHeader *hdr = (PacketHeader *)rxBuffer;
        // debugPrintValue("enter--type", hdr->type==PKT_TYPE_SENSOR_DATA);
        if (hdr->type == PKT_TYPE_SENSOR_DATA) {
          // 只有当时隙和ID都匹配的时候才会处理数据包
          for (int i = 0; i < numSlavesFound; i++) {
            // debugPrintValue("enter--src_id", hdr->src_id);

            // debugPrintValue("Checking slave ID ", slaveList[i].id);

            // debugPrintValue("current_slot", current_slot);
            // debugPrintValue("Checking slave slot ", slaveList[i].slot);
            if (slaveList[i].id == hdr->src_id &&
                slaveList[i].slot == current_slot) {

              halLedSet(2);
              SensorDataPacket *data = (SensorDataPacket *)rxBuffer;
              // ... (此处可以添加处理和打印接收数据的代码) ...
              debugPrintValue("MASTER: Rcvd from Node ", data->hdr.src_id);
              // 输出数据到串口,包括当前时隙

              // Standard format output: "Node 1: Temp=25.0, Vib=0,
              // current_slot=1",

              // Build complete message in buffer first
              static char outputBuffer[64];
              char *ptr = outputBuffer;
              const char *prefix = "Node ";
              while (*prefix)
                *ptr++ = *prefix++;

              *ptr++ = '0' + data->hdr.src_id;

              const char *tempLabel = ": Temp=";
              while (*tempLabel)
                *ptr++ = *tempLabel++;

              // Convert temperature to decimal format (divide by 10 for 1
              // decimal place)
              // 传过来的是只保留整数的温度

              uint16 temperature = data->temperature;

              // 整数变成字符串
              if (temperature >= 100) {
                *ptr++ = '0' + (temperature / 100);
                temperature %= 100;
              }
              if (temperature >= 10) {
                *ptr++ = '0' + (temperature / 10);
                temperature %= 10; // Get the last digit
              } else {
                *ptr++ = '0'; // 前导0
              }

              *ptr++ = '0' + temperature;

              const char *vibLabel = ", Vib=";
              while (*vibLabel)
                *ptr++ = *vibLabel++;

              *ptr++ = data->vibration ? '1' : '0';

              const char *syncLabel = ",Slot=";
              while (*syncLabel)
                *ptr++ = *syncLabel++;

              // Find the slot number for this node
              uint8 node_slot = 0;
              for (uint8 j = 0; j < numSlavesFound; j++) {
                if (slaveList[j].id == data->hdr.src_id) {
                  node_slot = slaveList[j].slot;
                  break;
                }
              }
              *ptr++ = '0' + node_slot;

              *ptr++ = '\r';
              *ptr++ = '\n';
              *ptr = '\0';

              // Single atomic output to prevent interleaving
              halUartWrite((uint8 *)outputBuffer, ptr - outputBuffer);

              halLedClear(2);
              break;
            }
          }
        }
      }
    }
    halMcuWaitMs(1);
  }
}

/***********************************************************************************
 * MAIN & ISR
 */
#pragma vector = T3_VECTOR
__interrupt void timer3_isr(void) {
  IRCON &= ~0x08; // 清除T3中断标志
  timer_ms_count++;

  if (enable_send_sync) {
    sync_timer_cnt++;
    // 启用信标同步
    // 大约 slot_duration_ms，切换一次时隙

    if (sync_timer_cnt >= SLOT_DURATION_MS) {
      sync_timer_cnt = 0;

      // 进入下一个时隙
      current_slot++;
      if (current_slot > numSlavesFound) {
        current_slot = SLOT_ALLOC_START_NUM; // Reset to first slot
      }

      // 点亮LED1表示开始时隙同步（中断中不应该有延时）,也不能有打印，
      // 避免在中断中使用耗时操作，导致无限中断

      halLedSet(1);
      // debugPrintValue("send current_slot ", current_slot);

      master_send_sync_beacon(current_slot);

      // 关闭LED1表示结束时隙同步

      halLedClear(1);
    }
  }
}

// 1ms tick configuration for Timer3
void configure_timer3_for_ms_tick(void) {
  T3CTL = 0x00;  // Stop timer and clear configuration
  T3CTL |= 0x08; // Set Modulo Mode and enable overflow interrupt
  T3IE = 1;      // Enable T3 interrupt in IEN1
  T3CTL |= 0xE0; // Prescaler 128 (32MHz/128 = 250kHz)

  T3CC0 = 250; // set top value for 1ms tick (250 counts at 250kHz)

  T3CTL |= 0x10; // Start timer
  EA = 1;        // Enable global interrupt
}

void main(void) {
  WDCTL = 0x00;
  halBoardInit();
  halUartInit(115200);
  configure_timer3_for_ms_tick();

  debugPrint("\r\n\r\n=== TDMA Master Node ===\r\n");

  basicRfConfig.panId = PAN_ID;
  basicRfConfig.channel = RF_CHANNEL;
  basicRfConfig.ackRequest = FALSE;
  basicRfConfig.myAddr = MASTER_ID;

  if (basicRfInit(&basicRfConfig) == FAILED) {
    while (1)
      ; // 初始化失败则停机
  }
  basicRfReceiveOn();

  P1DIR |= 0x1B; // LEDs as output
  WDCTL = 0xAC;  // 启用看门�?

  debugPrint("Initialization complete. Starting application...\r\n");
  master_app();
}