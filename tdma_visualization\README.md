# TDMA传感器网络可视化监控系统

## 📋 项目简介

这是一个用于监控和可视化TDMA传感器网络数据的Python应用程序。该系统通过串口接收CC2530主节点发送的传感器数据，并提供实时可视化界面。

## ✨ 主要功能

### 🔌 串口通信
- 自动检测可用串口
- 支持多种波特率配置
- 实时数据接收和解析
- 连接状态监控

### 📊 数据可视化
- **实时数据表格**：显示最新的传感器读数
- **网络拓扑显示**：实时显示网络状态信息
- **历史数据图表**：温度和振动数据趋势图
- **节点状态监控**：同步状态和时隙信息

### 💾 数据存储
- SQLite数据库存储历史数据
- 自动数据备份
- 支持数据查询和导出

## 🚀 快速开始

### 1. 安装依赖
```bash
cd tdma_visualization
pip install -r requirements.txt
```

### 2. 运行程序
```bash
python main.py
```

### 3. 使用步骤
1. **选择串口**：从下拉菜单选择CC2530连接的串口
2. **设置波特率**：通常为115200（根据您的TDMA系统配置）
3. **点击连接**：建立串口连接
4. **查看数据**：实时监控传感器数据和网络状态

## 📁 项目结构

```
tdma_visualization/
├── main.py              # 主程序文件
├── requirements.txt     # 依赖包列表
├── README.md           # 项目说明
├── data/               # 数据存储目录（自动创建）
│   └── sensor_data.db  # SQLite数据库文件
└── screenshots/        # 界面截图（可选）
```

## 🔧 数据格式支持

程序当前支持解析以下格式的串口数据：

```
Node 1: Temp=25.6, Vib=0, Sync=1, Slot=1
Node 2: Temp=26.2, Vib=1, Sync=1, Slot=2
MASTER: Network synchronized
SLAVE: Sync Lost! Will wait for next beacon...
```

## 🎨 界面布局

```
┌─────────────────────────────────────────────────────────┐
│  串口控制面板: [串口选择] [波特率] [连接按钮] [状态显示]    │
├─────────────────────┬───────────────────────────────────┤
│  网络拓扑显示        │    传感器数据表格                  │
│  - 实时日志输出      │    - 节点ID                      │
│  - 网络状态信息      │    - 温度值                      │
│  - 同步状态         │    - 振动状态                     │
├─────────────────────┴───────────────────────────────────┤
│  历史数据图表                                           │
│  [温度趋势图]              [振动状态图]                  │
└─────────────────────────────────────────────────────────┘
```

## ⚙️ 配置说明

### 串口配置
- **波特率**：支持9600、115200、230400
- **数据位**：8位
- **停止位**：1位
- **校验位**：无

### 数据解析
如果您的TDMA系统输出格式与默认格式不同，可以修改`main.py`中的`parse_and_display_data`函数中的正则表达式。

### 数据库
- 使用SQLite存储历史数据
- 数据库文件位置：`data/sensor_data.db`
- 表结构包含：时间戳、节点ID、温度、振动、同步状态、时隙号

## 🔍 故障排除

### 常见问题

1. **串口连接失败**
   - 检查串口是否被其他程序占用
   - 确认波特率设置正确
   - 检查USB驱动是否正常

2. **数据解析错误**
   - 检查串口输出格式是否匹配
   - 查看控制台错误信息
   - 调整正则表达式匹配规则

3. **图表不更新**
   - 确认数据正在接收
   - 检查时间戳格式
   - 重启程序重新连接

## 📈 扩展功能

可以根据需要添加以下功能：
- 数据导出为CSV/Excel格式
- 报警和通知系统
- 远程监控支持
- 更多图表类型
- 配置文件管理

## 🤝 技术支持

如果遇到问题或需要功能扩展，请检查：
1. Python版本（建议3.7+）
2. 依赖包版本兼容性
3. 串口权限设置
4. 防火墙和安全软件设置
