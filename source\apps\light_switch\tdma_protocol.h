#ifndef TDMA_PROTOCOL_H
#define TDMA_PROTOCOL_H

#include <hal_types.h>
/***********************************************************************************
 * SYSTEM CONFIGURATION - 在此文件中统一配置网络和TDMA参数
 */

// === 网络配置 ===
#define RF_CHANNEL 25
#define PAN_ID 0x2024
#define MASTER_ID 0
#define BROADCAST_ADDR 0xFFFF

// === TDMA系统配置 ===
// 可轻松修改这些参数以适应您的系统需求
// 最大从节点数量 (可设置为 1-20)
#define MAX_SLAVES 5

// TDMA时序参数 (单位: 毫秒)
//时隙周期(ms)
#define SLOT_DURATION_MS 3000     

// 发现阶段持续时间 (等待从节点响应的时间，ms)
#define DISCOVERY_DURATION_MS 5000 // (例如: 5秒)

// === 计算参数 (请勿修改) ===
#define BEACON_SLOT 0              // 时隙0始终用于主节点的信标
#define SLOT_ALLOC_START_NUM 1     // 从节点时隙从1开始



/***********************************************************************************
 * PACKET TYPES - 数据包类型定义
 */
#define PKT_TYPE_ROLL_CALL_REQ 0x10 // 主节点请求节点身份
#define PKT_TYPE_ROLL_CALL_RSP 0x11 // 从节点响应点名
#define PKT_TYPE_SLOT_ALLOC 0x20    // 主节点广播时隙分配表
#define PKT_TYPE_SYNC_BEACON 0x30   // 主节点广播同步信标
#define PKT_TYPE_SENSOR_DATA 0x40   // 从节点发送传感器数据

/***********************************************************************************
 * DATA STRUCTURES - 协议数据结构定义
 */

// 通用数据包头
typedef struct {
  uint8 type;
  uint8 src_id;
} PacketHeader;

// 从节点对点名的响应
typedef struct {
  PacketHeader hdr;
} RollCallRspPacket;

// 主节点广播时隙分配
typedef struct {
  PacketHeader hdr;
  uint8 slave_ids[MAX_SLAVES];
  uint8 slave_slots[MAX_SLAVES];
  uint8 num_slaves;
} SlotAllocPacket;

// 主节点的周期性同步信标
typedef struct {
  PacketHeader hdr;
  uint8 current_slot;
} SyncBeaconPacket;

// 从节点的传感器数据包
typedef struct {
  PacketHeader hdr;
  uint16 temperature; // 温度，单位：摄氏度
  uint16 vibration;   // 振动状态：0=静止 1=振动
} SensorDataPacket;

#endif 