#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
TDMA可视化系统简单启动脚本
直接启动简化版本，避免复杂依赖
"""

import sys

def main():
    print("=" * 50)
    print("TDMA传感器网络可视化监控系统 (简化版)")
    print("=" * 50)
    print()
    
    try:
        # 检查Python版本
        if sys.version_info < (3, 6):
            print("错误: 需要Python 3.6或更高版本")
            print(f"当前版本: {sys.version}")
            input("按回车键退出...")
            return
        
        print(f"Python版本: {sys.version_info.major}.{sys.version_info.minor}.{sys.version_info.micro}")
        print()
        
        # 尝试导入pyserial
        try:
            import serial
            print("✓ 检测到pyserial，支持真实串口连接")
        except ImportError:
            print("⚠ 未检测到pyserial，将使用演示模式")
            print("  如需连接真实串口，请运行: pip install pyserial")
        
        print()
        print("启动监控系统...")
        
        # 启动简化版本
        import simple_monitor
        simple_monitor.main()
        
    except ImportError as e:
        print(f"导入错误: {e}")
        input("按回车键退出...")
    except Exception as e:
        print(f"程序运行错误: {e}")
        input("按回车键退出...")

if __name__ == "__main__":
    main()
