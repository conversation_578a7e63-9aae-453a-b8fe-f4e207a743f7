#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试数据解析功能
"""

import re
import sys
import os

# 添加当前目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_data_parsing():
    """测试数据解析功能"""
    
    # 测试数据样本
    test_lines = [
        "[SERIAL] Node 2: Temp=25.0, Vib=0,Slot=1",  # 新格式
        "Node 1: Temp=25.6, Vib=0, Sync=1, Slot=1",  # 标准格式1
        "Node 1: Temp=25.0, Vib=0, Sync=1, Slot=1",  # 标准格式2
        "MASTER: Received from Node 3 - Temp=298, Vib=0",  # 旧格式
        "Node 3: Temp=23.5, Vib=1, Slot=2",  # 新格式变体
        "Invalid data line",  # 无效数据
    ]
    
    print("=" * 60)
    print("数据解析测试")
    print("=" * 60)
    
    for i, line in enumerate(test_lines, 1):
        print(f"\n测试 {i}: {line}")
        result = parse_sensor_data(line)
        if result:
            print(f"  ✓ 解析成功:")
            print(f"    节点ID: {result['node_id']}")
            print(f"    温度: {result['temperature']}°C")
            print(f"    振动: {result['vibration']}")
            print(f"    同步状态: {result['sync_status']}")
            print(f"    时隙: {result['slot_number']}")
            print(f"    格式: {result.get('format', 'unknown')}")
        else:
            print("  ✗ 解析失败")

def parse_sensor_data(line):
    """解析传感器数据 - 支持多种格式"""
    
    # 格式1: 标准格式 "Node 1: Temp=25.6, Vib=0, Sync=1, Slot=1"
    sensor_pattern1 = r'Node\s+(\d+).*?Temp=([0-9.]+).*?Vib=(\d+).*?Sync=(\d+).*?Slot=(\d+)'
    match1 = re.search(sensor_pattern1, line, re.IGNORECASE)

    # 格式2: 标准格式 "Node 1: Temp=25.0, Vib=0, Sync=1, Slot=1" (新的TDMA输出格式)
    sensor_pattern2 = r'Node\s+(\d+):\s*Temp=([0-9.]+),\s*Vib=(\d+),\s*Sync=(\d+),\s*Slot=(\d+)'
    match2 = re.search(sensor_pattern2, line, re.IGNORECASE)

    # 格式3: 新格式 "Node 2: Temp=25.0, Vib=0,Slot=1" (缺少Sync字段)
    sensor_pattern3 = r'Node\s+(\d+):\s*Temp=([0-9.]+),\s*Vib=(\d+),\s*Slot=(\d+)'
    match3 = re.search(sensor_pattern3, line, re.IGNORECASE)

    # 格式4: 旧版MASTER接收格式 "MASTER: Received from Node 3 - Temp=298, Vib=0" (兼容性)
    sensor_pattern4 = r'MASTER:\s*Received from Node\s+(\d+).*?Temp=([0-9.]+).*?Vib=(\d+)'
    match4 = re.search(sensor_pattern4, line, re.IGNORECASE)

    # 初始化变量
    match = None
    node_id = None
    temperature = None
    vibration = None
    sync_status = None
    slot_number = None
    format_type = None

    if match1:
        match = match1
        node_id = int(match.group(1))
        temperature = float(match.group(2))
        vibration = int(match.group(3))
        sync_status = int(match.group(4))
        slot_number = int(match.group(5))
        format_type = 'standard_format1'
    elif match2:
        match = match2
        node_id = int(match.group(1))
        temperature = float(match.group(2))  # 已经是正确的小数格式
        vibration = int(match.group(3))
        sync_status = int(match.group(4))  # 从数据中获取同步状态
        slot_number = int(match.group(5))  # 从数据中获取时隙号
        format_type = 'standard_format2'
    elif match3:
        match = match3
        node_id = int(match.group(1))
        temperature = float(match.group(2))  # 新格式已经是正确的小数格式
        vibration = int(match.group(3))
        sync_status = 1  # 新格式默认认为已同步（能收到数据说明已同步）
        slot_number = int(match.group(4))  # 从数据中获取时隙号
        format_type = 'new_format'
    elif match4:
        match = match4
        node_id = int(match.group(1))
        temperature = float(match.group(2)) / 10.0  # 298 -> 29.8°C (旧格式转换)
        vibration = int(match.group(3))
        sync_status = 1  # MASTER收到数据说明已同步
        slot_number = node_id  # 使用节点ID作为时隙号
        format_type = 'legacy_master'

    if match and node_id is not None:
        return {
            'node_id': node_id,
            'temperature': temperature,
            'vibration': vibration,
            'sync_status': sync_status,
            'slot_number': slot_number,
            'format': format_type
        }
    
    return None

if __name__ == "__main__":
    test_data_parsing()
    
    print("\n" + "=" * 60)
    print("测试完成")
    print("=" * 60)
    
    input("\n按回车键退出...")
