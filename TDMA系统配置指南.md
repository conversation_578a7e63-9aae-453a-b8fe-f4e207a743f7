# TDMA系统配置指南 🔧

## 🎯 系统优化完成！

您的TDMA系统现在支持**灵活的配置**，可以根据实际需求轻松调整从节点数量和时隙参数。

## 📋 可配置参数

### 1. **从节点数量配置**
```c
// 在编译时定义，支持1-20个从节点
#define MAX_SLAVES 5  // 默认：支持5个从节点
```

### 2. **时序参数配置**
```c
// 超帧周期（毫秒）
#define SUPERFRAME_PERIOD_MS 1000  // 默认：1秒

// 时隙持续时间（毫秒）
#define SLOT_DURATION_MS 50        // 默认：50ms（可调：20-100ms）

// 发现阶段持续时间（毫秒）
#define DISCOVERY_DURATION_MS 5000  // 默认：5秒

// 周期性重新发现间隔（毫秒）
#define REDISCOVERY_INTERVAL_MS 30000  // 默认：30秒
```

## 🔧 不同场景的配置示例

### 场景1：小型网络（2个从节点，快速响应）
```c
#define MAX_SLAVES 2
#define SUPERFRAME_PERIOD_MS 500    // 0.5秒超帧
#define SLOT_DURATION_MS 30         // 30ms时隙
#define DISCOVERY_DURATION_MS 3000  // 3秒发现
```
**特点**：响应快，适合实时控制

### 场景2：中型网络（5个从节点，平衡性能）
```c
#define MAX_SLAVES 5
#define SUPERFRAME_PERIOD_MS 1000   // 1秒超帧
#define SLOT_DURATION_MS 50         // 50ms时隙
#define DISCOVERY_DURATION_MS 5000  // 5秒发现
```
**特点**：默认配置，平衡性能和容量

### 场景3：大型网络（10个从节点，高容量）
```c
#define MAX_SLAVES 10
#define SUPERFRAME_PERIOD_MS 2000   // 2秒超帧
#define SLOT_DURATION_MS 80         // 80ms时隙
#define DISCOVERY_DURATION_MS 8000  // 8秒发现
```
**特点**：支持更多节点，适合传感器网络

### 场景4：超大网络（20个从节点，最大容量）
```c
#define MAX_SLAVES 20
#define SUPERFRAME_PERIOD_MS 3000   // 3秒超帧
#define SLOT_DURATION_MS 100        // 100ms时隙
#define DISCOVERY_DURATION_MS 10000 // 10秒发现
```
**特点**：最大节点数，适合大规模监控

## 📊 系统自动验证

系统会自动验证配置的合理性：

### ✅ 自动检查项目
1. **时序合理性**：确保超帧时间 ≥ 所需最小时间
2. **节点数限制**：确保从节点数 ≤ 20（硬件限制）
3. **时隙最小值**：确保时隙时间 ≥ 20ms（通信需求）

### 🚨 编译时错误提示
```c
// 如果配置不合理，编译时会报错：
#error "SUPERFRAME_PERIOD_MS is too small for the configured number of slots!"
#error "MAX_SLAVES cannot exceed 20 (hardware limitation)"
#error "SLOT_DURATION_MS cannot be less than 20ms (timing constraint)"
```

## 🎯 运行时配置显示

系统启动时会显示当前配置：

```
=== TDMA System Configuration ===
Max Slaves: 5
Superframe Period (ms): 1000
Slot Duration (ms): 50
Total Slots: 6
Min Superframe Time (ms): 300
Guard Time (ms): 700
System Status: OPTIMAL
================================
```

## 🔄 动态特性

### 1. **自适应时隙分配**
- 主节点根据实际发现的从节点数量分配时隙
- 未使用的时隙自动跳过，提高效率

### 2. **灵活的发现机制**
- 可配置的发现持续时间
- 支持周期性重新发现新节点

### 3. **智能时序管理**
- 自动计算保护时间
- 动态调整监听窗口

## 🚀 如何修改配置

### 方法1：直接修改源码
在`light_switch.c`文件顶部修改宏定义：
```c
#define MAX_SLAVES 8              // 改为8个从节点
#define SLOT_DURATION_MS 60       // 改为60ms时隙
```

### 方法2：编译时定义
在IAR项目设置中添加预定义宏：
```
MAX_SLAVES=8
SLOT_DURATION_MS=60
```

### 方法3：创建配置头文件
创建`tdma_config.h`：
```c
#ifndef TDMA_CONFIG_H
#define TDMA_CONFIG_H

#define MAX_SLAVES 8
#define SLOT_DURATION_MS 60
#define SUPERFRAME_PERIOD_MS 1200

#endif
```

## 📈 性能对比

| 配置 | 从节点数 | 超帧周期 | 数据率 | 延迟 | 适用场景 |
|------|----------|----------|--------|------|----------|
| 快速 | 2 | 500ms | 2Hz | 低 | 实时控制 |
| 平衡 | 5 | 1000ms | 1Hz | 中 | 一般监控 |
| 高容量 | 10 | 2000ms | 0.5Hz | 高 | 大规模传感 |
| 最大 | 20 | 3000ms | 0.33Hz | 最高 | 超大网络 |

## 🎉 优化效果

✅ **灵活配置**：支持1-20个从节点  
✅ **自动验证**：编译时检查配置合理性  
✅ **动态分配**：根据实际节点数优化时隙  
✅ **智能调度**：自适应时序管理  
✅ **易于扩展**：简单修改即可适应不同需求  

现在您的TDMA系统具备了工业级的灵活性和可配置性！🚀
