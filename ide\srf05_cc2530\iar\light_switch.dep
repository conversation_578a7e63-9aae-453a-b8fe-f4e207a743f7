<?xml version="1.0" encoding="iso-8859-1"?>

<project>
  <fileVersion>2</fileVersion>
  <fileChecksum>750053012</fileChecksum>
  <configuration>
    <name>srf05_cc2530</name>
    <outputs>
      <file>$PROJ_DIR$\temp\light_switch\TDMA_Modified.pbi</file>
      <file>$PROJ_DIR$\..\..\..\source\Components\utils\util_buffer.h</file>
      <file>$PROJ_DIR$\..\..\..\source\Components\utils\util_lcd.c</file>
      <file>$PROJ_DIR$\light_switch.hex</file>
      <file>$PROJ_DIR$\..\..\..\source\Components\utils\util_lcd.h</file>
      <file>$PROJ_DIR$\temp\light_switch\hal_int.pbi</file>
      <file>$PROJ_DIR$\..\..\..\source\Components\utils\util.h</file>
      <file>$PROJ_DIR$\..\..\..\source\Components\utils\util_buffer.c</file>
      <file>$PROJ_DIR$\temp\light_switch\hal_uart.pbi</file>
      <file>$PROJ_DIR$\temp\light_switch\util_buffer.r51</file>
      <file>$PROJ_DIR$\temp\light_switch\smater_node.r51</file>
      <file>$PROJ_DIR$\temp\light_switch\hal_mcu.pbi</file>
      <file>$PROJ_DIR$\temp\light_switch\pin_test.pbi</file>
      <file>$PROJ_DIR$\temp\light_switch\TDMA.pbi</file>
      <file>$PROJ_DIR$\..\..\..\..\..\..\&#x8D44;&#x6599;\18.&#x6e29;&#x6e7F;&#x5eA6;&#x4F20;&#x611F;&#x5668;DHT11\DHT11\slave.c</file>
      <file>$PROJ_DIR$\..\..\..\..\..\..\&#x8D44;&#x6599;\18.&#x6e29;&#x6e7F;&#x5eA6;&#x4F20;&#x611F;&#x5668;DHT11\DHT11\timer1_led_test.c</file>
      <file>$PROJ_DIR$\temp\light_switch\master.pbi</file>
      <file>$PROJ_DIR$\temp\light_switch\util_lcd.pbi</file>
      <file>$PROJ_DIR$\temp\light_switch\hal_led.r51</file>
      <file>$PROJ_DIR$\temp\light_switch\hal_button.pbi</file>
      <file>$PROJ_DIR$\temp\light_switch\UART.pbi</file>
      <file>$PROJ_DIR$\temp\light_switch\basic_rf.r51</file>
      <file>$PROJ_DIR$\..\..\..\..\..\..\&#x8D44;&#x6599;\18.&#x6e29;&#x6e7F;&#x5eA6;&#x4F20;&#x611F;&#x5668;DHT11\DHT11\TDMA_Modified.c</file>
      <file>$TOOLKIT_DIR$\inc\clib\string.h</file>
      <file>$PROJ_DIR$\temp\light_switch\hal_assert.r51</file>
      <file>$PROJ_DIR$\..\..\..\..\..\..\&#x8D44;&#x6599;\18.&#x6e29;&#x6e7F;&#x5eA6;&#x4F20;&#x611F;&#x5668;DHT11\DHT11\DHT11.C</file>
      <file>$PROJ_DIR$\..\..\..\..\..\..\&#x8D44;&#x6599;\18.&#x6e29;&#x6e7F;&#x5eA6;&#x4F20;&#x611F;&#x5668;DHT11\DHT11\slave_node.c</file>
      <file>$PROJ_DIR$\temp\light_switch\hal_mcu.r51</file>
      <file>$PROJ_DIR$\..\..\..\source\apps\light_switch\TDMA.c</file>
      <file>$PROJ_DIR$\temp\light_switch\hal_rf.r51</file>
      <file>$PROJ_DIR$\temp\light_switch\hal_board.r51</file>
      <file>$PROJ_DIR$\temp\light_switch\light_switch.pbi</file>
      <file>$PROJ_DIR$\temp\light_switch\slave_node.r51</file>
      <file>$PROJ_DIR$\temp\light_switch\TDMA_Modified.r51</file>
      <file>$PROJ_DIR$\temp\light_switch\hal_assert.pbi</file>
      <file>$PROJ_DIR$\temp\light_switch\util.r51</file>
      <file>$PROJ_DIR$\temp\light_switch\clock.r51</file>
      <file>$PROJ_DIR$\temp\light_switch\slave.pbi</file>
      <file>$PROJ_DIR$\..\..\..\source\apps\light_switch\light_switch.c</file>
      <file>$PROJ_DIR$\temp\light_switch\slave.r51</file>
      <file>$PROJ_DIR$\temp\light_switch\hal_board.pbi</file>
      <file>$PROJ_DIR$\temp\light_switch\timer1_led_test.pbi</file>
      <file>$PROJ_DIR$\temp\light_switch\slave_node.pbi</file>
      <file>$PROJ_DIR$\temp\light_switch\util_buffer.pbi</file>
      <file>$PROJ_DIR$\temp\light_switch\hal_uart.r51</file>
      <file>$PROJ_DIR$\temp\light_switch\util_lcd.r51</file>
      <file>$PROJ_DIR$\temp\light_switch\hal_joystick.r51</file>
      <file>$TOOLKIT_DIR$\config\devices\Texas Instruments\lnk51ew_cc2530F256_banked.xcl</file>
      <file>$PROJ_DIR$\..\..\..\..\..\..\&#x8D44;&#x6599;\18.&#x6e29;&#x6e7F;&#x5eA6;&#x4F20;&#x611F;&#x5668;DHT11\DHT11\UART.H</file>
      <file>$PROJ_DIR$\temp\light_switch\main.pbi</file>
      <file>$PROJ_DIR$\temp\light_switch\hal_lcd_srf05.pbi</file>
      <file>$PROJ_DIR$\temp\light_switch\pin_test.r51</file>
      <file>$PROJ_DIR$\temp\light_switch\smater_node.pbi</file>
      <file>$TOOLKIT_DIR$\inc\clib\stdlib.h</file>
      <file>$PROJ_DIR$\..\..\..\source\apps\light_switch\tdma_protocol.h</file>
      <file>$PROJ_DIR$\temp\light_switch\hal_button.r51</file>
      <file>$PROJ_DIR$\temp\light_switch\timer1_led_test.r51</file>
      <file>$PROJ_DIR$\..\..\..\..\..\..\&#x8D44;&#x6599;\18.&#x6e29;&#x6e7F;&#x5eA6;&#x4F20;&#x611F;&#x5668;DHT11\DHT11\main.c</file>
      <file>$PROJ_DIR$\temp\light_switch\hal_led.pbi</file>
      <file>$PROJ_DIR$\..\..\..\tem_test.c</file>
      <file>$TOOLKIT_DIR$\inc\clib\sysmac.h</file>
      <file>$PROJ_DIR$\temp\light_switch\hal_rf.pbi</file>
      <file>$PROJ_DIR$\temp\light_switch\tem_test.r51</file>
      <file>$PROJ_DIR$\temp\light_switch\util.pbi</file>
      <file>$TOOLKIT_DIR$\lib\clib\cl-pli-blxd-1e16x01.r51</file>
      <file>$PROJ_DIR$\light_switch.d51</file>
      <file>$PROJ_DIR$\temp\light_switch\clock.pbi</file>
      <file>$PROJ_DIR$\temp\light_switch\tem_test.pbi</file>
      <file>$PROJ_DIR$\temp\light_switch\basic_rf.pbi</file>
      <file>$PROJ_DIR$\temp\light_switch\light_switch.r51</file>
      <file>$PROJ_DIR$\..\..\..\source\Components\targets\common\hal_lcd_srf05.c</file>
      <file>$PROJ_DIR$\..\..\..\source\Components\common\cc8051\hal_cc8051.h</file>
      <file>$PROJ_DIR$\..\..\..\..\..\..\&#x8D44;&#x6599;\18.&#x6e29;&#x6e7F;&#x5eA6;&#x4F20;&#x611F;&#x5668;DHT11\DHT11\tdma_protocol.h</file>
      <file>$PROJ_DIR$\..\..\..\source\components\common\hal_uart.c</file>
      <file>$PROJ_DIR$\..\..\..\source\Components\common\hal_uart.h</file>
      <file>$PROJ_DIR$\..\..\..\source\Components\common\hal_types.h</file>
      <file>$PROJ_DIR$\..\..\..\source\Components\targets\interface\hal_assert.h</file>
      <file>$PROJ_DIR$\..\..\..\source\Components\basicrf\basic_rf.c</file>
      <file>$PROJ_DIR$\..\..\..\source\Components\basicrf\basic_rf_security.c</file>
      <file>$PROJ_DIR$\..\..\..\source\apps\light_switch\slave_node.c</file>
      <file>$PROJ_DIR$\..\..\..\source\Components\common\hal_defs.h</file>
      <file>$PROJ_DIR$\..\..\..\source\Components\basicrf\basic_rf.h</file>
      <file>$PROJ_DIR$\..\..\..\source\Components\common\hal_int.c</file>
      <file>$PROJ_DIR$\..\..\..\source\Components\basicrf\basic_rf_security.h</file>
      <file>$PROJ_DIR$\..\..\..\source\Components\targets\interface\hal_mcu.h</file>
      <file>$PROJ_DIR$\..\..\..\source\Components\targets\srf05_soc\hal_assert.c</file>
      <file>$PROJ_DIR$\..\..\..\source\Components\radios\cc2530\clock.c</file>
      <file>$PROJ_DIR$\..\..\..\source\Components\radios\cc2530\clock.h</file>
      <file>$PROJ_DIR$\..\..\..\source\Components\targets\srf05_soc\hal_button.c</file>
      <file>$PROJ_DIR$\..\..\..\source\Components\targets\srf05_soc\hal_digio.c</file>
      <file>$PROJ_DIR$\..\..\..\source\Components\targets\interface\hal_lcd.h</file>
      <file>$PROJ_DIR$\..\..\..\source\Components\utils\util.c</file>
      <file>$PROJ_DIR$\..\..\..\source\Components\targets\srf05_soc\hal_board.c</file>
      <file>$PROJ_DIR$\..\..\..\source\Components\targets\interface\hal_button.h</file>
      <file>$PROJ_DIR$\..\..\..\source\Components\targets\interface\hal_digio.h</file>
      <file>$PROJ_DIR$\..\..\..\source\Components\targets\interface\hal_joystick.h</file>
      <file>$PROJ_DIR$\..\..\..\source\Components\targets\srf05_soc\hal_joystick.c</file>
      <file>$PROJ_DIR$\..\..\..\source\Components\targets\interface\hal_int.h</file>
      <file>$PROJ_DIR$\..\..\..\source\Components\radios\cc2530\hal_mcu.c</file>
      <file>$PROJ_DIR$\..\..\..\source\Components\radios\cc2530\adc.c</file>
      <file>$PROJ_DIR$\..\..\..\source\Components\targets\srf05_soc\hal_led.c</file>
      <file>$PROJ_DIR$\..\..\..\source\Components\targets\interface\hal_rf_security.h</file>
      <file>$PROJ_DIR$\..\..\..\source\Components\targets\srf05_soc\hal_board.h</file>
      <file>$PROJ_DIR$\..\..\..\source\Components\radios\cc2530\hal_rf.c</file>
      <file>$PROJ_DIR$\..\..\..\source\Components\radios\cc2530\adc.h</file>
      <file>$PROJ_DIR$\..\..\..\source\Components\targets\interface\hal_rf.h</file>
      <file>$PROJ_DIR$\..\..\..\source\Components\targets\interface\hal_led.h</file>
      <file>$PROJ_DIR$\..\..\..\source\Components\radios\cc2530\hal_rf_security.c</file>
      <file>$PROJ_DIR$\temp\light_switch\main.r51</file>
      <file>$PROJ_DIR$\..\..\..\..\..\..\&#x8D44;&#x6599;\18.&#x6e29;&#x6e7F;&#x5eA6;&#x4F20;&#x611F;&#x5668;DHT11\DHT11\master.c</file>
      <file>$PROJ_DIR$\temp\light_switch\hal_lcd_srf05.r51</file>
      <file>$PROJ_DIR$\temp\light_switch\basic_rf_security.pbi</file>
      <file>$PROJ_DIR$\temp\light_switch\UART.r51</file>
      <file>$PROJ_DIR$\temp\light_switch\TDMA.r51</file>
      <file>$PROJ_DIR$\..\..\..\..\..\..\&#x8D44;&#x6599;\18.&#x6e29;&#x6e7F;&#x5eA6;&#x4F20;&#x611F;&#x5668;DHT11\DHT11\TDMA.c</file>
      <file>$PROJ_DIR$\temp\light_switch.map</file>
      <file>$PROJ_DIR$\temp\light_switch\hal_joystick.pbi</file>
      <file>$PROJ_DIR$\temp\light_switch\hal_digio.pbi</file>
      <file>$PROJ_DIR$\temp\light_switch\light_switch.pbd</file>
      <file>$PROJ_DIR$\temp\light_switch\ledtest.r51</file>
      <file>$PROJ_DIR$\temp\light_switch\adc.pbi</file>
      <file>$PROJ_DIR$\..\..\..\..\..\..\&#x8D44;&#x6599;\18.&#x6e29;&#x6e7F;&#x5eA6;&#x4F20;&#x611F;&#x5668;DHT11\DHT11\DHT11.H</file>
      <file>$PROJ_DIR$\..\..\..\..\..\..\&#x8D44;&#x6599;\18.&#x6e29;&#x6e7F;&#x5eA6;&#x4F20;&#x611F;&#x5668;DHT11\DHT11\smater_node.c</file>
      <file>$PROJ_DIR$\temp\light_switch\basic_rf_security.r51</file>
      <file>$PROJ_DIR$\temp\light_switch\ledtest.pbi</file>
      <file>$PROJ_DIR$\temp\light_switch\DHT11.pbi</file>
      <file>$PROJ_DIR$\..\..\..\..\..\..\&#x8D44;&#x6599;\18.&#x6e29;&#x6e7F;&#x5eA6;&#x4F20;&#x611F;&#x5668;DHT11\DHT11\UART.C</file>
      <file>$PROJ_DIR$\..\..\..\..\..\..\&#x8D44;&#x6599;\18.&#x6e29;&#x6e7F;&#x5eA6;&#x4F20;&#x611F;&#x5668;DHT11\DHT11\pin_test.c</file>
      <file>$PROJ_DIR$\temp\light_switch\hal_rf_security.pbi</file>
      <file>$PROJ_DIR$\temp\light_switch\hal_digio.r51</file>
      <file>$TOOLKIT_DIR$\inc\ioCC2530.h</file>
      <file>$PROJ_DIR$\temp\light_switch\DHT11.r51</file>
      <file>$PROJ_DIR$\temp\light_switch\adc.r51</file>
      <file>$PROJ_DIR$\temp\light_switch\master.r51</file>
      <file>$PROJ_DIR$\..\..\..\..\..\..\&#x8D44;&#x6599;\18.&#x6e29;&#x6e7F;&#x5eA6;&#x4F20;&#x611F;&#x5668;DHT11\DHT11\ledtest.c</file>
      <file>$PROJ_DIR$\temp\light_switch\hal_int.r51</file>
      <file>$PROJ_DIR$\temp\light_switch\hal_rf_security.r51</file>
    </outputs>
    <file>
      <name>$PROJ_DIR$\..\..\..\source\Components\utils\util_lcd.c</name>
      <outputs>
        <tool>
          <name>BICOMP</name>
          <file> 17</file>
        </tool>
        <tool>
          <name>ICC8051</name>
          <file> 45</file>
        </tool>
      </outputs>
      <inputs>
        <tool>
          <name>BICOMP</name>
          <file> 102 130 71 75 80 84 90 95 93 76 4 6 105 23 60</file>
        </tool>
      </inputs>
    </file>
    <file>
      <name>$PROJ_DIR$\..\..\..\source\Components\utils\util_buffer.c</name>
      <outputs>
        <tool>
          <name>BICOMP</name>
          <file> 43</file>
        </tool>
        <tool>
          <name>ICC8051</name>
          <file> 9</file>
        </tool>
      </outputs>
      <inputs>
        <tool>
          <name>BICOMP</name>
          <file> 1 75 102 130 71 80 84 97 76</file>
        </tool>
      </inputs>
    </file>
    <file>
      <name>[ROOT_NODE]</name>
      <outputs>
        <tool>
          <name>XLINK</name>
          <file> 65 115 3</file>
        </tool>
      </outputs>
    </file>
    <file>
      <name>$PROJ_DIR$\..\..\..\..\..\..\&#x8D44;&#x6599;\18.&#x6e29;&#x6e7F;&#x5eA6;&#x4F20;&#x611F;&#x5668;DHT11\DHT11\slave.c</name>
      <outputs>
        <tool>
          <name>BICOMP</name>
          <file> 37</file>
        </tool>
        <tool>
          <name>ICC8051</name>
          <file> 39</file>
        </tool>
      </outputs>
      <inputs>
        <tool>
          <name>BICOMP</name>
          <file> 81 75 80 102 130 71 84 106 105 74 53 60 23</file>
        </tool>
      </inputs>
    </file>
    <file>
      <name>$PROJ_DIR$\..\..\..\..\..\..\&#x8D44;&#x6599;\18.&#x6e29;&#x6e7F;&#x5eA6;&#x4F20;&#x611F;&#x5668;DHT11\DHT11\timer1_led_test.c</name>
      <outputs>
        <tool>
          <name>BICOMP</name>
          <file> 41</file>
        </tool>
        <tool>
          <name>ICC8051</name>
          <file> 56</file>
        </tool>
      </outputs>
      <inputs>
        <tool>
          <name>BICOMP</name>
          <file> 102 130 71 75 80 84 106 74 23 60</file>
        </tool>
      </inputs>
    </file>
    <file>
      <name>$PROJ_DIR$\..\..\..\..\..\..\&#x8D44;&#x6599;\18.&#x6e29;&#x6e7F;&#x5eA6;&#x4F20;&#x611F;&#x5668;DHT11\DHT11\TDMA_Modified.c</name>
      <outputs>
        <tool>
          <name>BICOMP</name>
          <file> 0</file>
        </tool>
        <tool>
          <name>ICC8051</name>
          <file> 33</file>
        </tool>
      </outputs>
      <inputs>
        <tool>
          <name>BICOMP</name>
          <file> 81 75 80 102 130 71 84 106 74 53 60 23</file>
        </tool>
      </inputs>
    </file>
    <file>
      <name>$PROJ_DIR$\..\..\..\..\..\..\&#x8D44;&#x6599;\18.&#x6e29;&#x6e7F;&#x5eA6;&#x4F20;&#x611F;&#x5668;DHT11\DHT11\DHT11.C</name>
      <outputs>
        <tool>
          <name>BICOMP</name>
          <file> 125</file>
        </tool>
        <tool>
          <name>ICC8051</name>
          <file> 131</file>
        </tool>
      </outputs>
      <inputs>
        <tool>
          <name>BICOMP</name>
          <file> 81 75 80 102 130 71 84 106 105 74</file>
        </tool>
      </inputs>
    </file>
    <file>
      <name>$PROJ_DIR$\..\..\..\..\..\..\&#x8D44;&#x6599;\18.&#x6e29;&#x6e7F;&#x5eA6;&#x4F20;&#x611F;&#x5668;DHT11\DHT11\slave_node.c</name>
      <outputs>
        <tool>
          <name>BICOMP</name>
          <file> 42</file>
        </tool>
      </outputs>
    </file>
    <file>
      <name>$PROJ_DIR$\..\..\..\source\apps\light_switch\TDMA.c</name>
      <outputs>
        <tool>
          <name>BICOMP</name>
          <file> 13</file>
        </tool>
        <tool>
          <name>ICC8051</name>
          <file> 113</file>
        </tool>
      </outputs>
      <inputs>
        <tool>
          <name>BICOMP</name>
          <file> 81 75 80 102 130 71 84 106 105 74 53 60 23</file>
        </tool>
      </inputs>
    </file>
    <file>
      <name>$PROJ_DIR$\..\..\..\source\apps\light_switch\light_switch.c</name>
      <outputs>
        <tool>
          <name>BICOMP</name>
          <file> 31</file>
        </tool>
        <tool>
          <name>ICC8051</name>
          <file> 69</file>
        </tool>
      </outputs>
    </file>
    <file>
      <name>$PROJ_DIR$\..\..\..\..\..\..\&#x8D44;&#x6599;\18.&#x6e29;&#x6e7F;&#x5eA6;&#x4F20;&#x611F;&#x5668;DHT11\DHT11\main.c</name>
      <outputs>
        <tool>
          <name>BICOMP</name>
          <file> 49</file>
        </tool>
        <tool>
          <name>ICC8051</name>
          <file> 108</file>
        </tool>
      </outputs>
      <inputs>
        <tool>
          <name>BICOMP</name>
          <file> 121 75 81 80 102 130 71 84 106 105 74 48 23 60</file>
        </tool>
      </inputs>
    </file>
    <file>
      <name>$PROJ_DIR$\..\..\..\tem_test.c</name>
      <outputs>
        <tool>
          <name>BICOMP</name>
          <file> 67</file>
        </tool>
        <tool>
          <name>ICC8051</name>
          <file> 62</file>
        </tool>
      </outputs>
      <inputs>
        <tool>
          <name>BICOMP</name>
          <file> 81 75 80 102 130 71 84 106 105 74 53 60 23</file>
        </tool>
      </inputs>
    </file>
    <file>
      <name>$PROJ_DIR$\light_switch.d51</name>
      <outputs>
        <tool>
          <name>XLINK</name>
          <file> 115 3</file>
        </tool>
      </outputs>
      <inputs>
        <tool>
          <name>XLINK</name>
          <file> 47 132 21 123 36 24 30 55 129 135 46 110 18 27 29 136 44 32 35 9 45 64</file>
        </tool>
      </inputs>
    </file>
    <file>
      <name>$PROJ_DIR$\..\..\..\source\Components\targets\common\hal_lcd_srf05.c</name>
      <outputs>
        <tool>
          <name>BICOMP</name>
          <file> 50</file>
        </tool>
        <tool>
          <name>ICC8051</name>
          <file> 110</file>
        </tool>
      </outputs>
      <inputs>
        <tool>
          <name>BICOMP</name>
          <file> 75 90 84 102 130 71 80 23 60 6</file>
        </tool>
      </inputs>
    </file>
    <file>
      <name>$PROJ_DIR$\..\..\..\source\components\common\hal_uart.c</name>
      <outputs>
        <tool>
          <name>BICOMP</name>
          <file> 8</file>
        </tool>
        <tool>
          <name>ICC8051</name>
          <file> 44</file>
        </tool>
      </outputs>
      <inputs>
        <tool>
          <name>BICOMP</name>
          <file> 80 102 130 71 75 84 74</file>
        </tool>
      </inputs>
    </file>
    <file>
      <name>$PROJ_DIR$\..\..\..\source\Components\basicrf\basic_rf.c</name>
      <outputs>
        <tool>
          <name>BICOMP</name>
          <file> 68</file>
        </tool>
        <tool>
          <name>ICC8051</name>
          <file> 21 123 135 132 36 27 29 136 110 24 30 55 129 46 18 35 9 45 32 44</file>
        </tool>
      </outputs>
      <inputs>
        <tool>
          <name>BICOMP</name>
          <file> 97 75 84 105 81 80 6 23 60</file>
        </tool>
        <tool>
          <name>ICC8051</name>
          <file> 97 75 84 105 81 80 6 23 60 78 82 102 130 71 99 104 86 87 98 76 103 107 101 70 90 85 106 92 94 95 74 88 93 89 96 100 91 7 1 2 4 79 53 54 73</file>
        </tool>
      </inputs>
    </file>
    <file>
      <name>$PROJ_DIR$\..\..\..\source\Components\basicrf\basic_rf_security.c</name>
      <outputs>
        <tool>
          <name>BICOMP</name>
          <file> 111</file>
        </tool>
        <tool>
          <name>ICC8051</name>
          <file> 123</file>
        </tool>
      </outputs>
    </file>
    <file>
      <name>$PROJ_DIR$\..\..\..\source\apps\light_switch\slave_node.c</name>
      <outputs>
        <tool>
          <name>BICOMP</name>
          <file> 42</file>
        </tool>
        <tool>
          <name>ICC8051</name>
          <file> 32</file>
        </tool>
      </outputs>
      <inputs>
        <tool>
          <name>BICOMP</name>
          <file> 81 75 80 102 130 71 84 106 105 74 53 60 23 54</file>
        </tool>
      </inputs>
    </file>
    <file>
      <name>$PROJ_DIR$\..\..\..\source\Components\common\hal_int.c</name>
      <outputs>
        <tool>
          <name>BICOMP</name>
          <file> 5</file>
        </tool>
        <tool>
          <name>ICC8051</name>
          <file> 135</file>
        </tool>
      </outputs>
      <inputs>
        <tool>
          <name>BICOMP</name>
          <file> 75 80 97 102 130 71 84</file>
        </tool>
      </inputs>
    </file>
    <file>
      <name>$PROJ_DIR$\..\..\..\source\Components\targets\srf05_soc\hal_assert.c</name>
      <outputs>
        <tool>
          <name>BICOMP</name>
          <file> 34</file>
        </tool>
        <tool>
          <name>ICC8051</name>
          <file> 24</file>
        </tool>
      </outputs>
      <inputs>
        <tool>
          <name>BICOMP</name>
          <file> 76 80 106 75 84</file>
        </tool>
      </inputs>
    </file>
    <file>
      <name>$PROJ_DIR$\..\..\..\source\Components\radios\cc2530\clock.c</name>
      <outputs>
        <tool>
          <name>BICOMP</name>
          <file> 66</file>
        </tool>
        <tool>
          <name>ICC8051</name>
          <file> 36</file>
        </tool>
      </outputs>
      <inputs>
        <tool>
          <name>BICOMP</name>
          <file> 87 75 80 130 84</file>
        </tool>
      </inputs>
    </file>
    <file>
      <name>$PROJ_DIR$\..\..\..\source\Components\targets\srf05_soc\hal_button.c</name>
      <outputs>
        <tool>
          <name>BICOMP</name>
          <file> 19</file>
        </tool>
        <tool>
          <name>ICC8051</name>
          <file> 55</file>
        </tool>
      </outputs>
      <inputs>
        <tool>
          <name>BICOMP</name>
          <file> 75 102 130 71 80 84 93</file>
        </tool>
      </inputs>
    </file>
    <file>
      <name>$PROJ_DIR$\..\..\..\source\Components\targets\srf05_soc\hal_digio.c</name>
      <outputs>
        <tool>
          <name>BICOMP</name>
          <file> 117</file>
        </tool>
        <tool>
          <name>ICC8051</name>
          <file> 129</file>
        </tool>
      </outputs>
      <inputs>
        <tool>
          <name>BICOMP</name>
          <file> 75 80 102 130 71 84 97 94</file>
        </tool>
      </inputs>
    </file>
    <file>
      <name>$PROJ_DIR$\..\..\..\source\Components\utils\util.c</name>
      <outputs>
        <tool>
          <name>BICOMP</name>
          <file> 63</file>
        </tool>
        <tool>
          <name>ICC8051</name>
          <file> 35</file>
        </tool>
      </outputs>
      <inputs>
        <tool>
          <name>BICOMP</name>
          <file> 6 75 80 105</file>
        </tool>
      </inputs>
    </file>
    <file>
      <name>$PROJ_DIR$\..\..\..\source\Components\targets\srf05_soc\hal_board.c</name>
      <outputs>
        <tool>
          <name>BICOMP</name>
          <file> 40</file>
        </tool>
        <tool>
          <name>ICC8051</name>
          <file> 30</file>
        </tool>
      </outputs>
      <inputs>
        <tool>
          <name>BICOMP</name>
          <file> 75 80 94 97 84 102 130 71 90 95 87 74</file>
        </tool>
      </inputs>
    </file>
    <file>
      <name>$PROJ_DIR$\..\..\..\source\Components\targets\srf05_soc\hal_joystick.c</name>
      <outputs>
        <tool>
          <name>BICOMP</name>
          <file> 116</file>
        </tool>
        <tool>
          <name>ICC8051</name>
          <file> 46</file>
        </tool>
      </outputs>
      <inputs>
        <tool>
          <name>BICOMP</name>
          <file> 95 75 102 130 71 80 84 104 94 97</file>
        </tool>
      </inputs>
    </file>
    <file>
      <name>$PROJ_DIR$\..\..\..\source\Components\radios\cc2530\hal_mcu.c</name>
      <outputs>
        <tool>
          <name>BICOMP</name>
          <file> 11</file>
        </tool>
        <tool>
          <name>ICC8051</name>
          <file> 27</file>
        </tool>
      </outputs>
      <inputs>
        <tool>
          <name>BICOMP</name>
          <file> 75 84 87 80 130 76</file>
        </tool>
      </inputs>
    </file>
    <file>
      <name>$PROJ_DIR$\..\..\..\source\Components\radios\cc2530\adc.c</name>
      <outputs>
        <tool>
          <name>BICOMP</name>
          <file> 120</file>
        </tool>
        <tool>
          <name>ICC8051</name>
          <file> 132</file>
        </tool>
      </outputs>
      <inputs>
        <tool>
          <name>BICOMP</name>
          <file> 104 75 80 130</file>
        </tool>
      </inputs>
    </file>
    <file>
      <name>$PROJ_DIR$\..\..\..\source\Components\targets\srf05_soc\hal_led.c</name>
      <outputs>
        <tool>
          <name>BICOMP</name>
          <file> 58</file>
        </tool>
        <tool>
          <name>ICC8051</name>
          <file> 18</file>
        </tool>
      </outputs>
      <inputs>
        <tool>
          <name>BICOMP</name>
          <file> 75 106 102 130 71 80 84</file>
        </tool>
      </inputs>
    </file>
    <file>
      <name>$PROJ_DIR$\..\..\..\source\Components\radios\cc2530\hal_rf.c</name>
      <outputs>
        <tool>
          <name>BICOMP</name>
          <file> 61</file>
        </tool>
        <tool>
          <name>ICC8051</name>
          <file> 29</file>
        </tool>
      </outputs>
      <inputs>
        <tool>
          <name>BICOMP</name>
          <file> 102 130 71 75 80 84 97 76 105 6</file>
        </tool>
      </inputs>
    </file>
    <file>
      <name>$PROJ_DIR$\..\..\..\source\Components\radios\cc2530\hal_rf_security.c</name>
      <outputs>
        <tool>
          <name>BICOMP</name>
          <file> 128</file>
        </tool>
        <tool>
          <name>ICC8051</name>
          <file> 136</file>
        </tool>
      </outputs>
      <inputs>
        <tool>
          <name>BICOMP</name>
          <file> 102 130 71 75 80 84 105 101 6 23 60</file>
        </tool>
      </inputs>
    </file>
    <file>
      <name>$PROJ_DIR$\..\..\..\..\..\..\&#x8D44;&#x6599;\18.&#x6e29;&#x6e7F;&#x5eA6;&#x4F20;&#x611F;&#x5668;DHT11\DHT11\master.c</name>
      <outputs>
        <tool>
          <name>BICOMP</name>
          <file> 16</file>
        </tool>
        <tool>
          <name>ICC8051</name>
          <file> 133</file>
        </tool>
      </outputs>
      <inputs>
        <tool>
          <name>BICOMP</name>
          <file> 81 75 80 102 130 71 84 106 105 74 53 60 23</file>
        </tool>
      </inputs>
    </file>
    <file>
      <name>$PROJ_DIR$\..\..\..\..\..\..\&#x8D44;&#x6599;\18.&#x6e29;&#x6e7F;&#x5eA6;&#x4F20;&#x611F;&#x5668;DHT11\DHT11\TDMA.c</name>
      <outputs>
        <tool>
          <name>BICOMP</name>
          <file> 13</file>
        </tool>
        <tool>
          <name>ICC8051</name>
          <file> 113</file>
        </tool>
      </outputs>
      <inputs>
        <tool>
          <name>BICOMP</name>
          <file> 81 75 80 102 130 71 84 106 105 74 53 60 23</file>
        </tool>
      </inputs>
    </file>
    <file>
      <name>$PROJ_DIR$\temp\light_switch\light_switch.pbd</name>
      <inputs>
        <tool>
          <name>BILINK</name>
          <file> 120 68 111 66 34 40 19 117 5 116 50 58 11 61 128 8 42 63 43 17</file>
        </tool>
      </inputs>
    </file>
    <file>
      <name>$PROJ_DIR$\..\..\..\..\..\..\&#x8D44;&#x6599;\18.&#x6e29;&#x6e7F;&#x5eA6;&#x4F20;&#x611F;&#x5668;DHT11\DHT11\smater_node.c</name>
      <outputs>
        <tool>
          <name>BICOMP</name>
          <file> 52</file>
        </tool>
        <tool>
          <name>ICC8051</name>
          <file> 10</file>
        </tool>
      </outputs>
      <inputs>
        <tool>
          <name>BICOMP</name>
          <file> 81 75 80 102 130 71 84 106 105 74 53 60 23 72</file>
        </tool>
      </inputs>
    </file>
    <file>
      <name>$PROJ_DIR$\..\..\..\..\..\..\&#x8D44;&#x6599;\18.&#x6e29;&#x6e7F;&#x5eA6;&#x4F20;&#x611F;&#x5668;DHT11\DHT11\UART.C</name>
      <outputs>
        <tool>
          <name>BICOMP</name>
          <file> 20</file>
        </tool>
        <tool>
          <name>ICC8051</name>
          <file> 112</file>
        </tool>
      </outputs>
      <inputs>
        <tool>
          <name>BICOMP</name>
          <file> 130 48</file>
        </tool>
      </inputs>
    </file>
    <file>
      <name>$PROJ_DIR$\..\..\..\..\..\..\&#x8D44;&#x6599;\18.&#x6e29;&#x6e7F;&#x5eA6;&#x4F20;&#x611F;&#x5668;DHT11\DHT11\pin_test.c</name>
      <outputs>
        <tool>
          <name>BICOMP</name>
          <file> 12</file>
        </tool>
        <tool>
          <name>ICC8051</name>
          <file> 51</file>
        </tool>
      </outputs>
      <inputs>
        <tool>
          <name>BICOMP</name>
          <file> 102 130 71 75 80 84 106 74 23 60</file>
        </tool>
      </inputs>
    </file>
    <file>
      <name>$PROJ_DIR$\..\..\..\..\..\..\&#x8D44;&#x6599;\18.&#x6e29;&#x6e7F;&#x5eA6;&#x4F20;&#x611F;&#x5668;DHT11\DHT11\ledtest.c</name>
      <outputs>
        <tool>
          <name>BICOMP</name>
          <file> 124</file>
        </tool>
        <tool>
          <name>ICC8051</name>
          <file> 119</file>
        </tool>
      </outputs>
      <inputs>
        <tool>
          <name>BICOMP</name>
          <file> 81 75 80 102 130 71 84 106 105 74</file>
        </tool>
      </inputs>
    </file>
    <forcedrebuild>
      <name>$PROJ_DIR$\..\..\..\..\..\..\&#x8D44;&#x6599;\18.&#x6e29;&#x6e7F;&#x5eA6;&#x4F20;&#x611F;&#x5668;DHT11\DHT11\smater_node.c</name>
      <tool>ICC8051</tool>
    </forcedrebuild>
  </configuration>
</project>


