<?xml version="1.0" encoding="iso-8859-1"?>

<project>
  <fileVersion>2</fileVersion>
  <fileChecksum>3252242052</fileChecksum>
  <configuration>
    <name>srf05_cc2530_91</name>
    <outputs>
      <file>$PROJ_DIR$\..\..\..\..\..\..\&#x8D44;&#x6599;\18.&#x6e29;&#x6e7F;&#x5eA6;&#x4F20;&#x611F;&#x5668;DHT11\DHT11\DHT11.C</file>
      <file>$PROJ_DIR$\..\..\..\..\..\..\&#x8D44;&#x6599;\18.&#x6e29;&#x6e7F;&#x5eA6;&#x4F20;&#x611F;&#x5668;DHT11\DHT11\DHT11.H</file>
      <file>$PROJ_DIR$\..\..\..\source\Components\radios\cc2530\hal_rf_security.c</file>
      <file>$PROJ_DIR$\..\..\..\source\Components\targets\interface\hal_lcd.h</file>
      <file>$PROJ_DIR$\..\..\..\source\Components\targets\interface\hal_mcu.h</file>
      <file>$PROJ_DIR$\..\..\..\..\..\..\&#x8D44;&#x6599;\18.&#x6e29;&#x6e7F;&#x5eA6;&#x4F20;&#x611F;&#x5668;DHT11\DHT11\UART.H</file>
      <file>$PROJ_DIR$\..\..\..\source\Components\common\hal_defs.h</file>
      <file>$PROJ_DIR$\..\..\..\source\Components\common\hal_types.h</file>
      <file>$PROJ_DIR$\..\..\..\source\Components\radios\cc2530\adc.c</file>
      <file>$PROJ_DIR$\..\..\..\..\..\..\&#x8D44;&#x6599;\18.&#x6e29;&#x6e7F;&#x5eA6;&#x4F20;&#x611F;&#x5668;DHT11\DHT11\UART.C</file>
      <file>$PROJ_DIR$\..\..\..\source\Components\basicrf\basic_rf.c</file>
      <file>$PROJ_DIR$\..\..\..\source\Components\radios\cc2530\clock.h</file>
      <file>$PROJ_DIR$\..\..\..\source\Components\targets\interface\hal_button.h</file>
      <file>$PROJ_DIR$\..\..\..\source\Components\common\hal_int.c</file>
      <file>$PROJ_DIR$\..\..\..\source\Components\targets\interface\hal_digio.h</file>
      <file>$PROJ_DIR$\..\..\..\source\Components\radios\cc2530\clock.c</file>
      <file>$PROJ_DIR$\..\..\..\source\Components\targets\interface\hal_led.h</file>
      <file>$PROJ_DIR$\..\..\..\source\Components\radios\cc2530\adc.h</file>
      <file>$PROJ_DIR$\..\..\..\source\Components\radios\cc2530\hal_mcu.c</file>
      <file>$PROJ_DIR$\..\..\..\source\Components\radios\cc2530\hal_rf.c</file>
      <file>$PROJ_DIR$\..\..\..\source\Components\basicrf\basic_rf.h</file>
      <file>$PROJ_DIR$\..\..\..\source\Components\targets\interface\hal_assert.h</file>
      <file>$PROJ_DIR$\..\..\..\source\Components\basicrf\basic_rf_security.c</file>
      <file>$PROJ_DIR$\..\..\..\source\Components\targets\interface\hal_joystick.h</file>
      <file>$PROJ_DIR$\..\..\..\..\..\..\&#x8D44;&#x6599;\18.&#x6e29;&#x6e7F;&#x5eA6;&#x4F20;&#x611F;&#x5668;DHT11\DHT11\main.c</file>
      <file>$PROJ_DIR$\..\..\..\source\Components\common\cc8051\hal_cc8051.h</file>
      <file>$PROJ_DIR$\..\..\..\source\Components\targets\common\hal_lcd_srf05.c</file>
      <file>$PROJ_DIR$\..\..\..\source\Components\targets\interface\hal_int.h</file>
      <file>$PROJ_DIR$\temp\light_switch\adc.r51</file>
      <file>$PROJ_DIR$\temp\light_switch\hal_button.r51</file>
      <file>$PROJ_DIR$\temp\light_switch\hal_digio.pbi</file>
      <file>$PROJ_DIR$\temp\light_switch\UART.pbi</file>
      <file>$PROJ_DIR$\temp\light_switch\basic_rf_security.pbi</file>
      <file>$PROJ_DIR$\temp\light_switch\util_lcd.pbi</file>
      <file>$PROJ_DIR$\temp\light_switch\hal_led.r51</file>
      <file>$PROJ_DIR$\light_switch.d51</file>
      <file>$TOOLKIT_DIR$\inc\clib\string.h</file>
      <file>$PROJ_DIR$\temp\light_switch\clock.pbi</file>
      <file>$PROJ_DIR$\temp\light_switch\hal_board.r51</file>
      <file>$PROJ_DIR$\temp\light_switch\hal_assert.pbi</file>
      <file>$PROJ_DIR$\temp\light_switch\hal_mcu.pbi</file>
      <file>$PROJ_DIR$\temp\light_switch\basic_rf.r51</file>
      <file>$PROJ_DIR$\temp\light_switch\hal_assert.r51</file>
      <file>$PROJ_DIR$\..\..\..\source\Components\common\hal_uart.h</file>
      <file>$PROJ_DIR$\temp\light_switch\clock.r51</file>
      <file>$PROJ_DIR$\temp\light_switch\DHT11.r51</file>
      <file>$PROJ_DIR$\temp\light_switch\hal_int.r51</file>
      <file>$PROJ_DIR$\temp\light_switch\hal_rf_security.pbi</file>
      <file>$PROJ_DIR$\temp\light_switch\light_switch.pbi</file>
      <file>$PROJ_DIR$\temp\light_switch\main.r51</file>
      <file>$PROJ_DIR$\temp\light_switch\hal_lcd_srf05.r51</file>
      <file>$PROJ_DIR$\temp\light_switch\hal_rf_security.r51</file>
      <file>$PROJ_DIR$\temp\light_switch\hal_button.pbi</file>
      <file>$PROJ_DIR$\temp\light_switch\main.pbi</file>
      <file>$TOOLKIT_DIR$\inc\clib\sysmac.h</file>
      <file>$PROJ_DIR$\temp\light_switch\util_buffer.r51</file>
      <file>$PROJ_DIR$\temp\light_switch\util.r51</file>
      <file>$PROJ_DIR$\temp\light_switch\hal_lcd_srf05.pbi</file>
      <file>$PROJ_DIR$\temp\light_switch\hal_digio.r51</file>
      <file>$PROJ_DIR$\temp\light_switch\util_lcd.r51</file>
      <file>$PROJ_DIR$\temp\light_switch\light_switch.r51</file>
      <file>$PROJ_DIR$\temp\light_switch\DHT11.pbi</file>
      <file>$PROJ_DIR$\temp\light_switch\hal_board.pbi</file>
      <file>$TOOLKIT_DIR$\inc\ioCC2530.h</file>
      <file>$PROJ_DIR$\temp\light_switch\UART.r51</file>
      <file>$PROJ_DIR$\temp\light_switch\hal_led.pbi</file>
      <file>$PROJ_DIR$\temp\light_switch\basic_rf.pbi</file>
      <file>$PROJ_DIR$\..\..\..\source\apps\light_switch\light_switch.c</file>
      <file>$PROJ_DIR$\temp\light_switch\hal_mcu.r51</file>
      <file>$PROJ_DIR$\temp\light_switch\hal_joystick.pbi</file>
      <file>$PROJ_DIR$\temp\light_switch\hal_int.pbi</file>
      <file>$PROJ_DIR$\temp\light_switch\util_buffer.pbi</file>
      <file>$PROJ_DIR$\temp\light_switch\util.pbi</file>
      <file>$PROJ_DIR$\temp\light_switch\hal_joystick.r51</file>
      <file>$PROJ_DIR$\temp\light_switch\hal_rf.r51</file>
      <file>$PROJ_DIR$\temp\light_switch\basic_rf_security.r51</file>
      <file>$PROJ_DIR$\temp\light_switch\light_switch.pbd</file>
      <file>$PROJ_DIR$\temp\light_switch\adc.pbi</file>
      <file>$PROJ_DIR$\temp\light_switch\hal_rf.pbi</file>
      <file>$PROJ_DIR$\..\..\..\source\Components\utils\util_lcd.c</file>
      <file>$PROJ_DIR$\..\..\..\source\Components\utils\util_buffer.c</file>
      <file>$PROJ_DIR$\..\..\..\source\Components\utils\util_lcd.h</file>
      <file>$PROJ_DIR$\..\..\..\source\Components\utils\util_buffer.h</file>
      <file>$PROJ_DIR$\..\..\..\source\Components\targets\srf05_soc\hal_assert.c</file>
      <file>$PROJ_DIR$\..\..\..\source\Components\targets\srf05_soc\hal_board.c</file>
      <file>$PROJ_DIR$\..\..\..\source\Components\targets\srf05_soc\hal_board.h</file>
      <file>$PROJ_DIR$\..\..\..\source\Components\targets\srf05_soc\hal_button.c</file>
      <file>$PROJ_DIR$\..\..\..\source\Components\targets\srf05_soc\hal_joystick.c</file>
      <file>$PROJ_DIR$\..\..\..\source\Components\utils\util.c</file>
      <file>$PROJ_DIR$\..\..\..\source\Components\targets\srf05_soc\hal_digio.c</file>
      <file>$PROJ_DIR$\..\..\..\source\Components\targets\srf05_soc\hal_led.c</file>
      <file>$PROJ_DIR$\..\..\..\source\Components\utils\util.h</file>
      <file>$PROJ_DIR$\..\..\..\source\Components\targets\interface\hal_rf.h</file>
      <file>$PROJ_DIR$\..\..\..\source\Components\targets\interface\hal_rf_security.h</file>
    </outputs>
    <file>
      <name>$PROJ_DIR$\..\..\..\..\..\..\&#x8D44;&#x6599;\18.&#x6e29;&#x6e7F;&#x5eA6;&#x4F20;&#x611F;&#x5668;DHT11\DHT11\DHT11.C</name>
      <outputs>
        <tool>
          <name>BICOMP</name>
          <file> 61</file>
        </tool>
        <tool>
          <name>ICC8051</name>
          <file> 45</file>
        </tool>
      </outputs>
      <inputs>
        <tool>
          <name>BICOMP</name>
          <file> 63</file>
        </tool>
        <tool>
          <name>ICC8051</name>
          <file> 63</file>
        </tool>
      </inputs>
    </file>
    <file>
      <name>$PROJ_DIR$\..\..\..\source\Components\radios\cc2530\hal_rf_security.c</name>
      <outputs>
        <tool>
          <name>BICOMP</name>
          <file> 47</file>
        </tool>
        <tool>
          <name>ICC8051</name>
          <file> 51</file>
        </tool>
      </outputs>
      <inputs>
        <tool>
          <name>BICOMP</name>
          <file> 85 63 25 7 6 4 92 93 91 36 54</file>
        </tool>
        <tool>
          <name>ICC8051</name>
          <file> 85 63 25 7 6 4 92 93 91 36 54</file>
        </tool>
      </inputs>
    </file>
    <file>
      <name>$PROJ_DIR$\..\..\..\source\Components\radios\cc2530\adc.c</name>
      <outputs>
        <tool>
          <name>BICOMP</name>
          <file> 77</file>
        </tool>
        <tool>
          <name>ICC8051</name>
          <file> 28</file>
        </tool>
      </outputs>
      <inputs>
        <tool>
          <name>BICOMP</name>
          <file> 17 7 6 63</file>
        </tool>
        <tool>
          <name>ICC8051</name>
          <file> 17 7 6 63</file>
        </tool>
      </inputs>
    </file>
    <file>
      <name>$PROJ_DIR$\..\..\..\..\..\..\&#x8D44;&#x6599;\18.&#x6e29;&#x6e7F;&#x5eA6;&#x4F20;&#x611F;&#x5668;DHT11\DHT11\UART.C</name>
      <outputs>
        <tool>
          <name>BICOMP</name>
          <file> 31</file>
        </tool>
        <tool>
          <name>ICC8051</name>
          <file> 64</file>
        </tool>
      </outputs>
      <inputs>
        <tool>
          <name>BICOMP</name>
          <file> 63 5</file>
        </tool>
        <tool>
          <name>ICC8051</name>
          <file> 63 5</file>
        </tool>
      </inputs>
    </file>
    <file>
      <name>$PROJ_DIR$\..\..\..\source\Components\basicrf\basic_rf.c</name>
      <outputs>
        <tool>
          <name>BICOMP</name>
          <file> 66</file>
        </tool>
        <tool>
          <name>ICC8051</name>
          <file> 41</file>
        </tool>
      </outputs>
      <inputs>
        <tool>
          <name>BICOMP</name>
          <file> 27 7 4 92 20 6 91 36 54</file>
        </tool>
        <tool>
          <name>ICC8051</name>
          <file> 27 7 4 92 20 6 91 36 54</file>
        </tool>
      </inputs>
    </file>
    <file>
      <name>$PROJ_DIR$\..\..\..\source\Components\common\hal_int.c</name>
      <outputs>
        <tool>
          <name>BICOMP</name>
          <file> 70</file>
        </tool>
        <tool>
          <name>ICC8051</name>
          <file> 46</file>
        </tool>
      </outputs>
      <inputs>
        <tool>
          <name>BICOMP</name>
          <file> 7 6 27 85 63 25 4</file>
        </tool>
        <tool>
          <name>ICC8051</name>
          <file> 7 6 27 85 63 25 4</file>
        </tool>
      </inputs>
    </file>
    <file>
      <name>$PROJ_DIR$\..\..\..\source\Components\radios\cc2530\clock.c</name>
      <outputs>
        <tool>
          <name>BICOMP</name>
          <file> 37</file>
        </tool>
        <tool>
          <name>ICC8051</name>
          <file> 44</file>
        </tool>
      </outputs>
      <inputs>
        <tool>
          <name>BICOMP</name>
          <file> 11 7 6 63 4</file>
        </tool>
        <tool>
          <name>ICC8051</name>
          <file> 11 7 6 63 4</file>
        </tool>
      </inputs>
    </file>
    <file>
      <name>$PROJ_DIR$\..\..\..\source\Components\radios\cc2530\hal_mcu.c</name>
      <outputs>
        <tool>
          <name>BICOMP</name>
          <file> 40</file>
        </tool>
        <tool>
          <name>ICC8051</name>
          <file> 68</file>
        </tool>
      </outputs>
      <inputs>
        <tool>
          <name>BICOMP</name>
          <file> 7 4 11 6 63 21</file>
        </tool>
        <tool>
          <name>ICC8051</name>
          <file> 7 4 11 6 63 21</file>
        </tool>
      </inputs>
    </file>
    <file>
      <name>$PROJ_DIR$\..\..\..\source\Components\radios\cc2530\hal_rf.c</name>
      <outputs>
        <tool>
          <name>BICOMP</name>
          <file> 78</file>
        </tool>
        <tool>
          <name>ICC8051</name>
          <file> 74</file>
        </tool>
      </outputs>
      <inputs>
        <tool>
          <name>BICOMP</name>
          <file> 85 63 25 7 6 4 27 21 92 91</file>
        </tool>
        <tool>
          <name>ICC8051</name>
          <file> 85 63 25 7 6 4 27 21 92 91</file>
        </tool>
      </inputs>
    </file>
    <file>
      <name>$PROJ_DIR$\..\..\..\source\Components\basicrf\basic_rf_security.c</name>
      <outputs>
        <tool>
          <name>BICOMP</name>
          <file> 32</file>
        </tool>
        <tool>
          <name>ICC8051</name>
          <file> 75</file>
        </tool>
      </outputs>
    </file>
    <file>
      <name>$PROJ_DIR$\..\..\..\..\..\..\&#x8D44;&#x6599;\18.&#x6e29;&#x6e7F;&#x5eA6;&#x4F20;&#x611F;&#x5668;DHT11\DHT11\main.c</name>
      <outputs>
        <tool>
          <name>BICOMP</name>
          <file> 53</file>
        </tool>
        <tool>
          <name>ICC8051</name>
          <file> 49</file>
        </tool>
      </outputs>
      <inputs>
        <tool>
          <name>BICOMP</name>
          <file> 63 36 54 5 1 85 25 7 6 4 43</file>
        </tool>
        <tool>
          <name>ICC8051</name>
          <file> 63 36 54 5 1 85 25 7 6 4 43</file>
        </tool>
      </inputs>
    </file>
    <file>
      <name>$PROJ_DIR$\..\..\..\source\Components\targets\common\hal_lcd_srf05.c</name>
      <outputs>
        <tool>
          <name>BICOMP</name>
          <file> 57</file>
        </tool>
        <tool>
          <name>ICC8051</name>
          <file> 50</file>
        </tool>
      </outputs>
      <inputs>
        <tool>
          <name>BICOMP</name>
          <file> 7 3 4 85 63 25 6 36 54 91</file>
        </tool>
        <tool>
          <name>ICC8051</name>
          <file> 7 3 4 85 63 25 6 36 54 91</file>
        </tool>
      </inputs>
    </file>
    <file>
      <name>[ROOT_NODE]</name>
      <outputs>
        <tool>
          <name>XLINK</name>
          <file> 35</file>
        </tool>
      </outputs>
    </file>
    <file>
      <name>$PROJ_DIR$\..\..\..\source\apps\light_switch\light_switch.c</name>
      <outputs>
        <tool>
          <name>BICOMP</name>
          <file> 48</file>
        </tool>
        <tool>
          <name>ICC8051</name>
          <file> 60</file>
        </tool>
      </outputs>
      <inputs>
        <tool>
          <name>ICC8051</name>
          <file> 3 7 16 23 21 6 85 63 25 4 27 12 92 81 91 20</file>
        </tool>
      </inputs>
    </file>
    <file>
      <name>$PROJ_DIR$\temp\light_switch\light_switch.pbd</name>
      <inputs>
        <tool>
          <name>BILINK</name>
          <file> 61 31 77 66 32 37 39 62 52 30 70 69 57 65 40 78 47 53 72 71 33</file>
        </tool>
      </inputs>
    </file>
    <file>
      <name>$PROJ_DIR$\..\..\..\source\Components\utils\util_lcd.c</name>
      <outputs>
        <tool>
          <name>BICOMP</name>
          <file> 33</file>
        </tool>
        <tool>
          <name>ICC8051</name>
          <file> 59</file>
        </tool>
      </outputs>
      <inputs>
        <tool>
          <name>BICOMP</name>
          <file> 85 63 25 7 6 4 3 23 12 21 81 91 92 36 54</file>
        </tool>
        <tool>
          <name>ICC8051</name>
          <file> 85 63 25 7 6 4 3 23 12 21 81 91 92 36 54</file>
        </tool>
      </inputs>
    </file>
    <file>
      <name>$PROJ_DIR$\..\..\..\source\Components\utils\util_buffer.c</name>
      <outputs>
        <tool>
          <name>BICOMP</name>
          <file> 71</file>
        </tool>
        <tool>
          <name>ICC8051</name>
          <file> 55</file>
        </tool>
      </outputs>
      <inputs>
        <tool>
          <name>BICOMP</name>
          <file> 82 7 85 63 25 6 4 27 21</file>
        </tool>
        <tool>
          <name>ICC8051</name>
          <file> 82 7 85 63 25 6 4 27 21</file>
        </tool>
      </inputs>
    </file>
    <file>
      <name>$PROJ_DIR$\..\..\..\source\Components\targets\srf05_soc\hal_assert.c</name>
      <outputs>
        <tool>
          <name>BICOMP</name>
          <file> 39</file>
        </tool>
        <tool>
          <name>ICC8051</name>
          <file> 42</file>
        </tool>
      </outputs>
      <inputs>
        <tool>
          <name>BICOMP</name>
          <file> 21 6 16 7 4</file>
        </tool>
        <tool>
          <name>ICC8051</name>
          <file> 21 6 16 7 4</file>
        </tool>
      </inputs>
    </file>
    <file>
      <name>$PROJ_DIR$\..\..\..\source\Components\targets\srf05_soc\hal_board.c</name>
      <outputs>
        <tool>
          <name>BICOMP</name>
          <file> 62</file>
        </tool>
        <tool>
          <name>ICC8051</name>
          <file> 38</file>
        </tool>
      </outputs>
      <inputs>
        <tool>
          <name>BICOMP</name>
          <file> 7 6 14 27 4 85 63 25 3 23 11 43</file>
        </tool>
        <tool>
          <name>ICC8051</name>
          <file> 7 6 14 27 4 85 63 25 3 23 11 43</file>
        </tool>
      </inputs>
    </file>
    <file>
      <name>$PROJ_DIR$\..\..\..\source\Components\targets\srf05_soc\hal_button.c</name>
      <outputs>
        <tool>
          <name>BICOMP</name>
          <file> 52</file>
        </tool>
        <tool>
          <name>ICC8051</name>
          <file> 29</file>
        </tool>
      </outputs>
      <inputs>
        <tool>
          <name>BICOMP</name>
          <file> 7 85 63 25 6 4 12</file>
        </tool>
        <tool>
          <name>ICC8051</name>
          <file> 7 85 63 25 6 4 12</file>
        </tool>
      </inputs>
    </file>
    <file>
      <name>$PROJ_DIR$\..\..\..\source\Components\targets\srf05_soc\hal_joystick.c</name>
      <outputs>
        <tool>
          <name>BICOMP</name>
          <file> 69</file>
        </tool>
        <tool>
          <name>ICC8051</name>
          <file> 73</file>
        </tool>
      </outputs>
      <inputs>
        <tool>
          <name>BICOMP</name>
          <file> 23 7 85 63 25 6 4 17 14 27</file>
        </tool>
        <tool>
          <name>ICC8051</name>
          <file> 23 7 85 63 25 6 4 17 14 27</file>
        </tool>
      </inputs>
    </file>
    <file>
      <name>$PROJ_DIR$\..\..\..\source\Components\utils\util.c</name>
      <outputs>
        <tool>
          <name>BICOMP</name>
          <file> 72</file>
        </tool>
        <tool>
          <name>ICC8051</name>
          <file> 56</file>
        </tool>
      </outputs>
      <inputs>
        <tool>
          <name>BICOMP</name>
          <file> 91 7 6 92</file>
        </tool>
        <tool>
          <name>ICC8051</name>
          <file> 91 7 6 92</file>
        </tool>
      </inputs>
    </file>
    <file>
      <name>$PROJ_DIR$\..\..\..\source\Components\targets\srf05_soc\hal_digio.c</name>
      <outputs>
        <tool>
          <name>BICOMP</name>
          <file> 30</file>
        </tool>
        <tool>
          <name>ICC8051</name>
          <file> 58</file>
        </tool>
      </outputs>
      <inputs>
        <tool>
          <name>BICOMP</name>
          <file> 7 6 85 63 25 4 27 14</file>
        </tool>
        <tool>
          <name>ICC8051</name>
          <file> 7 6 85 63 25 4 27 14</file>
        </tool>
      </inputs>
    </file>
    <file>
      <name>$PROJ_DIR$\..\..\..\source\Components\targets\srf05_soc\hal_led.c</name>
      <outputs>
        <tool>
          <name>BICOMP</name>
          <file> 65</file>
        </tool>
        <tool>
          <name>ICC8051</name>
          <file> 34</file>
        </tool>
      </outputs>
      <inputs>
        <tool>
          <name>BICOMP</name>
          <file> 7 16 85 63 25 6 4</file>
        </tool>
        <tool>
          <name>ICC8051</name>
          <file> 7 16 85 63 25 6 4</file>
        </tool>
      </inputs>
    </file>
    <forcedrebuild>
      <name>[MULTI_TOOL]</name>
      <tool>XLINK</tool>
    </forcedrebuild>
  </configuration>
</project>


