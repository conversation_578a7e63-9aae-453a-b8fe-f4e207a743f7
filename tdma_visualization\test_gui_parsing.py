#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试GUI可视化系统的数据解析功能
"""

import tkinter as tk
from tkinter import ttk
import threading
import time
import queue
import sys
import os

# 添加当前目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

class TestGUIParser:
    def __init__(self, root):
        self.root = root
        self.root.title("GUI数据解析测试")
        self.root.geometry("800x600")
        
        # 数据队列
        self.data_queue = queue.Queue()
        
        # 创建界面
        self.create_widgets()
        
        # 启动数据处理
        self.start_data_processing()
        
        # 启动模拟数据
        self.start_demo_data()
    
    def create_widgets(self):
        """创建界面组件"""
        # 主框架
        main_frame = ttk.Frame(self.root)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # 标题
        title_label = ttk.Label(main_frame, text="GUI数据解析测试", font=("Arial", 16, "bold"))
        title_label.pack(pady=10)
        
        # 数据表格（移除同步列）
        columns = ("时间", "节点", "温度", "振动", "时隙", "格式")
        self.tree = ttk.Treeview(main_frame, columns=columns, show="headings", height=15)
        
        # 设置列标题
        for col in columns:
            self.tree.heading(col, text=col)
            self.tree.column(col, width=100)
        
        # 滚动条
        scrollbar = ttk.Scrollbar(main_frame, orient=tk.VERTICAL, command=self.tree.yview)
        self.tree.configure(yscrollcommand=scrollbar.set)
        
        # 打包表格和滚动条
        tree_frame = ttk.Frame(main_frame)
        tree_frame.pack(fill=tk.BOTH, expand=True, pady=10)
        
        self.tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # 日志区域
        log_frame = ttk.LabelFrame(main_frame, text="解析日志")
        log_frame.pack(fill=tk.BOTH, expand=True, pady=10)
        
        self.log_text = tk.Text(log_frame, height=8)
        log_scrollbar = ttk.Scrollbar(log_frame, orient=tk.VERTICAL, command=self.log_text.yview)
        self.log_text.configure(yscrollcommand=log_scrollbar.set)
        
        self.log_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        log_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # 控制按钮
        button_frame = ttk.Frame(main_frame)
        button_frame.pack(fill=tk.X, pady=10)
        
        clear_btn = ttk.Button(button_frame, text="清除数据", command=self.clear_data)
        clear_btn.pack(side=tk.LEFT, padx=5)
        
        test_btn = ttk.Button(button_frame, text="发送测试数据", command=self.send_test_data)
        test_btn.pack(side=tk.LEFT, padx=5)
    
    def start_data_processing(self):
        """启动数据处理线程"""
        def process_data():
            while True:
                try:
                    if not self.data_queue.empty():
                        data_type, line = self.data_queue.get_nowait()
                        self.root.after(0, lambda: self.parse_and_display_data(line, data_type))
                    time.sleep(0.1)
                except queue.Empty:
                    pass
                except Exception as e:
                    print(f"数据处理错误: {e}")
        
        thread = threading.Thread(target=process_data, daemon=True)
        thread.start()
    
    def start_demo_data(self):
        """启动模拟数据"""
        def generate_demo_data():
            demo_lines = [
                "[SERIAL] Node 2: Temp=25.0, Vib=0,Slot=1",
                "Node 1: Temp=25.6, Vib=0, Sync=1, Slot=1",
                "Node 3: Temp=23.5, Vib=1, Slot=2",
                "MASTER: Received from Node 4 - Temp=298, Vib=0",
                "[SERIAL] Node 5: Temp=26.2, Vib=0,Slot=3",
            ]
            
            counter = 0
            while True:
                line = demo_lines[counter % len(demo_lines)]
                # 修改温度值使其变化
                import re
                temp_match = re.search(r'Temp=([0-9.]+)', line)
                if temp_match:
                    base_temp = float(temp_match.group(1))
                    new_temp = base_temp + (counter % 10) * 0.1
                    line = line.replace(f"Temp={temp_match.group(1)}", f"Temp={new_temp:.1f}")
                
                self.data_queue.put(('demo', line))
                counter += 1
                time.sleep(2)  # 每2秒发送一次数据
        
        thread = threading.Thread(target=generate_demo_data, daemon=True)
        thread.start()
    
    def send_test_data(self):
        """发送测试数据"""
        test_lines = [
            "[SERIAL] Node 2: Temp=25.0, Vib=0,Slot=1",
            "Node 1: Temp=25.6, Vib=0, Sync=1, Slot=1", 
            "Node 3: Temp=23.5, Vib=1, Slot=2",
            "Invalid data line",
        ]
        
        for line in test_lines:
            self.data_queue.put(('test', line))
    
    def parse_and_display_data(self, line, data_type=""):
        """解析并显示数据 - 使用与enhanced_monitor相同的逻辑"""
        import re
        from datetime import datetime
        
        # 格式1: 标准格式 "Node 1: Temp=25.6, Vib=0, Sync=1, Slot=1"
        sensor_pattern1 = r'Node\s+(\d+).*?Temp=([0-9.]+).*?Vib=(\d+).*?Sync=(\d+).*?Slot=(\d+)'
        match1 = re.search(sensor_pattern1, line, re.IGNORECASE)

        # 格式2: 标准格式 "Node 1: Temp=25.0, Vib=0, Sync=1, Slot=1" (新的TDMA输出格式)
        sensor_pattern2 = r'Node\s+(\d+):\s*Temp=([0-9.]+),\s*Vib=(\d+),\s*Sync=(\d+),\s*Slot=(\d+)'
        match2 = re.search(sensor_pattern2, line, re.IGNORECASE)

        # 格式3: 新格式 "Node 2: Temp=25.0, Vib=0,Slot=1" (缺少Sync字段)
        sensor_pattern3 = r'Node\s+(\d+):\s*Temp=([0-9.]+),\s*Vib=(\d+),\s*Slot=(\d+)'
        match3 = re.search(sensor_pattern3, line, re.IGNORECASE)

        # 格式4: 旧版MASTER接收格式 "MASTER: Received from Node 3 - Temp=298, Vib=0" (兼容性)
        sensor_pattern4 = r'MASTER:\s*Received from Node\s+(\d+).*?Temp=([0-9.]+).*?Vib=(\d+)'
        match4 = re.search(sensor_pattern4, line, re.IGNORECASE)

        # 初始化变量
        match = None
        node_id = None
        temperature = None
        vibration = None
        sync_status = 1  # 默认同步状态
        slot_number = 0  # 默认时隙
        format_type = "unknown"

        if match1:
            match = match1
            node_id = int(match.group(1))
            temperature = float(match.group(2))
            vibration = int(match.group(3))
            sync_status = int(match.group(4))
            slot_number = int(match.group(5))
            format_type = "standard_format1"
        elif match2:
            match = match2
            node_id = int(match.group(1))
            temperature = float(match.group(2))  # 已经是正确的小数格式
            vibration = int(match.group(3))
            sync_status = int(match.group(4))  # 从数据中获取同步状态
            slot_number = int(match.group(5))  # 从数据中获取时隙号
            format_type = "standard_format2"
        elif match3:
            match = match3
            node_id = int(match.group(1))
            temperature = float(match.group(2))  # 新格式已经是正确的小数格式
            vibration = int(match.group(3))
            sync_status = 1  # 新格式默认认为已同步（能收到数据说明已同步）
            slot_number = int(match.group(4))  # 从数据中获取时隙号
            format_type = "new_format"
        elif match4:
            match = match4
            node_id = int(match.group(1))
            temperature = float(match.group(2)) / 10.0  # 298 -> 29.8°C (旧格式转换)
            vibration = int(match.group(3))
            sync_status = 1  # MASTER收到数据说明已同步
            slot_number = node_id  # 使用节点ID作为时隙号
            format_type = "legacy_master"

        if match and node_id is not None:
            timestamp = datetime.now()
            
            # 更新表格显示（移除同步状态显示）
            time_str = timestamp.strftime("%H:%M:%S")
            
            # 限制表格行数
            children = self.tree.get_children()
            if len(children) >= 20:
                self.tree.delete(children[0])

            item = self.tree.insert('', 'end', values=(
                time_str, f"节点{node_id}", f"{temperature:.1f}", vibration, slot_number, format_type
            ))
            
            # 滚动到最新数据
            self.tree.see(item)
            
            # 添加解析日志
            self.add_log(f"✓ 解析成功: {line} -> 节点{node_id}, 温度{temperature}°C, 振动{vibration}, 时隙{slot_number}")
        else:
            # 解析失败
            self.add_log(f"✗ 解析失败: {line}")
    
    def add_log(self, message):
        """添加日志"""
        timestamp = time.strftime("%H:%M:%S")
        log_message = f"[{timestamp}] {message}\n"
        
        self.log_text.insert(tk.END, log_message)
        self.log_text.see(tk.END)
        
        # 限制日志行数
        lines = self.log_text.get("1.0", tk.END).split('\n')
        if len(lines) > 50:
            self.log_text.delete("1.0", "10.0")
    
    def clear_data(self):
        """清除数据"""
        # 清除表格
        for item in self.tree.get_children():
            self.tree.delete(item)
        
        # 清除日志
        self.log_text.delete("1.0", tk.END)
        
        self.add_log("数据已清除")

def main():
    root = tk.Tk()
    app = TestGUIParser(root)
    
    def on_closing():
        root.quit()
        root.destroy()
    
    root.protocol("WM_DELETE_WINDOW", on_closing)
    root.mainloop()

if __name__ == "__main__":
    main()
