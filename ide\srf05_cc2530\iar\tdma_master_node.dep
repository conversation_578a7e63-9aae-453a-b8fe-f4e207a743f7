<?xml version="1.0" encoding="iso-8859-1"?>

<project>
  <fileVersion>2</fileVersion>
  <fileChecksum>2193485278</fileChecksum>
  <configuration>
    <name>srf05_cc2530</name>
    <outputs>
      <file>$TOOLKIT_DIR$\lib\clib\cl-pli-blxd-1e16x01.r51</file>
      <file>$PROJ_DIR$\temp\tdma_master_node\util_buffer.r51</file>
      <file>$PROJ_DIR$\temp\tdma_master_node\clock.r51</file>
      <file>$PROJ_DIR$\temp\tdma_master_node\hal_int.pbi</file>
      <file>$PROJ_DIR$\tdma_master_node.d51</file>
      <file>$PROJ_DIR$\temp\tdma_master_node\hal_rf.r51</file>
      <file>$PROJ_DIR$\temp\tdma_master_node\hal_digio.pbi</file>
      <file>$PROJ_DIR$\temp\tdma_master_node\util_buffer.pbi</file>
      <file>$PROJ_DIR$\temp\tdma_master_node\hal_board.r51</file>
      <file>$PROJ_DIR$\temp\tdma_master_node\hal_joystick.pbi</file>
      <file>$TOOLKIT_DIR$\config\devices\Texas Instruments\lnk51ew_cc2530F256_banked.xcl</file>
      <file>$PROJ_DIR$\..\..\..\source\Components\basicrf\basic_rf.c</file>
      <file>$PROJ_DIR$\temp\tdma_master_node\hal_rf.pbi</file>
      <file>$PROJ_DIR$\..\..\..\source\apps\tdma_sensor_network\tdma_master_node.c</file>
      <file>$PROJ_DIR$\..\..\..\source\Components\targets\interface\hal_assert.h</file>
      <file>$PROJ_DIR$\..\..\..\source\Components\targets\srf05_soc\hal_led.c</file>
      <file>$TOOLKIT_DIR$\inc\clib\string.h</file>
      <file>$PROJ_DIR$\..\..\..\source\Components\targets\interface\hal_joystick.h</file>
      <file>$PROJ_DIR$\..\..\..\source\Components\targets\interface\hal_int.h</file>
      <file>$PROJ_DIR$\..\..\..\source\Components\radios\cc2530\hal_rf.c</file>
      <file>$PROJ_DIR$\..\..\..\source\Components\targets\srf05_soc\hal_assert.c</file>
      <file>$PROJ_DIR$\..\..\..\source\Components\common\hal_defs.h</file>
      <file>$PROJ_DIR$\..\..\..\source\Components\radios\cc2530\adc.c</file>
      <file>$PROJ_DIR$\..\..\..\source\Components\radios\cc2530\clock.h</file>
      <file>$PROJ_DIR$\..\..\..\source\Components\common\cc8051\hal_cc8051.h</file>
      <file>$PROJ_DIR$\..\..\..\source\Components\targets\interface\hal_led.h</file>
      <file>$PROJ_DIR$\..\..\..\source\Components\basicrf\basic_rf.h</file>
      <file>$PROJ_DIR$\..\..\..\source\Components\common\hal_uart.c</file>
      <file>$PROJ_DIR$\..\..\..\source\Components\common\hal_int.c</file>
      <file>$PROJ_DIR$\..\..\..\source\Components\targets\interface\hal_lcd.h</file>
      <file>$PROJ_DIR$\..\..\..\source\Components\targets\interface\hal_mcu.h</file>
      <file>$PROJ_DIR$\..\..\..\source\Components\targets\interface\hal_rf.h</file>
      <file>$PROJ_DIR$\..\..\..\source\Components\targets\interface\hal_button.h</file>
      <file>$PROJ_DIR$\..\..\..\source\Components\radios\cc2530\adc.h</file>
      <file>$PROJ_DIR$\tdma_master_node.hex</file>
      <file>$PROJ_DIR$\..\..\..\source\Components\targets\srf05_soc\hal_board.h</file>
      <file>$PROJ_DIR$\..\..\..\source\Components\targets\srf05_soc\hal_digio.c</file>
      <file>$PROJ_DIR$\..\..\..\source\Components\targets\srf05_soc\hal_board.c</file>
      <file>$TOOLKIT_DIR$\inc\clib\sysmac.h</file>
      <file>$PROJ_DIR$\..\..\..\source\Components\targets\srf05_soc\hal_joystick.c</file>
      <file>$PROJ_DIR$\..\..\..\source\Components\common\hal_uart.h</file>
      <file>$PROJ_DIR$\..\..\..\source\Components\radios\cc2530\clock.c</file>
      <file>$PROJ_DIR$\..\..\..\source\Components\radios\cc2530\hal_mcu.c</file>
      <file>$PROJ_DIR$\..\..\..\source\Components\targets\srf05_soc\hal_button.c</file>
      <file>$PROJ_DIR$\temp\tdma_master_node\hal_assert.r51</file>
      <file>$PROJ_DIR$\..\..\..\source\Components\utils\util.h</file>
      <file>$PROJ_DIR$\..\..\..\source\Components\utils\util_buffer.c</file>
      <file>$PROJ_DIR$\..\..\..\source\Components\utils\util_lcd.c</file>
      <file>$PROJ_DIR$\..\..\..\source\Components\utils\util.c</file>
      <file>$PROJ_DIR$\..\..\..\source\Components\utils\util_buffer.h</file>
      <file>$PROJ_DIR$\..\..\..\source\Components\utils\util_lcd.h</file>
      <file>$PROJ_DIR$\temp\tdma_master_node\basic_rf.pbi</file>
      <file>$PROJ_DIR$\temp\tdma_master_node\hal_int.r51</file>
      <file>$PROJ_DIR$\temp\tdma_master_node\hal_mcu.r51</file>
      <file>$PROJ_DIR$\..\..\..\source\Components\common\hal_types.h</file>
      <file>$PROJ_DIR$\temp\tdma_master_node\util_lcd.r51</file>
      <file>$PROJ_DIR$\temp\tdma_master_node\hal_uart.pbi</file>
      <file>$PROJ_DIR$\temp\tdma_master_node\hal_uart.r51</file>
      <file>$TOOLKIT_DIR$\inc\ioCC2530.h</file>
      <file>$PROJ_DIR$\temp\tdma_master_node\hal_assert.pbi</file>
      <file>$PROJ_DIR$\temp\tdma_master_node\util.r51</file>
      <file>$PROJ_DIR$\temp\tdma_master_node\basic_rf.r51</file>
      <file>$PROJ_DIR$\temp\tdma_master_node\hal_mcu.pbi</file>
      <file>$PROJ_DIR$\temp\tdma_master_node\adc.pbi</file>
      <file>$PROJ_DIR$\temp\tdma_master_node\tdma_master_node.pbd</file>
      <file>$PROJ_DIR$\temp\tdma_master_node\hal_digio.r51</file>
      <file>$PROJ_DIR$\temp\tdma_master_node\adc.r51</file>
      <file>$PROJ_DIR$\temp\tdma_master_node\tdma_master_node.r51</file>
      <file>$PROJ_DIR$\temp\tdma_master_node\util_lcd.pbi</file>
      <file>$PROJ_DIR$\temp\tdma_master_node\hal_button.r51</file>
      <file>$PROJ_DIR$\temp\tdma_master_node\tdma_master_node.pbi</file>
      <file>$PROJ_DIR$\temp\tdma_master_node\util.pbi</file>
      <file>$PROJ_DIR$\temp\tdma_master_node\hal_led.r51</file>
      <file>$PROJ_DIR$\temp\tdma_master_node\hal_board.pbi</file>
      <file>$PROJ_DIR$\temp\tdma_master_node\clock.pbi</file>
      <file>$PROJ_DIR$\temp\tdma_master_node\hal_led.pbi</file>
      <file>$PROJ_DIR$\temp\tdma_master_node\hal_button.pbi</file>
      <file>$PROJ_DIR$\temp\tdma_master_node.map</file>
      <file>$PROJ_DIR$\temp\tdma_master_node\hal_joystick.r51</file>
      <file>$PROJ_DIR$\..\..\..\source\Components\targets\interface\hal_digio.h</file>
    </outputs>
    <file>
      <name>[ROOT_NODE]</name>
      <outputs>
        <tool>
          <name>XLINK</name>
          <file> 77 34 4</file>
        </tool>
      </outputs>
    </file>
    <file>
      <name>$PROJ_DIR$\tdma_master_node.d51</name>
      <outputs>
        <tool>
          <name>XLINK</name>
          <file> 77 34</file>
        </tool>
      </outputs>
      <inputs>
        <tool>
          <name>XLINK</name>
          <file> 10 66 61 2 44 8 69 65 52 78 72 53 5 57 67 60 1 55 0</file>
        </tool>
      </inputs>
    </file>
    <file>
      <name>$PROJ_DIR$\..\..\..\source\Components\basicrf\basic_rf.c</name>
      <outputs>
        <tool>
          <name>BICOMP</name>
          <file> 51</file>
        </tool>
        <tool>
          <name>ICC8051</name>
          <file> 61</file>
        </tool>
      </outputs>
      <inputs>
        <tool>
          <name>BICOMP</name>
          <file> 18 54 30 31 26 21 45 16 38</file>
        </tool>
        <tool>
          <name>ICC8051</name>
          <file> 18 54 30 31 26 21 45 16 38</file>
        </tool>
      </inputs>
    </file>
    <file>
      <name>$PROJ_DIR$\..\..\..\source\apps\tdma_sensor_network\tdma_master_node.c</name>
      <outputs>
        <tool>
          <name>BICOMP</name>
          <file> 70</file>
        </tool>
      </outputs>
      <inputs>
        <tool>
          <name>BICOMP</name>
          <file> 33 54 21 58 26 23 32 30 31 40 50 29 45 14 35 24 18 17 25</file>
        </tool>
        <tool>
          <name>ICC8051</name>
          <file> 33 54 21 58 26 23 32 30 31 40 50 29 45 14 35 24 18 17 25</file>
        </tool>
      </inputs>
    </file>
    <file>
      <name>$PROJ_DIR$\..\..\..\source\Components\targets\srf05_soc\hal_led.c</name>
      <outputs>
        <tool>
          <name>BICOMP</name>
          <file> 75</file>
        </tool>
        <tool>
          <name>ICC8051</name>
          <file> 72</file>
        </tool>
      </outputs>
      <inputs>
        <tool>
          <name>BICOMP</name>
          <file> 54 25 35 58 24 21 30</file>
        </tool>
        <tool>
          <name>ICC8051</name>
          <file> 54 25 35 58 24 21 30</file>
        </tool>
      </inputs>
    </file>
    <file>
      <name>$PROJ_DIR$\..\..\..\source\Components\radios\cc2530\hal_rf.c</name>
      <outputs>
        <tool>
          <name>BICOMP</name>
          <file> 12</file>
        </tool>
        <tool>
          <name>ICC8051</name>
          <file> 5</file>
        </tool>
      </outputs>
      <inputs>
        <tool>
          <name>BICOMP</name>
          <file> 35 58 24 54 21 30 18 14 31 45</file>
        </tool>
        <tool>
          <name>ICC8051</name>
          <file> 35 58 24 54 21 30 18 14 31 45</file>
        </tool>
      </inputs>
    </file>
    <file>
      <name>$PROJ_DIR$\..\..\..\source\Components\targets\srf05_soc\hal_assert.c</name>
      <outputs>
        <tool>
          <name>BICOMP</name>
          <file> 59</file>
        </tool>
        <tool>
          <name>ICC8051</name>
          <file> 44</file>
        </tool>
      </outputs>
      <inputs>
        <tool>
          <name>BICOMP</name>
          <file> 14 21 25 54 30</file>
        </tool>
        <tool>
          <name>ICC8051</name>
          <file> 14 21 25 54 30</file>
        </tool>
      </inputs>
    </file>
    <file>
      <name>$PROJ_DIR$\..\..\..\source\Components\radios\cc2530\adc.c</name>
      <outputs>
        <tool>
          <name>BICOMP</name>
          <file> 63</file>
        </tool>
        <tool>
          <name>ICC8051</name>
          <file> 66</file>
        </tool>
      </outputs>
      <inputs>
        <tool>
          <name>BICOMP</name>
          <file> 33 54 21 58</file>
        </tool>
        <tool>
          <name>ICC8051</name>
          <file> 33 54 21 58</file>
        </tool>
      </inputs>
    </file>
    <file>
      <name>$PROJ_DIR$\..\..\..\source\Components\common\hal_uart.c</name>
      <outputs>
        <tool>
          <name>BICOMP</name>
          <file> 56</file>
        </tool>
        <tool>
          <name>ICC8051</name>
          <file> 57</file>
        </tool>
      </outputs>
      <inputs>
        <tool>
          <name>BICOMP</name>
          <file> 21 35 58 24 54 30 40</file>
        </tool>
        <tool>
          <name>ICC8051</name>
          <file> 21 35 58 24 54 30 40</file>
        </tool>
      </inputs>
    </file>
    <file>
      <name>$PROJ_DIR$\..\..\..\source\Components\common\hal_int.c</name>
      <outputs>
        <tool>
          <name>BICOMP</name>
          <file> 3</file>
        </tool>
        <tool>
          <name>ICC8051</name>
          <file> 52</file>
        </tool>
      </outputs>
      <inputs>
        <tool>
          <name>BICOMP</name>
          <file> 54 21 18 35 58 24 30</file>
        </tool>
        <tool>
          <name>ICC8051</name>
          <file> 54 21 18 35 58 24 30</file>
        </tool>
      </inputs>
    </file>
    <file>
      <name>$PROJ_DIR$\..\..\..\source\Components\targets\srf05_soc\hal_digio.c</name>
      <outputs>
        <tool>
          <name>BICOMP</name>
          <file> 6</file>
        </tool>
        <tool>
          <name>ICC8051</name>
          <file> 65</file>
        </tool>
      </outputs>
      <inputs>
        <tool>
          <name>BICOMP</name>
          <file> 54 21 35 58 24 30 18 79</file>
        </tool>
        <tool>
          <name>ICC8051</name>
          <file> 54 21 35 58 24 30 18 79</file>
        </tool>
      </inputs>
    </file>
    <file>
      <name>$PROJ_DIR$\..\..\..\source\Components\targets\srf05_soc\hal_board.c</name>
      <outputs>
        <tool>
          <name>BICOMP</name>
          <file> 73</file>
        </tool>
        <tool>
          <name>ICC8051</name>
          <file> 8</file>
        </tool>
      </outputs>
      <inputs>
        <tool>
          <name>BICOMP</name>
          <file> 54 21 79 18 30 35 58 24 29 17 23 40</file>
        </tool>
        <tool>
          <name>ICC8051</name>
          <file> 54 21 79 18 30 35 58 24 29 17 23 40</file>
        </tool>
      </inputs>
    </file>
    <file>
      <name>$PROJ_DIR$\..\..\..\source\Components\targets\srf05_soc\hal_joystick.c</name>
      <outputs>
        <tool>
          <name>BICOMP</name>
          <file> 9</file>
        </tool>
        <tool>
          <name>ICC8051</name>
          <file> 78</file>
        </tool>
      </outputs>
      <inputs>
        <tool>
          <name>BICOMP</name>
          <file> 17 54 35 58 24 21 30 33 79 18</file>
        </tool>
        <tool>
          <name>ICC8051</name>
          <file> 17 54 35 58 24 21 30 33 79 18</file>
        </tool>
      </inputs>
    </file>
    <file>
      <name>$PROJ_DIR$\..\..\..\source\Components\radios\cc2530\clock.c</name>
      <outputs>
        <tool>
          <name>BICOMP</name>
          <file> 74</file>
        </tool>
        <tool>
          <name>ICC8051</name>
          <file> 2</file>
        </tool>
      </outputs>
      <inputs>
        <tool>
          <name>BICOMP</name>
          <file> 23 54 21 58 30</file>
        </tool>
        <tool>
          <name>ICC8051</name>
          <file> 23 54 21 58 30</file>
        </tool>
      </inputs>
    </file>
    <file>
      <name>$PROJ_DIR$\..\..\..\source\Components\radios\cc2530\hal_mcu.c</name>
      <outputs>
        <tool>
          <name>BICOMP</name>
          <file> 62</file>
        </tool>
        <tool>
          <name>ICC8051</name>
          <file> 53</file>
        </tool>
      </outputs>
      <inputs>
        <tool>
          <name>BICOMP</name>
          <file> 54 30 23 21 58 14</file>
        </tool>
        <tool>
          <name>ICC8051</name>
          <file> 54 30 23 21 58 14</file>
        </tool>
      </inputs>
    </file>
    <file>
      <name>$PROJ_DIR$\..\..\..\source\Components\targets\srf05_soc\hal_button.c</name>
      <outputs>
        <tool>
          <name>BICOMP</name>
          <file> 76</file>
        </tool>
        <tool>
          <name>ICC8051</name>
          <file> 69</file>
        </tool>
      </outputs>
      <inputs>
        <tool>
          <name>BICOMP</name>
          <file> 54 35 58 24 21 30 32</file>
        </tool>
        <tool>
          <name>ICC8051</name>
          <file> 54 35 58 24 21 30 32</file>
        </tool>
      </inputs>
    </file>
    <file>
      <name>$PROJ_DIR$\..\..\..\source\Components\utils\util_buffer.c</name>
      <outputs>
        <tool>
          <name>BICOMP</name>
          <file> 7</file>
        </tool>
        <tool>
          <name>ICC8051</name>
          <file> 1</file>
        </tool>
      </outputs>
      <inputs>
        <tool>
          <name>BICOMP</name>
          <file> 49 54 35 58 24 21 30 18 14</file>
        </tool>
        <tool>
          <name>ICC8051</name>
          <file> 49 54 35 58 24 21 30 18 14</file>
        </tool>
      </inputs>
    </file>
    <file>
      <name>$PROJ_DIR$\..\..\..\source\Components\utils\util_lcd.c</name>
      <outputs>
        <tool>
          <name>BICOMP</name>
          <file> 68</file>
        </tool>
        <tool>
          <name>ICC8051</name>
          <file> 55</file>
        </tool>
      </outputs>
      <inputs>
        <tool>
          <name>BICOMP</name>
          <file> 35 58 24 54 21 30 29 17 32 14 50 45 31 16 38</file>
        </tool>
        <tool>
          <name>ICC8051</name>
          <file> 35 58 24 54 21 30 29 17 32 14 50 45 31 16 38</file>
        </tool>
      </inputs>
    </file>
    <file>
      <name>$PROJ_DIR$\..\..\..\source\Components\utils\util.c</name>
      <outputs>
        <tool>
          <name>BICOMP</name>
          <file> 71</file>
        </tool>
        <tool>
          <name>ICC8051</name>
          <file> 60</file>
        </tool>
      </outputs>
      <inputs>
        <tool>
          <name>BICOMP</name>
          <file> 45 54 21 31</file>
        </tool>
        <tool>
          <name>ICC8051</name>
          <file> 45 54 21 31</file>
        </tool>
      </inputs>
    </file>
    <file>
      <name>$PROJ_DIR$\temp\tdma_master_node\tdma_master_node.pbd</name>
      <inputs>
        <tool>
          <name>BILINK</name>
          <file> 63 51 74 59 73 76 6 3 9 75 62 12 56 70 71 7 68</file>
        </tool>
      </inputs>
    </file>
  </configuration>
</project>


