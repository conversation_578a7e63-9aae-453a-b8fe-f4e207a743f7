本系统采用TDMA算法，采用CC2530将多个传感器采集的数据传输到主节点，主节点完成数据接收并发送到串口，管理软件实现传感器数据的显示、曲线绘制等。管理软件实现对指定从机数据发送（通过TDMA方式），实现LED的开启、关闭等。
基本功能要求：
（1）实现TDMA算法的物理层、MAC及路由传输协议设计；
（2）采用2种以上传感器或1个控制器进行系统验证；
（3）实现数据采集和显示，系统演示网络规模（从节点数）：>=3；
（4）提交原理图、设计图、系统代码及报告（开题报告、中期检查、课程设计报告）。
发挥部分要求：
（1）从节点数和时隙可根据系统配置；
（2）采集数据可视化、可分析和统计；
（3）TDMA算法可优化、可移植等。
 设备：CC2530开发板x4（从机数：3，主机数：1），采集对象：温度数据（DTH11）x1、震动传感器x1，控制器：LEDx1（板载），
震动模拟器（用于模拟震动数据）x1,


参考资料：
（1）震动传感器，电源：5V，输出:模拟量
https://pan.baidu.com/s/1-_7hboA6fYK1IaAS73p3lg 提取码: qiew
（2）震动马达，高低电平触发
链接: https://pan.baidu.com/s/1biZZzaDxllPeSaemzTE22Q 提取码: d28e





**开发环境确认：**
- 用户已具备IAR Embedded Workbench for 8051、CC Debugger和SmartRF Flash Programmer
- 项目中已有现成的HAL驱动代码基础
- 用户将自行负责代码编译、烧录和硬件测试

**核心功能要求：**
- 实现基于CC2530的主从节点通信系统（1主节点 + 3从节点）
- 主节点：数据汇聚、串口通信、网络协调
- 从节点：传感器数据采集（温度DTH11、震动传感器）、LED控制
- 实现TDMA时分多址协议进行无线通信

**技术实现细节：**
- TDMA参数（时隙长度、同步精度等）由开发者合理设计
- 数据传输实时性要求由开发者根据应用场景确定
- 暂不需要数据加密，重点关注基本通信功能
- 网络规模固定为4个节点，但代码架构应支持后续扩展

**开发约束：**
- 严禁运行任何代码或启动编译过程
- 只提供完整的源代码文件和配置文件
- 所有功能验证通过用户在IAR环境中编译烧录完成
- 代码应包含详细注释，便于用户理解和调试

**交付要求：**
- 提供可直接在IAR中编译的完整项目源码
- 包含主节点和从节点的不同配置版本
- 提供清晰的代码结构和模块划分
- 确保代码的可读性和可维护性


网络规模按照要求来，但是我目前只有一主一从，两个节点设备。
震动传感器型号是SW-520D。
请基于现有的项目文档和代码基础，开发出满足上述要求的完整实现方案。


---



CC2530 BasicRF/
├── source/apps/
│   ├── light_switch/light_switch.c          # 修改后的TDMA版本
│   └── tdma_sensor_network/
│       ├── tdma_master_node.c               # 主节点程序
│       └── tdma_slave_node.c                # 从节点程序
├── ide/
│   ├── cc2530_sw_examples.eww              # 更新的工作空间
│   └── srf05_cc2530/iar/
│       ├── tdma_master_node.ewp            # 主节点项目配置
│       └── tdma_slave_node.ewp             # 从节点项目配置
├── 项目要求.md                             # 原始需求
└── TDMA传感器网络系统说明.md               # 完整说明文档










---
报错


Error[e46]: Undefined external "min::?relay" referred in basic_rf ( D:\Desktop\实验1\任务3.1  无线开关LED灯\CC2530 BasicRF\ide\srf05_cc2530\iar\temp\tdma_master_node\ 
basic_rf.r51 ) 
Error while running Linker 

------


找到问题了！tdmaWaitForSlot 函数在等待 currentSlot 变为指定值，但是 currentSlot 只有在定时器中断中才会更新。如果定时器没有正确启动，currentSlot 永远不会改变，导致无限等待。

---
从节点也被 tdmaWaitForSlot() 函数卡住了
为什么会被卡住？这个在tdma中是不是重要的结构？

---
使用软件TDMA（基于循环计数器）而不是硬件定时器：

---
硬件定时器，可以参考

文件实现，同时用LED2的状态来检验硬件定时是否正确


硬件定时同步时隙，
