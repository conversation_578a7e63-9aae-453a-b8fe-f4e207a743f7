<?xml version="1.0" encoding="iso-8859-1"?>

<project>
  <fileVersion>2</fileVersion>
  <fileChecksum>4103724155</fileChecksum>
  <configuration>
    <name>srf05_cc2530</name>
    <outputs>
      <file>$PROJ_DIR$\..\..\..\source\Components\targets\interface\hal_mcu.h</file>
      <file>$PROJ_DIR$\..\..\..\source\Components\targets\interface\hal_int.h</file>
      <file>$PROJ_DIR$\..\..\..\source\Components\targets\interface\hal_timer_32k.h</file>
      <file>$PROJ_DIR$\..\..\..\source\Components\radios\cc2530\adc.c</file>
      <file>$PROJ_DIR$\temp\per_test\per_test_menu.r51</file>
      <file>$PROJ_DIR$\..\..\..\source\Components\targets\interface\hal_button.h</file>
      <file>$PROJ_DIR$\..\..\..\source\Components\targets\interface\hal_joystick.h</file>
      <file>$PROJ_DIR$\..\..\..\source\Components\targets\interface\hal_lcd.h</file>
      <file>$PROJ_DIR$\..\..\..\source\Components\targets\interface\hal_digio.h</file>
      <file>$PROJ_DIR$\..\..\..\source\Components\targets\interface\hal_led.h</file>
      <file>$PROJ_DIR$\..\..\..\source\Components\common\hal_defs.h</file>
      <file>$PROJ_DIR$\..\..\..\source\Components\common\hal_int.c</file>
      <file>$PROJ_DIR$\..\..\..\source\Components\targets\common\hal_lcd_srf05.c</file>
      <file>$PROJ_DIR$\..\..\..\source\apps\per_test\per_test.h</file>
      <file>$PROJ_DIR$\..\..\..\source\Components\targets\interface\hal_assert.h</file>
      <file>$PROJ_DIR$\temp\per_test\hal_lcd_srf05.pbi</file>
      <file>$PROJ_DIR$\..\..\..\source\apps\per_test\per_test.c</file>
      <file>$PROJ_DIR$\temp\per_test\hal_digio.r51</file>
      <file>$PROJ_DIR$\temp\per_test\hal_joystick.pbi</file>
      <file>$PROJ_DIR$\temp\per_test\hal_led.pbi</file>
      <file>$PROJ_DIR$\..\..\..\source\Components\basicrf\basic_rf.c</file>
      <file>$PROJ_DIR$\..\..\..\source\Components\basicrf\basic_rf.h</file>
      <file>$PROJ_DIR$\..\..\..\source\Components\common\hal_types.h</file>
      <file>$PROJ_DIR$\..\..\..\source\apps\per_test\per_test_menu.h</file>
      <file>$PROJ_DIR$\temp\per_test\per_test.pbd</file>
      <file>$PROJ_DIR$\temp\per_test\hal_int.pbi</file>
      <file>$PROJ_DIR$\..\..\..\source\Components\common\cc8051\hal_cc8051.h</file>
      <file>$PROJ_DIR$\temp\per_test\hal_board.pbi</file>
      <file>$PROJ_DIR$\..\..\..\source\apps\per_test\per_test_menu.c</file>
      <file>$PROJ_DIR$\temp\per_test\hal_assert.pbi</file>
      <file>$PROJ_DIR$\temp\per_test\util_lcd.r51</file>
      <file>$PROJ_DIR$\temp\per_test\hal_button.pbi</file>
      <file>$PROJ_DIR$\temp\per_test\util.pbi</file>
      <file>$PROJ_DIR$\temp\per_test\util_lcd.pbi</file>
      <file>$PROJ_DIR$\per_test.d51</file>
      <file>$PROJ_DIR$\temp\per_test\hal_lcd_srf05.r51</file>
      <file>$PROJ_DIR$\temp\per_test\hal_timer_32k.r51</file>
      <file>$PROJ_DIR$\temp\per_test\adc.r51</file>
      <file>$PROJ_DIR$\temp\per_test\util_buffer.r51</file>
      <file>$TOOLKIT_DIR$\inc\clib\sysmac.h</file>
      <file>$PROJ_DIR$\temp\per_test\adc.pbi</file>
      <file>$PROJ_DIR$\temp\per_test\per_test.pbi</file>
      <file>$PROJ_DIR$\temp\per_test\hal_assert.r51</file>
      <file>$PROJ_DIR$\temp\per_test\hal_digio.pbi</file>
      <file>$PROJ_DIR$\..\..\..\source\Components\common\hal_uart.h</file>
      <file>$PROJ_DIR$\temp\per_test\hal_led.r51</file>
      <file>$PROJ_DIR$\temp\per_test\per_test_menu.pbi</file>
      <file>$PROJ_DIR$\temp\per_test\clock.pbi</file>
      <file>$PROJ_DIR$\temp\per_test\hal_timer_32k.pbi</file>
      <file>$PROJ_DIR$\temp\per_test\basic_rf.pbi</file>
      <file>$PROJ_DIR$\temp\per_test\basic_rf.r51</file>
      <file>$PROJ_DIR$\temp\per_test\hal_rf.pbi</file>
      <file>$PROJ_DIR$\temp\per_test\util.r51</file>
      <file>$PROJ_DIR$\..\..\..\source\Components\targets\srf05_soc\hal_button.c</file>
      <file>$PROJ_DIR$\..\..\..\source\Components\utils\util_lcd.c</file>
      <file>$PROJ_DIR$\..\..\..\source\Components\utils\util_lcd.h</file>
      <file>$PROJ_DIR$\..\..\..\source\Components\targets\srf05_soc\hal_board.c</file>
      <file>$PROJ_DIR$\..\..\..\source\Components\utils\util.c</file>
      <file>$PROJ_DIR$\..\..\..\source\Components\targets\srf05_soc\hal_board.h</file>
      <file>$PROJ_DIR$\..\..\..\source\Components\utils\util_buffer.h</file>
      <file>$PROJ_DIR$\..\..\..\source\Components\targets\interface\hal_rf.h</file>
      <file>$PROJ_DIR$\temp\per_test\hal_joystick.r51</file>
      <file>$PROJ_DIR$\temp\per_test\hal_mcu.pbi</file>
      <file>$PROJ_DIR$\..\..\..\source\Components\radios\cc2530\hal_rf.c</file>
      <file>$PROJ_DIR$\..\..\..\source\Components\utils\util.h</file>
      <file>$PROJ_DIR$\..\..\..\source\Components\utils\util_buffer.c</file>
      <file>$TOOLKIT_DIR$\inc\ioCC2530.h</file>
      <file>$PROJ_DIR$\..\..\..\source\Components\targets\srf05_soc\hal_joystick.c</file>
      <file>$PROJ_DIR$\..\..\..\source\Components\radios\cc2530\hal_mcu.c</file>
      <file>$PROJ_DIR$\temp\per_test\hal_board.r51</file>
      <file>$PROJ_DIR$\..\..\..\source\Components\targets\srf05_soc\hal_digio.c</file>
      <file>$PROJ_DIR$\..\..\..\source\Components\radios\cc2530\clock.c</file>
      <file>$PROJ_DIR$\temp\per_test\hal_mcu.r51</file>
      <file>$PROJ_DIR$\temp\per_test\per_test.r51</file>
      <file>$PROJ_DIR$\..\..\..\source\Components\targets\srf05_soc\hal_led.c</file>
      <file>$TOOLKIT_DIR$\inc\clib\string.h</file>
      <file>$PROJ_DIR$\..\..\..\source\Components\radios\cc2530\hal_timer_32k.c</file>
      <file>$PROJ_DIR$\..\..\..\source\Components\targets\srf05_soc\hal_assert.c</file>
      <file>$PROJ_DIR$\..\..\..\source\Components\radios\cc2530\adc.h</file>
      <file>$PROJ_DIR$\..\..\..\source\Components\radios\cc2530\clock.h</file>
      <file>$PROJ_DIR$\temp\per_test\hal_rf.r51</file>
      <file>$PROJ_DIR$\temp\per_test\clock.r51</file>
      <file>$PROJ_DIR$\temp\per_test\hal_button.r51</file>
      <file>$PROJ_DIR$\temp\per_test\hal_int.r51</file>
      <file>$PROJ_DIR$\temp\per_test\util_buffer.pbi</file>
    </outputs>
    <file>
      <name>$PROJ_DIR$\..\..\..\source\Components\radios\cc2530\adc.c</name>
      <outputs>
        <tool>
          <name>BICOMP</name>
          <file> 40</file>
        </tool>
        <tool>
          <name>ICC8051</name>
          <file> 37</file>
        </tool>
      </outputs>
      <inputs>
        <tool>
          <name>BICOMP</name>
          <file> 78 22 10 66</file>
        </tool>
      </inputs>
    </file>
    <file>
      <name>$PROJ_DIR$\..\..\..\source\Components\common\hal_int.c</name>
      <outputs>
        <tool>
          <name>BICOMP</name>
          <file> 25</file>
        </tool>
        <tool>
          <name>ICC8051</name>
          <file> 83</file>
        </tool>
      </outputs>
      <inputs>
        <tool>
          <name>BICOMP</name>
          <file> 22 10 1 58 66 26 0</file>
        </tool>
      </inputs>
    </file>
    <file>
      <name>$PROJ_DIR$\..\..\..\source\Components\targets\common\hal_lcd_srf05.c</name>
      <outputs>
        <tool>
          <name>BICOMP</name>
          <file> 15</file>
        </tool>
        <tool>
          <name>ICC8051</name>
          <file> 35</file>
        </tool>
      </outputs>
      <inputs>
        <tool>
          <name>BICOMP</name>
          <file> 22 7 0 58 66 26 10 75 39 64</file>
        </tool>
      </inputs>
    </file>
    <file>
      <name>$PROJ_DIR$\..\..\..\source\apps\per_test\per_test.c</name>
      <outputs>
        <tool>
          <name>BICOMP</name>
          <file> 41</file>
        </tool>
        <tool>
          <name>ICC8051</name>
          <file> 73</file>
        </tool>
      </outputs>
      <inputs>
        <tool>
          <name>BICOMP</name>
          <file> 7 22 9 1 2 6 5 58 66 26 10 0 60 14 55 64 21 13 23</file>
        </tool>
      </inputs>
    </file>
    <file>
      <name>$PROJ_DIR$\..\..\..\source\Components\basicrf\basic_rf.c</name>
      <outputs>
        <tool>
          <name>BICOMP</name>
          <file> 49</file>
        </tool>
        <tool>
          <name>ICC8051</name>
          <file> 50 83 37 81 72 80 36 35 42 69 82 17 61 45 52 38 30 73 4</file>
        </tool>
      </outputs>
      <inputs>
        <tool>
          <name>BICOMP</name>
          <file> 1 22 0 60 21 10 64 75 39</file>
        </tool>
        <tool>
          <name>ICC8051</name>
          <file> 1 22 0 60 21 10 64 75 39 11 58 66 26 3 78 71 79 68 14 63 76 2 12 7 77 9 56 8 6 44 53 5 70 67 74 57 65 59 54 55 16 13 23 28</file>
        </tool>
      </inputs>
    </file>
    <file>
      <name>$PROJ_DIR$\temp\per_test\per_test.pbd</name>
      <inputs>
        <tool>
          <name>BILINK</name>
          <file> 40 49 47 29 27 31 43 25 18 15 19 62 51 48 41 46 32 84 33</file>
        </tool>
      </inputs>
    </file>
    <file>
      <name>$PROJ_DIR$\..\..\..\source\apps\per_test\per_test_menu.c</name>
      <outputs>
        <tool>
          <name>BICOMP</name>
          <file> 46</file>
        </tool>
        <tool>
          <name>ICC8051</name>
          <file> 4</file>
        </tool>
      </outputs>
      <inputs>
        <tool>
          <name>BICOMP</name>
          <file> 23 22 10 58 66 26 0 7 60 14 55 64</file>
        </tool>
      </inputs>
    </file>
    <file>
      <name>[ROOT_NODE]</name>
      <outputs>
        <tool>
          <name>XLINK</name>
          <file> 34</file>
        </tool>
      </outputs>
    </file>
    <file>
      <name>$PROJ_DIR$\..\..\..\source\Components\targets\srf05_soc\hal_button.c</name>
      <outputs>
        <tool>
          <name>BICOMP</name>
          <file> 31</file>
        </tool>
        <tool>
          <name>ICC8051</name>
          <file> 82</file>
        </tool>
      </outputs>
      <inputs>
        <tool>
          <name>BICOMP</name>
          <file> 22 58 66 26 10 0 5</file>
        </tool>
      </inputs>
    </file>
    <file>
      <name>$PROJ_DIR$\..\..\..\source\Components\utils\util_lcd.c</name>
      <outputs>
        <tool>
          <name>BICOMP</name>
          <file> 33</file>
        </tool>
        <tool>
          <name>ICC8051</name>
          <file> 30</file>
        </tool>
      </outputs>
      <inputs>
        <tool>
          <name>BICOMP</name>
          <file> 58 66 26 22 10 0 7 6 5 14 55 64 60 75 39</file>
        </tool>
      </inputs>
    </file>
    <file>
      <name>$PROJ_DIR$\..\..\..\source\Components\targets\srf05_soc\hal_board.c</name>
      <outputs>
        <tool>
          <name>BICOMP</name>
          <file> 27</file>
        </tool>
        <tool>
          <name>ICC8051</name>
          <file> 69</file>
        </tool>
      </outputs>
      <inputs>
        <tool>
          <name>BICOMP</name>
          <file> 22 10 8 1 0 58 66 26 7 6 79 44</file>
        </tool>
      </inputs>
    </file>
    <file>
      <name>$PROJ_DIR$\..\..\..\source\Components\utils\util.c</name>
      <outputs>
        <tool>
          <name>BICOMP</name>
          <file> 32</file>
        </tool>
        <tool>
          <name>ICC8051</name>
          <file> 52</file>
        </tool>
      </outputs>
      <inputs>
        <tool>
          <name>BICOMP</name>
          <file> 64 22 10 60</file>
        </tool>
      </inputs>
    </file>
    <file>
      <name>$PROJ_DIR$\..\..\..\source\Components\radios\cc2530\hal_rf.c</name>
      <outputs>
        <tool>
          <name>BICOMP</name>
          <file> 51</file>
        </tool>
        <tool>
          <name>ICC8051</name>
          <file> 80</file>
        </tool>
      </outputs>
      <inputs>
        <tool>
          <name>BICOMP</name>
          <file> 58 66 26 22 10 0 1 14 60 64</file>
        </tool>
      </inputs>
    </file>
    <file>
      <name>$PROJ_DIR$\..\..\..\source\Components\utils\util_buffer.c</name>
      <outputs>
        <tool>
          <name>BICOMP</name>
          <file> 84</file>
        </tool>
        <tool>
          <name>ICC8051</name>
          <file> 38</file>
        </tool>
      </outputs>
      <inputs>
        <tool>
          <name>BICOMP</name>
          <file> 59 22 58 66 26 10 0 1 14</file>
        </tool>
      </inputs>
    </file>
    <file>
      <name>$PROJ_DIR$\..\..\..\source\Components\targets\srf05_soc\hal_joystick.c</name>
      <outputs>
        <tool>
          <name>BICOMP</name>
          <file> 18</file>
        </tool>
        <tool>
          <name>ICC8051</name>
          <file> 61</file>
        </tool>
      </outputs>
      <inputs>
        <tool>
          <name>BICOMP</name>
          <file> 6 22 58 66 26 10 0 78 8 1</file>
        </tool>
      </inputs>
    </file>
    <file>
      <name>$PROJ_DIR$\..\..\..\source\Components\radios\cc2530\hal_mcu.c</name>
      <outputs>
        <tool>
          <name>BICOMP</name>
          <file> 62</file>
        </tool>
        <tool>
          <name>ICC8051</name>
          <file> 72</file>
        </tool>
      </outputs>
      <inputs>
        <tool>
          <name>BICOMP</name>
          <file> 22 0 79 10 66 14</file>
        </tool>
      </inputs>
    </file>
    <file>
      <name>$PROJ_DIR$\..\..\..\source\Components\targets\srf05_soc\hal_digio.c</name>
      <outputs>
        <tool>
          <name>BICOMP</name>
          <file> 43</file>
        </tool>
        <tool>
          <name>ICC8051</name>
          <file> 17</file>
        </tool>
      </outputs>
      <inputs>
        <tool>
          <name>BICOMP</name>
          <file> 22 10 58 66 26 0 1 8</file>
        </tool>
      </inputs>
    </file>
    <file>
      <name>$PROJ_DIR$\..\..\..\source\Components\radios\cc2530\clock.c</name>
      <outputs>
        <tool>
          <name>BICOMP</name>
          <file> 47</file>
        </tool>
        <tool>
          <name>ICC8051</name>
          <file> 81</file>
        </tool>
      </outputs>
      <inputs>
        <tool>
          <name>BICOMP</name>
          <file> 79 22 10 66 0</file>
        </tool>
      </inputs>
    </file>
    <file>
      <name>$PROJ_DIR$\..\..\..\source\Components\targets\srf05_soc\hal_led.c</name>
      <outputs>
        <tool>
          <name>BICOMP</name>
          <file> 19</file>
        </tool>
        <tool>
          <name>ICC8051</name>
          <file> 45</file>
        </tool>
      </outputs>
      <inputs>
        <tool>
          <name>BICOMP</name>
          <file> 22 9 58 66 26 10 0</file>
        </tool>
      </inputs>
    </file>
    <file>
      <name>$PROJ_DIR$\..\..\..\source\Components\radios\cc2530\hal_timer_32k.c</name>
      <outputs>
        <tool>
          <name>BICOMP</name>
          <file> 48</file>
        </tool>
        <tool>
          <name>ICC8051</name>
          <file> 36</file>
        </tool>
      </outputs>
      <inputs>
        <tool>
          <name>BICOMP</name>
          <file> 22 10 2 58 66 26 0 1 14</file>
        </tool>
      </inputs>
    </file>
    <file>
      <name>$PROJ_DIR$\..\..\..\source\Components\targets\srf05_soc\hal_assert.c</name>
      <outputs>
        <tool>
          <name>BICOMP</name>
          <file> 29</file>
        </tool>
        <tool>
          <name>ICC8051</name>
          <file> 42</file>
        </tool>
      </outputs>
      <inputs>
        <tool>
          <name>BICOMP</name>
          <file> 14 10 9 22 0</file>
        </tool>
      </inputs>
    </file>
  </configuration>
</project>


