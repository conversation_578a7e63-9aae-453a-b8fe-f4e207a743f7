# TDMA主从同步配置指南

## 🎯 目标
实现CC2530主从节点之间的TDMA时分多址通信，主节点发送beacon帧，从节点同步并发送传感器数据。

## 📋 硬件准备

### 需要的设备
1. **2个CC2530开发板**
   - 1个作为主节点（Master Node）
   - 1个作为从节点（Slave Node）

2. **震动传感器SW-520D**
   - 连接到从节点的P1.2引脚
   - VCC → 3.3V, GND → GND, DO → P1.2

## 🔧 代码配置

### 1. 从节点配置（当前已完成）

**编译宏定义：**
```c
#define SLAVE_NODE  // 定义为从节点
```

**功能特点：**
- ✅ 震动传感器已配置在P1.2
- ✅ TDMA协议已启用
- ✅ BasicRF无线通信已启用
- ✅ 等待主节点beacon帧同步

### 2. 主节点配置（需要另一个开发板）

**编译宏定义：**
```c
#define MASTER_NODE  // 定义为主节点
```

**需要使用的项目文件：**
- `tdma_master_node.c` 或
- 修改 `light_switch.c` 并定义 `MASTER_NODE`

## 📊 通信协议

### TDMA时隙分配
```
时隙0: 主节点发送beacon帧 (20ms)
时隙1: 从节点1发送数据 (20ms)  
时隙2: 从节点2发送数据 (20ms)
时隙3: 从节点3发送数据 (20ms)
时隙4: 维护时隙 (20ms)
总周期: 100ms
```

### 数据包格式
**Beacon帧（主节点→从节点）：**
```c
typedef struct {
  frame_header_t header;
  uint16 superframeId;
  uint8 slotInfo;
  uint8 networkStatus;
} beacon_frame_t;
```

**传感器数据帧（从节点→主节点）：**
```c
typedef struct {
  frame_header_t header;
  uint8 nodeId;
  uint16 temperature;
  uint16 vibration;
  uint8 ledStatus;
  uint8 batteryLevel;
} sensor_data_packet_t;
```

## 🚀 启动步骤

### 步骤1：配置从节点（当前开发板）

1. **编译当前代码**
   - 确保定义了 `SLAVE_NODE`
   - 震动传感器已连接到P1.2

2. **下载并运行**
   - 串口输出应显示：
   ```
   === TDMA SLAVE NODE MODE ===
   Vibration sensor on P1.2, waiting for master...
   Waiting for master beacon...
   ```

3. **LED状态指示**
   - **LED2**: 启动时闪烁2次（从节点模式）
   - **LED4**: 心跳指示（每秒闪烁）
   - **LED1**: 未同步时双闪模式

### 步骤2：配置主节点（另一个开发板）

**选项A：使用现有的主节点项目**
```bash
# 编译 tdma_master_node.ewp 项目
# 或者编译 light_switch.ewp 并定义 MASTER_NODE
```

**选项B：修改当前项目为主节点**
1. 在项目设置中定义 `MASTER_NODE`
2. 取消定义 `SLAVE_NODE`
3. 重新编译

### 步骤3：测试同步

1. **先启动主节点**
   - 主节点开始发送beacon帧
   - LED指示主节点模式

2. **再启动从节点**
   - 从节点接收beacon帧并同步
   - LED1停止双闪，开始正常工作

3. **观察串口输出**

**主节点串口输出：**
```
Master Node Started
Sending beacon frames...
Node:1,Temp:250,Vib:0,LED:0,Seq:1
Node:1,Temp:251,Vib:1,LED:0,Seq:2
```

**从节点串口输出：**
```
Network synchronized
Node1: Temp=250, Vib=0, Sync=1, Slot=1
Node1: Temp=251, Vib=1, Sync=1, Slot=1
```

## 🎯 同步成功的标志

### 从节点同步成功
1. ✅ **串口显示**: `Network synchronized`
2. ✅ **LED1行为**: 停止双闪，在发送数据时短暂点亮
3. ✅ **LED2行为**: 接收到beacon时短暂闪烁
4. ✅ **数据传输**: `Sync=1` 表示已同步

### 主节点工作正常
1. ✅ **串口显示**: 接收到从节点数据并输出
2. ✅ **LED指示**: 正常的心跳和数据指示
3. ✅ **数据接收**: 显示从节点的传感器数据

## 🔧 故障排除

### 问题1：从节点无法同步
**现象**: 持续显示 `Waiting for master beacon...`

**解决方案**:
1. 检查主节点是否正常启动
2. 确认两个节点使用相同的PAN ID和信道
3. 检查无线通信距离（建议1米内测试）

### 问题2：数据传输不稳定
**现象**: 偶尔丢失数据包

**解决方案**:
1. 调整发射功率
2. 减少环境干扰
3. 检查时隙同步是否准确

### 问题3：震动数据不正确
**现象**: 震动传感器数据异常

**解决方案**:
1. 确认P1.2连接正确
2. 检查传感器供电
3. 测试传感器硬件

## 📝 配置参数

### 重要参数设置
```c
#define PAN_ID              0x2007
#define RF_CHANNEL          11
#define MASTER_NODE_ADDR    0x7770
#define SLAVE_NODE_BASE_ADDR 0x7771
#define NODE_ID             1  // 从节点ID

// TDMA时隙定义
#define SLOT_MASTER_BEACON  0
#define SLOT_SLAVE_1        1
#define SLOT_SLAVE_2        2
#define SLOT_SLAVE_3        3
#define SLOT_MAINTENANCE    4
#define TDMA_TOTAL_SLOTS    5
#define TDMA_SUPERFRAME_LENGTH 100  // ms
```

## 🎉 成功标志

当看到以下输出时，表示主从同步成功：

**从节点**:
```
Network synchronized
Node1: Temp=250, Vib=0, Sync=1, Slot=1
Node1: Temp=251, Vib=1, Sync=1, Slot=1
```

**主节点**:
```
Node:1,Temp:250,Vib:0,LED:0,Seq:1
Node:1,Temp:251,Vib:1,LED:0,Seq:2
```

这表示：
- ✅ 主从节点成功同步
- ✅ 震动传感器数据正常传输
- ✅ TDMA协议工作正常
- ✅ 无线通信稳定

## 🚀 下一步

同步成功后，你可以：
1. 添加更多从节点（修改NODE_ID）
2. 实现LED远程控制
3. 添加更多传感器
4. 优化通信协议
