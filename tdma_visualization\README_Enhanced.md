# TDMA传感器网络增强监控系统

## 🎯 项目简介

这是一个功能完整的TDMA传感器网络监控系统，包含实时数据显示、数据库存储、图形化分析和多节点管理功能。

## ✨ 主要功能

### 📊 **数据可视化**
- **实时图表显示**：温度趋势、振动状态、同步状态
- **多节点支持**：同时显示多个传感器节点的数据
- **时间范围选择**：1分钟到1小时的历史数据查看
- **图表类型切换**：温度、振动、综合视图

### 💾 **数据库存储**
- **SQLite数据库**：自动存储所有传感器数据
- **数据统计**：实时显示记录数、节点数、数据库大小
- **数据查询**：按节点、时间范围查询历史数据
- **数据导出**：支持CSV格式导出

### 🖥️ **用户界面**
- **多标签页设计**：实时监控、数据图表、数据库管理
- **实时数据表格**：显示最新的传感器读数
- **增强日志管理**：系统日志记录、过滤、清除、保存功能
- **串口控制**：支持多种波特率和串口选择

### 📝 **日志管理功能**
- **日志控制按钮**：清除日志、保存日志
- **智能过滤**：按类型过滤日志（全部/SERIAL/DEBUG/ERROR/DEMO）
- **右键菜单**：清除日志、保存日志、复制选中内容
- **快捷键支持**：Ctrl+L清除、Ctrl+S保存、Ctrl+D清除数据
- **自动分类**：串口数据、调试信息、错误信息、演示数据

### 🔧 **系统管理**
- **多节点管理**：自动发现和管理多个传感器节点
- **数据清理**：自动清理过期数据
- **演示模式**：无硬件时的模拟数据显示
- **错误处理**：完善的异常处理和用户提示

## 🚀 快速开始

### 1. 安装依赖
```bash
pip install -r requirements.txt
```

### 2. 运行程序

**Windows用户（推荐）**:
```bash
# 运行启动菜单，选择合适的版本
run.bat
```

**手动启动**:
```bash
# 简化版本（稳定快速）
python run_simple.py

# 增强版本（图表功能）
python run_enhanced.py

# 性能优化版本（推荐）
python performance_config.py && python run_enhanced.py
```

### 3. 性能优化工具
```bash
# 性能监控工具
python performance_monitor.py

# 中文字体修复
python fix_chinese_display.py

# 字体测试
python test_chinese_font.py
```

## 📋 系统要求

- **Python 3.6+**
- **必需依赖**：
  - `tkinter`（Python内置）
  - `sqlite3`（Python内置）
- **可选依赖**：
  - `pyserial>=3.5`（串口通信）
  - `matplotlib>=3.5.0`（图表显示）
  - `numpy>=1.20.0`（数学计算）

## 🎨 界面预览

### 实时监控标签页
```
┌─────────────────────────────────────────────────────────┐
│ [实时监控] [数据图表] [数据库管理]                        │
├─────────────────────────────────────────────────────────┤
│ 连接控制: [串口] [波特率] [连接] [状态] [数据统计]        │
├─────────────────────────────────────────────────────────┤
│ 实时数据表格                                            │
│ 时间     │节点ID│温度│振动│同步状态│时隙                  │
│ 12:34:56 │节点1 │25.6│ 0  │✓ 同步 │ 1                   │
├─────────────────────────────────────────────────────────┤
│ 系统日志 [清除日志] [保存日志]                          │
│ [12:34:56] 系统启动完成                                 │
└─────────────────────────────────────────────────────────┘
```

### 数据图表标签页
```
┌─────────────────────────────────────────────────────────┐
│ 图表控制: [节点选择] [时间范围] [图表类型] [刷新]        │
├─────────────────────────────────────────────────────────┤
│ ┌─────────────────┐ ┌─────────────────┐                 │
│ │   温度趋势图     │ │   振动状态图     │                 │
│ │                │ │                │                 │
│ └─────────────────┘ └─────────────────┘                 │
│ ┌─────────────────┐ ┌─────────────────┐                 │
│ │   同步状态图     │ │   综合视图      │                 │
│ │                │ │                │                 │
│ └─────────────────┘ └─────────────────┘                 │
└─────────────────────────────────────────────────────────┘
```

## 📊 数据格式支持

系统支持多种TDMA数据格式：

1. **标准格式**：`Node 1: Temp=25.6, Vib=0, Sync=1, Slot=1`
2. **紧凑格式**：`Node 1: Temp=25.0, Vib=0, Sync=1, Slot=1`
3. **兼容格式**：`MASTER: Received from Node 3 - Temp=298, Vib=0`

## 🗄️ 数据库结构

### sensor_readings 表
| 字段        | 类型    | 说明     |
| ----------- | ------- | -------- |
| id          | INTEGER | 主键     |
| timestamp   | TEXT    | 时间戳   |
| node_id     | INTEGER | 节点ID   |
| temperature | REAL    | 温度值   |
| vibration   | INTEGER | 振动状态 |
| sync_status | INTEGER | 同步状态 |
| slot_number | INTEGER | 时隙号   |

### node_info 表
| 字段          | 类型     | 说明           |
| ------------- | -------- | -------------- |
| node_id       | INTEGER  | 节点ID（主键） |
| node_name     | TEXT     | 节点名称       |
| last_seen     | DATETIME | 最后活跃时间   |
| total_packets | INTEGER  | 总数据包数     |
| is_active     | BOOLEAN  | 是否活跃       |

## ⚡ 性能优化

### 🎯 优化措施

系统已内置多项性能优化：

1. **智能图表更新**
   - 更新间隔：从2秒增加到5秒
   - 智能更新：只在数据变化时更新
   - 使用`draw_idle()`提高渲染性能

2. **内存优化**
   - 数据点限制：从100个减少到50个
   - 表格行数限制：最多50行
   - 日志行数限制：最多200行

3. **数据处理优化**
   - 批量处理：每次最多处理10条数据
   - 处理间隔：从100ms增加到200ms
   - 队列管理：避免数据积压

### 🔧 性能配置

#### 性能模式选择
```python
# 高性能模式（推荐用于低配置设备）
config = PerformanceConfig.get_optimized_config("high_performance")

# 平衡模式（默认）
config = PerformanceConfig.get_optimized_config("balanced")

# 高质量模式（适用于高配置设备）
config = PerformanceConfig.get_optimized_config("high_quality")
```

#### 手动配置
```python
# 图表更新频率
self.chart_update_interval = 5000  # 5秒更新一次

# 数据保留策略
'timestamps': deque(maxlen=50)  # 保留最近50个数据点

# 数据库自动清理7天前的数据
```

### 📊 性能监控

使用内置性能监控工具：
```bash
python performance_monitor.py
```

功能：
- 实时CPU和内存监控
- 性能瓶颈分析
- 自动优化建议
- 性能报告生成

### 🚨 性能问题解决

#### 问题1：图表卡顿或崩溃
**解决方案**：
```bash
# 启用高性能模式
python performance_config.py
python run_enhanced.py
```

#### 问题2：内存使用过高
**解决方案**：
- 减少数据缓存：`maxlen=30`
- 定期清理数据库
- 使用简化版本

#### 问题3：界面响应慢
**解决方案**：
- 增加更新间隔
- 减少同时显示的节点数
- 关闭不必要的图表

## 🎯 多节点支持

系统设计支持多达20个传感器节点：
- **自动发现**：新节点自动添加到监控列表
- **独立显示**：每个节点独立的数据曲线
- **颜色区分**：不同节点使用不同颜色显示
- **状态监控**：实时监控每个节点的连接状态

## 📈 扩展功能

### 已实现
- ✅ 实时数据监控
- ✅ 数据库存储
- ✅ 图表可视化
- ✅ 多节点支持
- ✅ 数据导出

### 可扩展
- 🔄 报警系统
- 🔄 远程监控
- 🔄 数据分析算法
- 🔄 Web界面
- 🔄 移动端支持

## 🐛 故障排除

### 常见问题

1. **matplotlib导入失败**
   ```bash
   pip install matplotlib
   ```

2. **中文字体显示乱码** ⭐

   **问题现象**: 图表标题显示为方块或乱码

   **快速修复**:
   ```bash
   # 运行自动修复工具
   python fix_chinese_display.py

   # 或手动测试字体
   python test_chinese_font.py
   ```

   **手动解决方案**:

   **Windows系统**:
   - 确保系统已安装中文字体（黑体、微软雅黑）
   - 下载思源黑体: https://github.com/adobe-fonts/source-han-sans

   **macOS系统**:
   ```bash
   brew tap homebrew/cask-fonts
   brew install font-source-han-sans
   ```

   **Linux系统**:
   ```bash
   # Ubuntu/Debian
   sudo apt install fonts-wqy-microhei fonts-noto-cjk

   # CentOS/RHEL
   sudo yum install wqy-microhei-fonts

   # 刷新字体缓存
   fc-cache -fv
   ```

3. **串口连接失败**
   - 检查串口权限
   - 确认波特率设置
   - 检查串口是否被占用

4. **清除数据功能无效** ⭐

   **问题现象**: 点击清除数据后，图表仍显示旧数据

   **已修复**:
   - ✅ 清除表格显示数据
   - ✅ 清除内存数据列表
   - ✅ 清除图表数据(node_data)
   - ✅ 清除数据队列
   - ✅ 自动更新图表和统计

   **新增功能**:
   - 🆕 清除实时数据（保留数据库）
   - 🆕 清除所有数据（包括数据库）
   - 🆕 用户确认对话框
   - 🆕 控制面板清除按钮

5. **数据库错误**
   - 检查data目录权限
   - 确认磁盘空间充足

### 字体问题详细说明

如果图表中的中文显示为乱码，这是matplotlib缺少中文字体支持导致的。系统已内置字体回退机制：

- **自动检测**: 系统会自动检测可用的中文字体
- **智能回退**: 如果中文显示失败，自动使用英文标题
- **多平台支持**: 支持Windows、macOS、Linux的常见中文字体

## 📞 技术支持

如需技术支持或功能扩展，请检查：
1. Python版本兼容性
2. 依赖包版本
3. 系统权限设置
4. 硬件连接状态
