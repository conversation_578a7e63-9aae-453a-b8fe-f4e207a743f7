<?xml version="1.0" encoding="iso-8859-1"?>

<project>
  <fileVersion>2</fileVersion>
  <fileChecksum>2954946759</fileChecksum>
  <configuration>
    <name>srf05_cc2530_91</name>
    <outputs>
      <file>$PROJ_DIR$\..\..\..\source\Components\targets\interface\hal_int.h</file>
      <file>$PROJ_DIR$\temp\per_test\hal_mcu.pbi</file>
      <file>$PROJ_DIR$\temp\per_test\hal_mcu.r51</file>
      <file>$PROJ_DIR$\..\..\..\source\apps\per_test\per_test.c</file>
      <file>$PROJ_DIR$\temp\per_test\hal_button.r51</file>
      <file>$PROJ_DIR$\temp\per_test\hal_digio.pbi</file>
      <file>$PROJ_DIR$\..\..\..\source\apps\per_test\per_test_menu.h</file>
      <file>$PROJ_DIR$\..\..\..\source\Components\basicrf\basic_rf.c</file>
      <file>$PROJ_DIR$\..\..\..\source\Components\common\cc8051\hal_cc8051.h</file>
      <file>$PROJ_DIR$\..\..\..\source\Components\common\hal_defs.h</file>
      <file>$PROJ_DIR$\..\..\..\source\Components\basicrf\basic_rf.h</file>
      <file>$PROJ_DIR$\..\..\..\source\Components\common\hal_int.c</file>
      <file>$PROJ_DIR$\temp\per_test\adc.pbi</file>
      <file>$PROJ_DIR$\..\..\..\source\Components\targets\common\hal_lcd_srf05.c</file>
      <file>$PROJ_DIR$\..\..\..\source\Components\common\hal_types.h</file>
      <file>$PROJ_DIR$\temp\per_test\hal_button.pbi</file>
      <file>$PROJ_DIR$\..\..\..\source\Components\targets\interface\hal_button.h</file>
      <file>$PROJ_DIR$\..\..\..\source\Components\targets\interface\hal_assert.h</file>
      <file>$PROJ_DIR$\..\..\..\source\Components\targets\interface\hal_digio.h</file>
      <file>$PROJ_DIR$\..\..\..\source\apps\per_test\per_test.h</file>
      <file>$PROJ_DIR$\..\..\..\source\apps\per_test\per_test_menu.c</file>
      <file>$PROJ_DIR$\temp\per_test\per_test.pbd</file>
      <file>$PROJ_DIR$\temp\per_test\clock.pbi</file>
      <file>$PROJ_DIR$\per_test.d51</file>
      <file>$PROJ_DIR$\temp\per_test\hal_int.pbi</file>
      <file>$PROJ_DIR$\temp\per_test\hal_rf.pbi</file>
      <file>$PROJ_DIR$\temp\per_test\per_test.r51</file>
      <file>$TOOLKIT_DIR$\inc\clib\string.h</file>
      <file>$PROJ_DIR$\temp\per_test\adc.r51</file>
      <file>$PROJ_DIR$\..\..\..\source\Components\targets\interface\hal_joystick.h</file>
      <file>$PROJ_DIR$\..\..\..\source\Components\radios\cc2530\hal_timer_32k.c</file>
      <file>$PROJ_DIR$\..\..\..\source\Components\targets\interface\hal_rf.h</file>
      <file>$PROJ_DIR$\..\..\..\source\Components\targets\interface\hal_mcu.h</file>
      <file>$PROJ_DIR$\..\..\..\source\Components\radios\cc2530\hal_rf.c</file>
      <file>$PROJ_DIR$\..\..\..\source\Components\targets\srf05_soc\hal_board.c</file>
      <file>$PROJ_DIR$\..\..\..\source\Components\targets\srf05_soc\hal_joystick.c</file>
      <file>$PROJ_DIR$\..\..\..\source\Components\utils\util.c</file>
      <file>$PROJ_DIR$\..\..\..\source\Components\utils\util.h</file>
      <file>$PROJ_DIR$\..\..\..\source\Components\targets\interface\hal_timer_32k.h</file>
      <file>$PROJ_DIR$\..\..\..\source\Components\radios\cc2530\adc.c</file>
      <file>$PROJ_DIR$\..\..\..\source\Components\targets\srf05_soc\hal_assert.c</file>
      <file>$PROJ_DIR$\..\..\..\source\Components\targets\srf05_soc\hal_board.h</file>
      <file>$PROJ_DIR$\..\..\..\source\Components\targets\srf05_soc\hal_digio.c</file>
      <file>$PROJ_DIR$\..\..\..\source\Components\targets\interface\hal_lcd.h</file>
      <file>$PROJ_DIR$\..\..\..\source\Components\targets\srf05_soc\hal_button.c</file>
      <file>$PROJ_DIR$\..\..\..\source\Components\targets\srf05_soc\hal_led.c</file>
      <file>$PROJ_DIR$\..\..\..\source\Components\radios\cc2530\hal_mcu.c</file>
      <file>$PROJ_DIR$\..\..\..\source\Components\targets\interface\hal_led.h</file>
      <file>$PROJ_DIR$\..\..\..\source\Components\radios\cc2530\clock.c</file>
      <file>$PROJ_DIR$\..\..\..\source\Components\radios\cc2530\adc.h</file>
      <file>$PROJ_DIR$\..\..\..\source\Components\radios\cc2530\clock.h</file>
      <file>$PROJ_DIR$\temp\per_test\util_buffer.r51</file>
      <file>$PROJ_DIR$\temp\per_test\per_test_menu.pbi</file>
      <file>$PROJ_DIR$\temp\per_test\hal_joystick.r51</file>
      <file>$PROJ_DIR$\temp\per_test\basic_rf.pbi</file>
      <file>$PROJ_DIR$\temp\per_test\hal_assert.r51</file>
      <file>$PROJ_DIR$\temp\per_test\util.r51</file>
      <file>$TOOLKIT_DIR$\inc\clib\sysmac.h</file>
      <file>$PROJ_DIR$\temp\per_test\hal_board.r51</file>
      <file>$PROJ_DIR$\temp\per_test\hal_lcd_srf05.r51</file>
      <file>$TOOLKIT_DIR$\inc\ioCC2530.h</file>
      <file>$PROJ_DIR$\temp\per_test\hal_led.pbi</file>
      <file>$PROJ_DIR$\temp\per_test\basic_rf.r51</file>
      <file>$PROJ_DIR$\temp\per_test\util.pbi</file>
      <file>$PROJ_DIR$\temp\per_test\hal_lcd_srf05.pbi</file>
      <file>$PROJ_DIR$\temp\per_test\hal_timer_32k.pbi</file>
      <file>$PROJ_DIR$\temp\per_test\hal_board.pbi</file>
      <file>$PROJ_DIR$\temp\per_test\util_lcd.r51</file>
      <file>$PROJ_DIR$\temp\per_test\hal_led.r51</file>
      <file>$PROJ_DIR$\temp\per_test\hal_digio.r51</file>
      <file>$PROJ_DIR$\temp\per_test\clock.r51</file>
      <file>$PROJ_DIR$\temp\per_test\util_buffer.pbi</file>
      <file>$PROJ_DIR$\temp\per_test\hal_joystick.pbi</file>
      <file>$PROJ_DIR$\temp\per_test\hal_assert.pbi</file>
      <file>$PROJ_DIR$\..\..\..\source\Components\utils\util_lcd.h</file>
      <file>$PROJ_DIR$\..\..\..\source\Components\common\hal_uart.h</file>
      <file>$PROJ_DIR$\temp\per_test\per_test.pbi</file>
      <file>$PROJ_DIR$\temp\per_test\hal_int.r51</file>
      <file>$PROJ_DIR$\..\..\..\source\Components\utils\util_buffer.h</file>
      <file>$PROJ_DIR$\..\..\..\source\Components\utils\util_lcd.c</file>
      <file>$PROJ_DIR$\temp\per_test\util_lcd.pbi</file>
      <file>$PROJ_DIR$\..\..\..\source\Components\utils\util_buffer.c</file>
      <file>$PROJ_DIR$\temp\per_test\hal_timer_32k.r51</file>
      <file>$PROJ_DIR$\temp\per_test\per_test_menu.r51</file>
    </outputs>
    <file>
      <name>$PROJ_DIR$\..\..\..\source\apps\per_test\per_test.c</name>
      <outputs>
        <tool>
          <name>BICOMP</name>
          <file> 76</file>
        </tool>
        <tool>
          <name>ICC8051</name>
          <file> 26</file>
        </tool>
      </outputs>
      <inputs>
        <tool>
          <name>BICOMP</name>
          <file> 43 14 47 0 38 29 16 41 60 8 9 32 31 17 74 37 10 19 6</file>
        </tool>
        <tool>
          <name>ICC8051</name>
          <file> 43 14 47 0 38 29 16 41 60 8 9 32 31 17 74 37 10 19 6</file>
        </tool>
      </inputs>
    </file>
    <file>
      <name>$PROJ_DIR$\..\..\..\source\Components\basicrf\basic_rf.c</name>
      <outputs>
        <tool>
          <name>BICOMP</name>
          <file> 54</file>
        </tool>
        <tool>
          <name>ICC8051</name>
          <file> 62</file>
        </tool>
      </outputs>
      <inputs>
        <tool>
          <name>BICOMP</name>
          <file> 0 14 32 31 10 9 37 27 57</file>
        </tool>
        <tool>
          <name>ICC8051</name>
          <file> 0 14 32 31 10 9 37 27 57</file>
        </tool>
      </inputs>
    </file>
    <file>
      <name>$PROJ_DIR$\..\..\..\source\Components\common\hal_int.c</name>
      <outputs>
        <tool>
          <name>BICOMP</name>
          <file> 24</file>
        </tool>
        <tool>
          <name>ICC8051</name>
          <file> 77</file>
        </tool>
      </outputs>
      <inputs>
        <tool>
          <name>BICOMP</name>
          <file> 14 9 0 41 60 8 32</file>
        </tool>
        <tool>
          <name>ICC8051</name>
          <file> 14 9 0 41 60 8 32</file>
        </tool>
      </inputs>
    </file>
    <file>
      <name>$PROJ_DIR$\..\..\..\source\Components\targets\common\hal_lcd_srf05.c</name>
      <outputs>
        <tool>
          <name>BICOMP</name>
          <file> 64</file>
        </tool>
        <tool>
          <name>ICC8051</name>
          <file> 59</file>
        </tool>
      </outputs>
      <inputs>
        <tool>
          <name>BICOMP</name>
          <file> 14 43 32 41 60 8 9 27 57 37</file>
        </tool>
        <tool>
          <name>ICC8051</name>
          <file> 14 43 32 41 60 8 9 27 57 37</file>
        </tool>
      </inputs>
    </file>
    <file>
      <name>$PROJ_DIR$\..\..\..\source\apps\per_test\per_test_menu.c</name>
      <outputs>
        <tool>
          <name>BICOMP</name>
          <file> 52</file>
        </tool>
        <tool>
          <name>ICC8051</name>
          <file> 83</file>
        </tool>
      </outputs>
      <inputs>
        <tool>
          <name>BICOMP</name>
          <file> 6 14 9 41 60 8 32 43 31 17 74 37</file>
        </tool>
        <tool>
          <name>ICC8051</name>
          <file> 6 14 9 41 60 8 32 43 31 17 74 37</file>
        </tool>
      </inputs>
    </file>
    <file>
      <name>$PROJ_DIR$\temp\per_test\per_test.pbd</name>
      <inputs>
        <tool>
          <name>BILINK</name>
          <file> 12 54 22 73 66 15 5 24 72 64 61 1 25 65 76 52 63 71 80</file>
        </tool>
      </inputs>
    </file>
    <file>
      <name>[ROOT_NODE]</name>
      <outputs>
        <tool>
          <name>XLINK</name>
          <file> 23</file>
        </tool>
      </outputs>
    </file>
    <file>
      <name>$PROJ_DIR$\..\..\..\source\Components\radios\cc2530\hal_timer_32k.c</name>
      <outputs>
        <tool>
          <name>BICOMP</name>
          <file> 65</file>
        </tool>
        <tool>
          <name>ICC8051</name>
          <file> 82</file>
        </tool>
      </outputs>
      <inputs>
        <tool>
          <name>BICOMP</name>
          <file> 14 9 38 41 60 8 32 0 17</file>
        </tool>
        <tool>
          <name>ICC8051</name>
          <file> 14 9 38 41 60 8 32 0 17</file>
        </tool>
      </inputs>
    </file>
    <file>
      <name>$PROJ_DIR$\..\..\..\source\Components\radios\cc2530\hal_rf.c</name>
      <outputs>
        <tool>
          <name>BICOMP</name>
          <file> 25</file>
        </tool>
      </outputs>
      <inputs>
        <tool>
          <name>BICOMP</name>
          <file> 41 60 8 14 9 32 0 17 31 37</file>
        </tool>
        <tool>
          <name>ICC8051</name>
          <file> 41 60 8 14 9 32 0 17 31 37</file>
        </tool>
      </inputs>
    </file>
    <file>
      <name>$PROJ_DIR$\..\..\..\source\Components\targets\srf05_soc\hal_board.c</name>
      <outputs>
        <tool>
          <name>BICOMP</name>
          <file> 66</file>
        </tool>
        <tool>
          <name>ICC8051</name>
          <file> 58</file>
        </tool>
      </outputs>
      <inputs>
        <tool>
          <name>BICOMP</name>
          <file> 14 9 18 0 32 41 60 8 43 29 50 75</file>
        </tool>
        <tool>
          <name>ICC8051</name>
          <file> 14 9 18 0 32 41 60 8 43 29 50 75</file>
        </tool>
      </inputs>
    </file>
    <file>
      <name>$PROJ_DIR$\..\..\..\source\Components\targets\srf05_soc\hal_joystick.c</name>
      <outputs>
        <tool>
          <name>BICOMP</name>
          <file> 72</file>
        </tool>
        <tool>
          <name>ICC8051</name>
          <file> 53</file>
        </tool>
      </outputs>
      <inputs>
        <tool>
          <name>BICOMP</name>
          <file> 29 14 41 60 8 9 32 49 18 0</file>
        </tool>
        <tool>
          <name>ICC8051</name>
          <file> 29 14 41 60 8 9 32 49 18 0</file>
        </tool>
      </inputs>
    </file>
    <file>
      <name>$PROJ_DIR$\..\..\..\source\Components\utils\util.c</name>
      <outputs>
        <tool>
          <name>BICOMP</name>
          <file> 63</file>
        </tool>
        <tool>
          <name>ICC8051</name>
          <file> 56</file>
        </tool>
      </outputs>
      <inputs>
        <tool>
          <name>BICOMP</name>
          <file> 37 14 9 31</file>
        </tool>
        <tool>
          <name>ICC8051</name>
          <file> 37 14 9 31</file>
        </tool>
      </inputs>
    </file>
    <file>
      <name>$PROJ_DIR$\..\..\..\source\Components\radios\cc2530\adc.c</name>
      <outputs>
        <tool>
          <name>BICOMP</name>
          <file> 12</file>
        </tool>
        <tool>
          <name>ICC8051</name>
          <file> 28</file>
        </tool>
      </outputs>
      <inputs>
        <tool>
          <name>BICOMP</name>
          <file> 49 14 9 60</file>
        </tool>
        <tool>
          <name>ICC8051</name>
          <file> 49 14 9 60</file>
        </tool>
      </inputs>
    </file>
    <file>
      <name>$PROJ_DIR$\..\..\..\source\Components\targets\srf05_soc\hal_assert.c</name>
      <outputs>
        <tool>
          <name>BICOMP</name>
          <file> 73</file>
        </tool>
        <tool>
          <name>ICC8051</name>
          <file> 55</file>
        </tool>
      </outputs>
      <inputs>
        <tool>
          <name>BICOMP</name>
          <file> 17 9 47 14 32</file>
        </tool>
        <tool>
          <name>ICC8051</name>
          <file> 17 9 47 14 32</file>
        </tool>
      </inputs>
    </file>
    <file>
      <name>$PROJ_DIR$\..\..\..\source\Components\targets\srf05_soc\hal_digio.c</name>
      <outputs>
        <tool>
          <name>BICOMP</name>
          <file> 5</file>
        </tool>
        <tool>
          <name>ICC8051</name>
          <file> 69</file>
        </tool>
      </outputs>
      <inputs>
        <tool>
          <name>BICOMP</name>
          <file> 14 9 41 60 8 32 0 18</file>
        </tool>
        <tool>
          <name>ICC8051</name>
          <file> 14 9 41 60 8 32 0 18</file>
        </tool>
      </inputs>
    </file>
    <file>
      <name>$PROJ_DIR$\..\..\..\source\Components\targets\srf05_soc\hal_button.c</name>
      <outputs>
        <tool>
          <name>BICOMP</name>
          <file> 15</file>
        </tool>
        <tool>
          <name>ICC8051</name>
          <file> 4</file>
        </tool>
      </outputs>
      <inputs>
        <tool>
          <name>BICOMP</name>
          <file> 14 41 60 8 9 32 16</file>
        </tool>
        <tool>
          <name>ICC8051</name>
          <file> 14 41 60 8 9 32 16</file>
        </tool>
      </inputs>
    </file>
    <file>
      <name>$PROJ_DIR$\..\..\..\source\Components\targets\srf05_soc\hal_led.c</name>
      <outputs>
        <tool>
          <name>BICOMP</name>
          <file> 61</file>
        </tool>
        <tool>
          <name>ICC8051</name>
          <file> 68</file>
        </tool>
      </outputs>
      <inputs>
        <tool>
          <name>BICOMP</name>
          <file> 14 47 41 60 8 9 32</file>
        </tool>
        <tool>
          <name>ICC8051</name>
          <file> 14 47 41 60 8 9 32</file>
        </tool>
      </inputs>
    </file>
    <file>
      <name>$PROJ_DIR$\..\..\..\source\Components\radios\cc2530\hal_mcu.c</name>
      <outputs>
        <tool>
          <name>BICOMP</name>
          <file> 1</file>
        </tool>
        <tool>
          <name>ICC8051</name>
          <file> 2</file>
        </tool>
      </outputs>
      <inputs>
        <tool>
          <name>BICOMP</name>
          <file> 14 32 50 9 60 17</file>
        </tool>
        <tool>
          <name>ICC8051</name>
          <file> 14 32 50 9 60 17</file>
        </tool>
      </inputs>
    </file>
    <file>
      <name>$PROJ_DIR$\..\..\..\source\Components\radios\cc2530\clock.c</name>
      <outputs>
        <tool>
          <name>BICOMP</name>
          <file> 22</file>
        </tool>
        <tool>
          <name>ICC8051</name>
          <file> 70</file>
        </tool>
      </outputs>
      <inputs>
        <tool>
          <name>BICOMP</name>
          <file> 50 14 9 60 32</file>
        </tool>
        <tool>
          <name>ICC8051</name>
          <file> 50 14 9 60 32</file>
        </tool>
      </inputs>
    </file>
    <file>
      <name>$PROJ_DIR$\..\..\..\source\Components\utils\util_lcd.c</name>
      <outputs>
        <tool>
          <name>BICOMP</name>
          <file> 80</file>
        </tool>
        <tool>
          <name>ICC8051</name>
          <file> 67</file>
        </tool>
      </outputs>
      <inputs>
        <tool>
          <name>BICOMP</name>
          <file> 41 60 8 14 9 32 43 29 16 17 74 37 31 27 57</file>
        </tool>
        <tool>
          <name>ICC8051</name>
          <file> 41 60 8 14 9 32 43 29 16 17 74 37 31 27 57</file>
        </tool>
      </inputs>
    </file>
    <file>
      <name>$PROJ_DIR$\..\..\..\source\Components\utils\util_buffer.c</name>
      <outputs>
        <tool>
          <name>BICOMP</name>
          <file> 71</file>
        </tool>
        <tool>
          <name>ICC8051</name>
          <file> 51</file>
        </tool>
      </outputs>
      <inputs>
        <tool>
          <name>BICOMP</name>
          <file> 78 14 41 60 8 9 32 0 17</file>
        </tool>
        <tool>
          <name>ICC8051</name>
          <file> 78 14 41 60 8 9 32 0 17</file>
        </tool>
      </inputs>
    </file>
    <forcedrebuild>
      <name>[MULTI_TOOL]</name>
      <tool>XLINK</tool>
    </forcedrebuild>
  </configuration>
</project>


