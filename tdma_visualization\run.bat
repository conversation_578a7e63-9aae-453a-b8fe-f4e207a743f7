@echo off
title TDMA传感器网络监控系统
color 0A

echo.
echo ========================================
echo   TDMA传感器网络监控系统
echo ========================================
echo.
echo 请选择运行模式:
echo.
echo [1] 简化版本 (推荐 - 稳定快速)
echo [2] 增强版本 (图表功能 - 可能较慢)
echo [3] 性能优化版本 (增强版本 + 性能优化)
echo [4] 性能测试和优化工具
echo [0] 退出
echo.

set /p choice="请输入选择 (0-4): "

if "%choice%"=="1" goto simple
if "%choice%"=="2" goto enhanced
if "%choice%"=="3" goto optimized
if "%choice%"=="4" goto performance
if "%choice%"=="0" goto exit
goto invalid

:simple
echo.
echo 启动简化版本...
python run_simple.py
goto end

:enhanced
echo.
echo 启动增强版本...
python run_enhanced.py
goto end

:optimized
echo.
echo 启动性能优化版本...
echo 正在应用性能优化...
python performance_config.py
echo.
echo 启动优化后的增强版本...
python run_enhanced.py
goto end

:performance
echo.
echo 启动性能测试工具...
python performance_config.py
echo.
echo 测试中文字体支持...
python test_chinese_font.py
echo.
echo 修复中文显示问题...
python fix_chinese_display.py
goto end

:invalid
echo.
echo 无效选择，请重新运行程序
goto end

:exit
echo.
echo 退出程序
exit /b 0

:end
echo.
pause
