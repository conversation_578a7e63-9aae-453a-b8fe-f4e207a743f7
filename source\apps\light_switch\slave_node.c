/***********************************************************************************
  Filename:    slave_node.c
  Description: TDMA Slave Node Implementation.
               Responds to discovery, receives slot, and sends sensor data
               in its assigned time slot.
***********************************************************************************/

#include "basic_rf.h"
#include "hal_board.h"
#include "hal_led.h"
#include "hal_mcu.h"
#include "hal_rf.h"
#include "hal_uart.h"
#include <stdlib.h> // For rand()
#include <string.h> // For strlen, memset

#if (chip == 2530 || chip == 2531)
#include <ioCC2530.h>
#endif

// 包含共享的协议定义
#include "tdma_protocol.h"

/***********************************************************************************
 * NODE CONFIGURATION - !!! 为每个从节点修改此ID !!!
 */
#define NODE_ID 2 // <-- 例如，这是2号从节点。为其他板修改为 3, 15 等。
/***********************************************************************************
 */

// DHT11 data pin definition
#define DATA_PIN P1_3

/***********************************************************************************
 * LOCAL VARIABLES
 */
static basicRfCfg_t basicRfConfig;
static uint8 rxBuffer[128];

// 从节点状态变量
static uint8 mySlot = 0xFF; // 0xFF 表示尚未分配时隙
static volatile uint32 lastSyncTimestamp = 0;

// DHT11 传感器变量
static uint8 uchartemp;
static uint8 ucharT_data_H, ucharT_data_L, ucharRH_data_H, ucharRH_data_L,
    ucharcheckdata;
static uint8 ucharcomdata;
static uint16 temperature = 0, humidity = 0;

/***********************************************************************************
 * HELPER & DEBUG FUNCTIONS
 */
static void debugPrint(const char *str) {
  halUartWrite((uint8 *)str, strlen(str));
}

static void debugPrintValue(const char *label, uint32 value) {
  //  Create a sufficiently large static buffer to construct the complete
  //  message
  // Static variables can prevent the allocation of large arrays on the stack
  // with each call
  char msg_buf[80];
  char *p = msg_buf;

  // 1. copy label to buffer
  while (*label) {
    *p++ = *label++;
  }

  // 2. convert num and append to buffer
  char val_buf[12];
  char *v = val_buf + 10;
  *v = '\0';
  if (value == 0) {
    *--v = '0';
  } else {
    while (value > 0) {
      *--v = (value % 10) + '0';
      value /= 10;
    }
  }
  while (*v) {
    *p++ = *v++;
  }

  // 3. append newline
  *p++ = '\r';
  *p++ = '\n';
  *p = '\0'; // end of string

  // 4. automatic send data
  halUartWrite((uint8 *)msg_buf, p - msg_buf);
  halMcuWaitMs(10);
}

// Temperature and humidity data reading
static void COM(void) {
  // DHT11 communication function implementation
  uint8 i;
  for (i = 0; i < 8; i++) {
    // Wait for DATA_PIN to go high,stand for reading to send data
    while (!DATA_PIN)
      ;
    // wait pass low signal
    halMcuWaitUs(10);
    halMcuWaitUs(10);
    halMcuWaitUs(10);
    halMcuWaitUs(10);
    uchartemp = 0;

    // if the high signal lasts for more than 40us, it is a 1
    if (DATA_PIN) {
      uchartemp = 1;
    }
    // ucharFLAG = 2;
   
    // wait next bit signal,wait for DATA_PIN to go low
    // while (DATA_PIN)
    //   ;
    
    // use type uchar of 8bits to store the data
    ucharcomdata <<= 1;
    ucharcomdata |= uchartemp;
  }
}
static void DHT11(void) {

  // set p1.3 as output for dht11
  P1DIR |= 0x08;
  DATA_PIN = 0;




  //   Delay_ms(19); //>18MS
  halMcuWaitMs(20);
  DATA_PIN = 1;

  halMcuWaitUs(10);
  halMcuWaitUs(10);
  halMcuWaitUs(10);
  halMcuWaitUs(10);

  // set p1.3 as input for DHT11 ,wait for response

  P1DIR &= ~0x08;

  
// Start timer for 1ms tick
 uint16 timeout_cnt = 0;
 
  // wait for DHT11 to respond
  halMcuWaitUs(30);
  // DHT11 response check

  //  If DATA_PIN is low, it means DHT11 is ready to send data

  if (!DATA_PIN) {
    //wait for DATA_PIN to go high
    while (!DATA_PIN)
    {
      
    }
                 
    while (DATA_PIN) // Wait for DATA_PIN to go low
    {
    }
      ;

    // DHT11 start to send data

    // total 5 COMS up to 40bits
    // first Humidity ,then temperature ,each 16bits
    // last 8bits is check data

    // a COM refer 8bits

    // humidity data
    COM();
    ucharRH_data_H = ucharcomdata;
    COM();
    ucharRH_data_L = ucharcomdata;
    // temperature data
    COM();
    ucharT_data_H = ucharcomdata;
    COM();
    ucharT_data_L = ucharcomdata;

    // check data
    COM();
    ucharcheckdata = ucharcomdata;

    // combine the data to check if it is valid
    //  data is valid if the sum of temperature and humidity data equals check
    //  data

    if ((ucharT_data_H + ucharT_data_L + ucharRH_data_H + ucharRH_data_L) ==
        ucharcheckdata) {

      // use 16bits to store temperature and humidity

      temperature = (ucharT_data_H / 10) * 10 +
                    (ucharT_data_L % 10); // Combine high and low byte
      humidity = (ucharRH_data_H / 10) * 10 +
                 (ucharRH_data_L % 10); // Combine high and low byte
    }
  }

  P1DIR |= 0x08; // set p1.3 as output for DHT11,set 1.3 equal to 1
                 // set p1.3 equal to 1,for next DHT11 reading
  DATA_PIN = 1;
}

/***********************************************************************************
 * SENSOR INITIALIZATION & READING
 */
static void sensorsInit(void) {
  P1SEL &= ~0x04; // 0100 SET p1.2 as I/O mode
  P1DIR &= ~0x04; // P1.2 as input for vibration
  P1SEL &= ~0x08; // 1000 SET p1.3 as I/O mode
  P1DIR &= ~0x08; // P1.3 as input for DHT11
  P2SEL &= ~0x01; // 0001 SET p2.0 as I/O mode
  // P2.0 as output for DHT11
  P2DIR |= 0x01;
  P2_0 = 0; // P2.0 as GND for DHT11
  debugPrint("SLAVE: Sensors initialized.\r\n");
}
static uint16 readTemperature(void) {
  DHT11();
  return temperature;
}
static uint16 readVibration(void) {
  return (P1_2) ? 0 : 1; // P1.2 low = vibration
}

/***********************************************************************************
 * CORE PROTOCOL FUNCTIONS
 */
static void slave_respond_to_roll_call(void) {
  // 随机延时防碰撞

  halMcuWaitMs(rand() % 500);
  RollCallRspPacket rsp;
  rsp.hdr.type = PKT_TYPE_ROLL_CALL_RSP;
  rsp.hdr.src_id = NODE_ID;
  basicRfSendPacket(MASTER_ID, (uint8 *)&rsp, sizeof(rsp));
  debugPrint("SLAVE: Sent Roll Call Response.\r\n");
}

static void slave_process_slot_allocation(uint8 *pRcv) {
  SlotAllocPacket *pkt = (SlotAllocPacket *)pRcv;
  for (int i = 0; i < pkt->num_slaves; i++) {
    if (pkt->slave_ids[i] == NODE_ID) {
      mySlot = pkt->slave_slots[i];
      // debugPrintValue("SLAVE: I have been assigned Slot ", mySlot);
      return;
    }
  }
}

static void slave_send_sensor_data(void) {
  SensorDataPacket pkt;
  pkt.hdr.type = PKT_TYPE_SENSOR_DATA;
  pkt.hdr.src_id = NODE_ID;
  pkt.temperature = readTemperature();
  pkt.vibration = readVibration();
  basicRfSendPacket(MASTER_ID, (uint8 *)&pkt, sizeof(pkt));
  debugPrintValue("SLAVE: Sent sensor data size:", sizeof(pkt));
}

/***********************************************************************************
 * APPLICATION LOGIC
 */
void slave_app() {
  // --- Discovery & Slot Allocation Phase ---
  while (mySlot == 0xFF) {
    WDCTL = 0xA0;
    WDCTL = 0x50;
    if (basicRfPacketIsReady()) {
      if (basicRfReceive(rxBuffer, 128, NULL) > 0) {
        PacketHeader *hdr = (PacketHeader *)rxBuffer;
        if (hdr->type == PKT_TYPE_ROLL_CALL_REQ) {
          slave_respond_to_roll_call();
        } else if (hdr->type == PKT_TYPE_SLOT_ALLOC) {
          slave_process_slot_allocation(rxBuffer);
        }
      }
    }
    halMcuWaitMs(10);
  }

  // --- Synchronized TDMA Phase ---
  debugPrint("\n--- SLAVE: Entering Normal Operation ---\n");
  // 初始状态为0，表示未同步
  uint8 networkSynced = 0;

  while (1) {
    WDCTL = 0xA0;
    WDCTL = 0x50;
    halLedClear(4); // 等待同步时关闭LED4

    // 一直监听是否有响应，只有接收的信息可以更改networkSynced状态

    if (basicRfPacketIsReady()) {
      if (basicRfReceive(rxBuffer, 128, NULL) > 0) {
        // 说明数据包是同步信标
        if (((PacketHeader *)rxBuffer)->type == PKT_TYPE_SYNC_BEACON) {

          // 说明数据包是同步信标
          SyncBeaconPacket *beacon = (SyncBeaconPacket *)rxBuffer;

          // 如果当前时隙是自己的，说明同步成功
          if (beacon->current_slot == mySlot) {
            networkSynced = 1;
          } else {
            networkSynced = 0; // 如果不是自己的时隙，则不同步
          }
          debugPrintValue("SLAVE: Sync Beacon received, current slot: ",
                          beacon->current_slot);
          halLedSet(4); // 同步成功点亮LED4
        }
      }
    }
    halMcuWaitMs(10);

    // 如果同步成功，则发送数据包
    if (networkSynced) {

      // set led1 flicker standing for sending data
      halLedSet(1);      // 点亮LED1表示正在发送数据
      halMcuWaitMs(100); // 等待LED亮起

      slave_send_sensor_data();

      halLedClear(1);

      // 发送后延时，避免发送过快

      halMcuWaitMs(100);
    } else {
      // 如果未同步，等待下一个同步信标
      debugPrint("SLAVE: Waiting for sync beacon...\r\n");
      halMcuWaitMs(300);

      halLedClear(4); // 关闭LED4表示未同步
    }
  }
}

/***********************************************************************************
 * MAIN
 */



void main(void) {
  WDCTL = 0x00;
  halBoardInit();
  halUartInit(115200);
  // debugPrintValue("Role: SLAVE, ID: ", NODE_ID);

  basicRfConfig.panId = PAN_ID;
  basicRfConfig.channel = RF_CHANNEL;
  basicRfConfig.ackRequest = FALSE;
  basicRfConfig.myAddr = NODE_ID;

  if (basicRfInit(&basicRfConfig) == FAILED) {
    while (1)
      ;
  }
  basicRfReceiveOn();

  P1DIR |= 0x1B; // LEDs as output
  sensorsInit();
  srand(NODE_ID);

  WDCTL = 0xAC;

  debugPrint("Initialization complete. Starting application...\r\n");
  slave_app();
}
