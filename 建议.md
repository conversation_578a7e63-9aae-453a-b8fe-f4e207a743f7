主从节点的宏定义：
当前的宏定义让从节点在没有主节点的情况下也能正常运行。确保在使用#define SLAVE_NODE 时，从节点能等到主节点的同步信号（即接收到 beacon 帧），以正确配置 Timer3。
// Uncomment ONE of the following:
#define MASTER_NODE // Master node configuration
// #define SLAVE_NODE // Slave node configuration
可以将#define SLAVE_NODE 放入#ifndef MASTER_NODE 条件内，以确保只会有一个节点被定义。
从节点的 Timer3 配置：
在 tdmaProcessBeacon() 函数中，你正确地更新了 Timer3 的计数器（timerTestCount），但是要确保 netowrkSynced 只在成功接收到有效 beacon 后才被设置为 TRUE。此逻辑应该已经实现，但要确保数据包处理逻辑正确。
// Example: Process received beacon frame (slave node)
static void tdmaProcessBeacon(uint8 *data, uint8 length) {
    ...
    if (beacon->header.frameType == FRAME_TYPE_BEACON &&
        beacon->header.nodeId == 0) { // Verify it's from the master
        ...
        networkSynced = TRUE; // Only set this after successfully processing the beacon
    }
}
Slot 定时和同步：
从节点的 tdmaWaitForSlot() 函数确实考虑了网络同步。如果未同步，采用软件定时，确保从节点通过 recv 来确认 beacon 包。
// Wait for specific slot
// If not synchronized
if (!networkSynced) {
    halUartWrite("Listening for master beacon (software timing)\r\n");
    halMcuWaitMs(20); // Use software timing
}
运行状态监控：

在主节点 masterNodeApp() 中，你可以添加更多调试信息来确认 beacon 发送与接收状态，尤其是在从节点的接收逻辑中。

----

 === TDMA System (Optimized Design) ===
[21:56:39] [SERIAL] Role: MASTER, ID: 1
[21:56:39] [SERIAL] System initialized successfully.
[21:56:39] [SERIAL] MASTER: Discovered new slave, ID=3
[21:56:44] [SERIAL] MASTER: Allocating slots...
[21:56:44] [SERIAL] - Slave ID 3
[21:56:44] [SERIAL] gets Slot 1