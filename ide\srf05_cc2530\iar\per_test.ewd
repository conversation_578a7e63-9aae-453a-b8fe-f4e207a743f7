<?xml version="1.0" encoding="iso-8859-1"?>

<project>
  <fileVersion>2</fileVersion>
  <configuration>
    <name>srf05_cc2530</name>
    <toolchain>
      <name>8051</name>
    </toolchain>
    <debug>1</debug>
    <settings>
      <name>C-SPY</name>
      <archiveVersion>2</archiveVersion>
      <data>
        <version>8</version>
        <wantNonLocal>1</wantNonLocal>
        <debug>1</debug>
        <option>
          <name>CInput</name>
          <state>1</state>
        </option>
        <option>
          <name>MacOverride</name>
          <state>0</state>
        </option>
        <option>
          <name>MacFile</name>
          <state></state>
        </option>
        <option>
          <name>GoToEnable</name>
          <state>1</state>
        </option>
        <option>
          <name>GoToName</name>
          <state>main</state>
        </option>
        <option>
          <name>MemOverride</name>
          <state>1</state>
        </option>
        <option>
          <name>OCProcessor</name>
          <state>1</state>
        </option>
        <option>
          <name>d24BitData</name>
          <state>1</state>
        </option>
        <option>
          <name>Debugger code model</name>
          <state>1</state>
        </option>
        <option>
          <name>OCNrOfVirtualRegisters</name>
          <state>1</state>
        </option>
        <option>
          <name>Sim extended stack</name>
          <state>1</state>
        </option>
        <option>
          <name>Debugger DPTR Settings</name>
          <state>1</state>
        </option>
        <option>
          <name>Debugger Code Banking</name>
          <state>1</state>
        </option>
        <option>
          <name>DebuggerMandatory</name>
          <state>1</state>
        </option>
        <option>
          <name>DynDriver</name>
          <state>CHIPCON_ID</state>
        </option>
        <option>
          <name>Debugger Extra Options Check</name>
          <state>0</state>
        </option>
        <option>
          <name>Debugger Extra Options Edit</name>
          <state></state>
        </option>
        <option>
          <name>Debugger data model</name>
          <state>1</state>
        </option>
        <option>
          <name>OCImagesSuppressCheck1</name>
          <state>0</state>
        </option>
        <option>
          <name>OCImagesPath1</name>
          <state></state>
        </option>
        <option>
          <name>OCImagesSuppressCheck2</name>
          <state>0</state>
        </option>
        <option>
          <name>OCImagesPath2</name>
          <state></state>
        </option>
        <option>
          <name>OCImagesSuppressCheck3</name>
          <state>0</state>
        </option>
        <option>
          <name>OCImagesPath3</name>
          <state></state>
        </option>
        <option>
          <name>DdfFile slave</name>
          <state>1</state>
        </option>
        <option>
          <name>DdfFile master</name>
          <state>$TOOLKIT_DIR$\config\devices\_generic\io8052.ddf</state>
        </option>
        <option>
          <name>OCImagesOffset1</name>
          <state></state>
        </option>
        <option>
          <name>OCImagesOffset2</name>
          <state></state>
        </option>
        <option>
          <name>OCImagesOffset3</name>
          <state></state>
        </option>
        <option>
          <name>OCImagesUse1</name>
          <state>0</state>
        </option>
        <option>
          <name>OCImagesUse2</name>
          <state>0</state>
        </option>
        <option>
          <name>OCImagesUse3</name>
          <state>0</state>
        </option>
        <option>
          <name>Exclude Exit Breakpoint</name>
          <state>1</state>
        </option>
        <option>
          <name>Exclude Putchar Breakpoint</name>
          <state>0</state>
        </option>
        <option>
          <name>Exclude Getchar Breakpoint</name>
          <state>0</state>
        </option>
      </data>
    </settings>
    <settings>
      <name>_3RD_ID</name>
      <archiveVersion>1</archiveVersion>
      <data>
        <version>0</version>
        <wantNonLocal>1</wantNonLocal>
        <debug>1</debug>
        <option>
          <name>Third-Party Driver Mandatory</name>
          <state>1</state>
        </option>
        <option>
          <name>Third-Party Driver File Name Edit</name>
          <state>ThirdPartyDriver.dll</state>
        </option>
        <option>
          <name>Third-Party Driver LogFile Check</name>
          <state>0</state>
        </option>
        <option>
          <name>Third-Party Driver LogFile Edit</name>
          <state>cspycomm.log</state>
        </option>
      </data>
    </settings>
    <settings>
      <name>CHIPCON_ID</name>
      <archiveVersion>2</archiveVersion>
      <data>
        <version>3</version>
        <wantNonLocal>1</wantNonLocal>
        <debug>1</debug>
        <option>
          <name>ChipconDriverMandatory</name>
          <state>1</state>
        </option>
        <option>
          <name>ChipconEraseFlash</name>
          <state>0</state>
        </option>
        <option>
          <name>ChipconRetainMemory</name>
          <state>1</state>
        </option>
        <option>
          <name>ChipconSuppressDownload</name>
          <state>0</state>
        </option>
        <option>
          <name>ChipconVerifyDownload</name>
          <state>1</state>
        </option>
        <option>
          <name>ChipconVerifyRadio</name>
          <state>0</state>
        </option>
        <option>
          <name>ChipconReduceSpeed</name>
          <state>0</state>
        </option>
        <option>
          <name>ChipconStackOverflow</name>
          <state>1</state>
        </option>
        <option>
          <name>ChipconNoBanks</name>
          <version>0</version>
          <state>2</state>
        </option>
        <option>
          <name>ChipconLogFileCheck</name>
          <state>0</state>
        </option>
        <option>
          <name>ChipconLogComFile</name>
          <state>communication.log</state>
        </option>
        <option>
          <name>ChipconFlashLock</name>
          <version>0</version>
          <state>0</state>
        </option>
        <option>
          <name>ChipconFlashLockInfo</name>
          <state>&lt;page size info. missing></state>
        </option>
        <option>
          <name>ChipconBootLock</name>
          <state>0</state>
        </option>
        <option>
          <name>ChipconDebugLock</name>
          <state>0</state>
        </option>
        <option>
          <name>ChipconLockFlash</name>
          <state>0</state>
        </option>
        <option>
          <name>ChipconLockLabel</name>
          <state>0</state>
        </option>
        <option>
          <name>ChipconRetainPagesCtrl</name>
          <state>0</state>
        </option>
        <option>
          <name>ChipconRetainPages</name>
          <state></state>
        </option>
        <option>
          <name>ChipconFlashPages</name>
          <state></state>
        </option>
        <option>
          <name>ChipconFlashRadio</name>
          <state>0</state>
        </option>
      </data>
    </settings>
    <settings>
      <name>FS2_ID</name>
      <archiveVersion>1</archiveVersion>
      <data>
        <version>0</version>
        <wantNonLocal>1</wantNonLocal>
        <debug>1</debug>
        <option>
          <name>Fs2DriverMandatory</name>
          <state>1</state>
        </option>
        <option>
          <name>Configuration</name>
          <version>0</version>
          <state>0</state>
        </option>
        <option>
          <name>Has program RAM</name>
          <state>0</state>
        </option>
        <option>
          <name>Program RAM areas</name>
          <state>0x8000-0x87FF,0xC000-0xC7FF</state>
        </option>
        <option>
          <name>Has program Flash</name>
          <state>0</state>
        </option>
        <option>
          <name>Program Flash cfg entry</name>
          <state>nRF24LU1</state>
        </option>
        <option>
          <name>Program Flash areas</name>
          <state>0x0000-0x7FFF</state>
        </option>
        <option>
          <name>FS2SuppressDownload</name>
          <state>0</state>
        </option>
        <option>
          <name>FS2VerifyDownload</name>
          <state>0</state>
        </option>
      </data>
    </settings>
    <settings>
      <name>INFINEON_ID</name>
      <archiveVersion>1</archiveVersion>
      <data>
        <version>1</version>
        <wantNonLocal>1</wantNonLocal>
        <debug>1</debug>
        <option>
          <name>InfineonDriverMandatory</name>
          <state>1</state>
        </option>
        <option>
          <name>InfineonEraseFlash</name>
          <state>0</state>
        </option>
        <option>
          <name>InfineonSuppressDownload</name>
          <state>0</state>
        </option>
        <option>
          <name>InfineonVerifyDownload</name>
          <state>0</state>
        </option>
        <option>
          <name>InfServerAddr</name>
          <state>localhost</state>
        </option>
        <option>
          <name>InfKey1</name>
          <state>0</state>
        </option>
        <option>
          <name>InfKey2</name>
          <state>0</state>
        </option>
        <option>
          <name>InfKey3</name>
          <state>0</state>
        </option>
        <option>
          <name>InfKey4</name>
          <state>0</state>
        </option>
        <option>
          <name>InfConnection</name>
          <state>0</state>
        </option>
        <option>
          <name>InfineonSwBp</name>
          <state>0</state>
        </option>
        <option>
          <name>InfServerName2</name>
          <version>0</version>
          <state>3</state>
        </option>
      </data>
    </settings>
    <settings>
      <name>NS_ID</name>
      <archiveVersion>1</archiveVersion>
      <data>
        <version>0</version>
        <wantNonLocal>1</wantNonLocal>
        <debug>1</debug>
        <option>
          <name>NsDriverMandatory</name>
          <state>1</state>
        </option>
        <option>
          <name>NSSuppressDownload</name>
          <state>0</state>
        </option>
        <option>
          <name>NSVerifyDownload</name>
          <state>0</state>
        </option>
      </data>
    </settings>
    <settings>
      <name>ROM_ID</name>
      <archiveVersion>1</archiveVersion>
      <data>
        <version>2</version>
        <wantNonLocal>1</wantNonLocal>
        <debug>1</debug>
        <option>
          <name>RomDriverMandatory</name>
          <state>1</state>
        </option>
        <option>
          <name>SuppressLoad</name>
          <state>0</state>
        </option>
        <option>
          <name>VerifyDownload</name>
          <state>0</state>
        </option>
        <option>
          <name>AllComm</name>
          <state>1</state>
        </option>
        <option>
          <name>Port</name>
          <version>0</version>
          <state>0</state>
        </option>
        <option>
          <name>Baud</name>
          <version>0</version>
          <state>6</state>
        </option>
        <option>
          <name>Parity</name>
          <version>0</version>
          <state>0</state>
        </option>
        <option>
          <name>DataBits</name>
          <version>0</version>
          <state>0</state>
        </option>
        <option>
          <name>StopBits</name>
          <version>0</version>
          <state>0</state>
        </option>
        <option>
          <name>Handshake</name>
          <version>0</version>
          <state>0</state>
        </option>
        <option>
          <name>DoLogfile</name>
          <state>0</state>
        </option>
        <option>
          <name>LogFile</name>
          <state>cspycomm.log</state>
        </option>
        <option>
          <name>ToggleDTR</name>
          <state>0</state>
        </option>
        <option>
          <name>ToggleRTS</name>
          <state>0</state>
        </option>
      </data>
    </settings>
    <settings>
      <name>AD2_ID</name>
      <archiveVersion>2</archiveVersion>
      <data>
        <version>6</version>
        <wantNonLocal>1</wantNonLocal>
        <debug>1</debug>
        <option>
          <name>CygnalDriverMandatory</name>
          <state>1</state>
        </option>
        <option>
          <name>CygnVerifyDownload</name>
          <state>0</state>
        </option>
        <option>
          <name>Port</name>
          <version>0</version>
          <state>0</state>
        </option>
        <option>
          <name>Baud</name>
          <version>0</version>
          <state>6</state>
        </option>
        <option>
          <name>CygnComm</name>
          <state>1</state>
        </option>
        <option>
          <name>ADuC8xx</name>
          <state>1</state>
        </option>
        <option>
          <name>ADuCpuClockFrequency</name>
          <state>12582912</state>
        </option>
        <option>
          <name>OverrideCpuClkFreq</name>
          <state>0</state>
        </option>
        <option>
          <name>AD2EraseDataFlash</name>
          <state>1</state>
        </option>
        <option>
          <name>Debug Interface</name>
          <state>0</state>
        </option>
      </data>
    </settings>
    <settings>
      <name>CYGNAL_ID</name>
      <archiveVersion>1</archiveVersion>
      <data>
        <version>1</version>
        <wantNonLocal>1</wantNonLocal>
        <debug>1</debug>
        <option>
          <name>CygnalDriverMandatory</name>
          <state>1</state>
        </option>
        <option>
          <name>CygnSuppressLoad</name>
          <state>0</state>
        </option>
        <option>
          <name>CygnVerifyDownload</name>
          <state>0</state>
        </option>
        <option>
          <name>CygnProtocol</name>
          <state>0</state>
        </option>
        <option>
          <name>Port</name>
          <version>0</version>
          <state>0</state>
        </option>
        <option>
          <name>Baud</name>
          <version>0</version>
          <state>4</state>
        </option>
        <option>
          <name>CygnComm</name>
          <state>1</state>
        </option>
        <option>
          <name>drv_silabs_page_size</name>
          <state>0</state>
        </option>
        <option>
          <name>SilabsUsb</name>
          <state>0</state>
        </option>
        <option>
          <name>SilabsPowerTarget</name>
          <state>0</state>
        </option>
        <option>
          <name>SilabsMulDevices</name>
          <state>0</state>
        </option>
        <option>
          <name>SilabsDevBefore</name>
          <state>0</state>
        </option>
        <option>
          <name>SilabsDevAfter</name>
          <state>0</state>
        </option>
        <option>
          <name>SilabsRegBefore</name>
          <state>0</state>
        </option>
        <option>
          <name>SilabsRegAfter</name>
          <state>0</state>
        </option>
      </data>
    </settings>
    <settings>
      <name>SIM_ID</name>
      <archiveVersion>1</archiveVersion>
      <data>
        <version>2</version>
        <wantNonLocal>1</wantNonLocal>
        <debug>1</debug>
        <option>
          <name>SimDriverMandatory</name>
          <state>1</state>
        </option>
        <option>
          <name>SimEnablePSP</name>
          <state>0</state>
        </option>
        <option>
          <name>SimPspOverrideConfig</name>
          <state>0</state>
        </option>
        <option>
          <name>SimPspConfigFile</name>
          <state>###Uninitialized###</state>
        </option>
      </data>
    </settings>
    <debuggerPlugins>
      <plugin>
        <file>$EW_DIR$\common\plugins\CodeCoverage\CodeCoverage.ENU.ewplugin</file>
        <loadFlag>1</loadFlag>
      </plugin>
      <plugin>
        <file>$EW_DIR$\common\plugins\Orti\Orti.ENU.ewplugin</file>
        <loadFlag>0</loadFlag>
      </plugin>
      <plugin>
        <file>$EW_DIR$\common\plugins\Stack\Stack.ENU.ewplugin</file>
        <loadFlag>1</loadFlag>
      </plugin>
      <plugin>
        <file>$EW_DIR$\common\plugins\SymList\SymList.ENU.ewplugin</file>
        <loadFlag>1</loadFlag>
      </plugin>
    </debuggerPlugins>
  </configuration>
</project>


