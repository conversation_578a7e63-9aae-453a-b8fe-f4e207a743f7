#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
TDMA监控系统中文显示修复工具
自动检测和修复matplotlib中文字体显示问题
"""

import sys
import os

def check_and_fix_chinese_font():
    """检查并修复中文字体问题"""
    print("🔧 TDMA监控系统中文显示修复工具")
    print("=" * 50)
    
    # 步骤1: 检查matplotlib
    print("\n1. 检查matplotlib安装...")
    try:
        import matplotlib
        import matplotlib.pyplot as plt
        import matplotlib.font_manager as fm
        print(f"   ✓ matplotlib版本: {matplotlib.__version__}")
    except ImportError:
        print("   ✗ matplotlib未安装")
        print("   修复方法: pip install matplotlib")
        return False
    
    # 步骤2: 检查中文字体
    print("\n2. 检查系统中文字体...")
    available_fonts = [f.name for f in fm.fontManager.ttflist]
    
    # 推荐的中文字体列表
    recommended_fonts = {
        'SimHei': '黑体 (Windows)',
        'Microsoft YaHei': '微软雅黑 (Windows)', 
        'PingFang SC': '苹方 (macOS)',
        'Hiragino Sans GB': '冬青黑体 (macOS)',
        'WenQuanYi Micro Hei': '文泉驿微米黑 (Linux)',
        'Noto Sans CJK SC': '思源黑体 (Linux)',
        'SimSun': '宋体 (Windows)',
    }
    
    found_fonts = []
    for font_name, description in recommended_fonts.items():
        if font_name in available_fonts:
            found_fonts.append(font_name)
            print(f"   ✓ {description}")
        else:
            print(f"   ✗ {description}")
    
    if not found_fonts:
        print("   ⚠ 未找到推荐的中文字体")
        show_font_install_guide()
        return False
    
    # 步骤3: 配置matplotlib
    print("\n3. 配置matplotlib中文字体...")
    try:
        # 选择最佳字体
        best_font = found_fonts[0]
        
        # 配置字体
        plt.rcParams['font.sans-serif'] = [best_font] + list(recommended_fonts.keys())
        plt.rcParams['axes.unicode_minus'] = False
        
        print(f"   ✓ 已配置字体: {best_font}")
        print("   ✓ 负号显示问题已修复")
        
    except Exception as e:
        print(f"   ✗ 字体配置失败: {e}")
        return False
    
    # 步骤4: 测试中文显示
    print("\n4. 测试中文显示...")
    try:
        # 创建简单测试
        fig, ax = plt.subplots(figsize=(8, 6))
        
        # 测试数据
        import numpy as np
        x = np.linspace(0, 10, 50)
        y = np.sin(x) * 25 + 25
        
        # 绘制图表
        ax.plot(x, y, 'b-', linewidth=2, label='温度数据')
        ax.set_title('TDMA传感器网络 - 温度监控')
        ax.set_xlabel('时间')
        ax.set_ylabel('温度 (°C)')
        ax.legend()
        ax.grid(True, alpha=0.3)
        
        # 保存测试图片
        test_file = 'chinese_display_test.png'
        plt.savefig(test_file, dpi=150, bbox_inches='tight')
        plt.close()
        
        print(f"   ✓ 测试图片已保存: {test_file}")
        print("   ✓ 请检查图片中的中文是否显示正常")
        
        return True
        
    except Exception as e:
        print(f"   ✗ 测试失败: {e}")
        return False

def show_font_install_guide():
    """显示字体安装指南"""
    import platform
    system = platform.system()
    
    print("\n📖 字体安装指南")
    print("-" * 30)
    
    if system == "Windows":
        print("Windows系统:")
        print("1. 下载思源黑体:")
        print("   https://github.com/adobe-fonts/source-han-sans/releases")
        print("2. 解压后双击.otf文件安装")
        print("3. 或复制字体文件到: C:\\Windows\\Fonts\\")
        
    elif system == "Darwin":  # macOS
        print("macOS系统:")
        print("1. 使用Homebrew安装:")
        print("   brew tap homebrew/cask-fonts")
        print("   brew install font-source-han-sans")
        print("2. 或从App Store安装字体应用")
        
    elif system == "Linux":
        print("Linux系统:")
        print("Ubuntu/Debian:")
        print("   sudo apt update")
        print("   sudo apt install fonts-wqy-microhei fonts-noto-cjk")
        print("CentOS/RHEL:")
        print("   sudo yum install wqy-microhei-fonts")
        print("Arch Linux:")
        print("   sudo pacman -S wqy-microhei noto-fonts-cjk")
        print("安装后运行: fc-cache -fv")
    
    print("\n安装字体后请重新运行此工具进行测试")

def create_font_config_file():
    """创建字体配置文件"""
    config_content = '''# matplotlib中文字体配置
import matplotlib.pyplot as plt

# 配置中文字体
plt.rcParams['font.sans-serif'] = [
    'SimHei',           # 黑体 (Windows)
    'Microsoft YaHei',  # 微软雅黑 (Windows)
    'PingFang SC',      # 苹方 (macOS)
    'Hiragino Sans GB', # 冬青黑体 (macOS)
    'WenQuanYi Micro Hei', # 文泉驿微米黑 (Linux)
    'Noto Sans CJK SC', # 思源黑体 (Linux)
    'DejaVu Sans',      # 备用字体
    'sans-serif'        # 系统默认
]

# 解决负号显示问题
plt.rcParams['axes.unicode_minus'] = False

print("matplotlib中文字体配置已加载")
'''
    
    try:
        with open('matplotlib_chinese_config.py', 'w', encoding='utf-8') as f:
            f.write(config_content)
        print("   ✓ 已创建字体配置文件: matplotlib_chinese_config.py")
        print("   ✓ 可以在其他项目中导入此配置")
        return True
    except Exception as e:
        print(f"   ✗ 创建配置文件失败: {e}")
        return False

def main():
    """主函数"""
    print("🚀 开始检测和修复中文显示问题...\n")
    
    # 检查和修复
    success = check_and_fix_chinese_font()
    
    if success:
        print("\n✅ 修复完成!")
        print("现在可以正常运行TDMA监控系统:")
        print("   python run_enhanced.py")
        
        # 创建配置文件
        print("\n5. 创建配置文件...")
        create_font_config_file()
        
    else:
        print("\n❌ 修复失败!")
        print("建议:")
        print("1. 按照指南安装中文字体")
        print("2. 重新运行此修复工具")
        print("3. 如果问题持续，可以使用简化版本:")
        print("   python simple_monitor.py")
    
    print("\n" + "=" * 50)
    return success

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n修复过程被用户中断")
    except Exception as e:
        print(f"\n\n修复过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
    
    input("\n按回车键退出...")
