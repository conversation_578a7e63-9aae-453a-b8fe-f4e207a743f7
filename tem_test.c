/***********************************************************************************
  Filename:    tdma_system_designed_by_ppt.c
  Description: TDMA Sensor Network Implementation based on GUET PPT.
               Implements Discovery, Slot Allocation, and Synchronized Data
Phases.

  Configuration:
  - Uncomment #define MASTER_NODE for master node
  - Uncomment #define SLAVE_NODE for slave node
  - Set a UNIQUE NODE_ID for each slave node (e.g., 2, 3, 15...).
***********************************************************************************/

/***********************************************************************************
 * INCLUDES
 */
#include "basic_rf.h"
#include "hal_board.h"
#include "hal_led.h"
#include "hal_mcu.h"
#include "hal_rf.h"
#include "hal_uart.h"
#include <stdlib.h> // For rand()
#include <string.h> // For strlen, memset

#if (chip == 2530 || chip == 2531)
#include <ioCC2530.h>
#endif

/***********************************************************************************
 * DHT11 SENSOR DEFINITIONS AND VARIABLES
 */
// DHT11 data pin definition (changed from P0_7 to P1_3)
#define DATA_PIN P1_3

// DHT11 type definitions
typedef unsigned char uchar;
typedef unsigned int uint;

// DHT11 global variables
static uchar ucharFLAG, uchartemp;
static uchar shidu_shi, shidu_ge, wendu_shi, wendu_ge = 4;
static uchar ucharT_data_H, ucharT_data_L, ucharRH_data_H, ucharRH_data_L,
    ucharcheckdata;
static uchar ucharT_data_H_temp, ucharT_data_L_temp, ucharRH_data_H_temp,
    ucharRH_data_L_temp, ucharcheckdata_temp;
static uchar ucharcomdata;

/***********************************************************************************
 * NODE CONFIGURATION
 */
// #define MASTER_NODE
#define SLAVE_NODE

#ifdef MASTER_NODE
#ifdef SLAVE_NODE
#error "Cannot define both MASTER_NODE and SLAVE_NODE"
#endif
#define NODE_ID 1
#else
#ifndef SLAVE_NODE
#error "You must define either MASTER_NODE or SLAVE_NODE"
#endif
#define NODE_ID 2
#endif

/***********************************************************************************
 * SYSTEM CONFIGURATION - Easily Configurable Parameters
 */

// === Network Configuration ===
#define MASTER_ADDR 1
#define RF_CHANNEL 25
#define PAN_ID 0x2024
#define BROADCAST_ADDR 0xFFFF

// === TDMA System Configuration ===
// You can easily modify these parameters to suit your system requirements

// Maximum number of slave nodes (can be 1-20)
#ifndef MAX_SLAVES
// Default: support up to 5 slave nodes
#define MAX_SLAVES 5
#endif

// TDMA timing parameters (all in milliseconds)
#ifndef SUPERFRAME_PERIOD_MS
// Default: 1 second superframe
#define SUPERFRAME_PERIOD_MS 1000
#endif

#ifndef SLOT_DURATION_MS
// Default: 50ms per slot (adjustable: 20-100ms)
#define SLOT_DURATION_MS 50
#endif

// Discovery phase duration (how long to wait for slave responses)
#ifndef DISCOVERY_DURATION_MS
// Default: 5 seconds
#define DISCOVERY_DURATION_MS 5000
#endif

// Periodic rediscovery interval (how often to search for new nodes)
#ifndef REDISCOVERY_INTERVAL_MS
// Default: 30 seconds
#define REDISCOVERY_INTERVAL_MS 30000
#endif

// === Calculated Parameters (DO NOT MODIFY) ===
#define MAX_SLOTS (1 + MAX_SLAVES) // 1 for beacon, rest for slaves
#define BEACON_SLOT 0              // Slot 0 is always for the master's beacon
#define SLOT_ALLOC_START_NUM 1     // Slave slots start from 1
#define MIN_SUPERFRAME_TIME                                                    \
  (MAX_SLOTS * SLOT_DURATION_MS) // Minimum time needed

// === Packet Types ===
#define PKT_TYPE_ROLL_CALL_REQ                                                 \
  0x10 // Master requests nodes to identify themselves
#define PKT_TYPE_ROLL_CALL_RSP 0x11 // Slave responds to roll call
#define PKT_TYPE_SLOT_ALLOC 0x20    // Master broadcasts slot allocation table
#define PKT_TYPE_SYNC_BEACON 0x30   // Master broadcasts sync beacon
#define PKT_TYPE_SENSOR_DATA 0x40   // Slave sends sensor data
#define PKT_TYPE_CONFIG_REQ 0x50    // Configuration request
#define PKT_TYPE_CONFIG_RSP 0x51    // Configuration response

// === System Validation ===
#if SUPERFRAME_PERIOD_MS < MIN_SUPERFRAME_TIME
#error "SUPERFRAME_PERIOD_MS is too small for the configured number of slots!"
#endif

#if MAX_SLAVES > 20
#error "MAX_SLAVES cannot exceed 20 (hardware limitation)"
#endif

#if SLOT_DURATION_MS < 20
#error "SLOT_DURATION_MS cannot be less than 20ms (timing constraint)"
#endif

// Network State Machine
typedef enum {
  STATE_INIT,
  STATE_DISCOVERY,
  STATE_SLOT_ALLOCATION,
  STATE_SYNC_RUNNING
} NetworkState;

/***********************************************************************************
 * DATA STRUCTURES
 */
// Generic Packet Header
typedef struct {
  uint8 type;
  uint8 src_id;
} PacketHeader;

// Slave's response to a roll call
typedef struct {
  PacketHeader hdr;
} RollCallRspPacket;

// Master broadcasts allocated slots
typedef struct {
  PacketHeader hdr;
  uint8 slave_ids[MAX_SLAVES];
  uint8 slave_slots[MAX_SLAVES];
  uint8 num_slaves;
} SlotAllocPacket;

// Master's periodic synchronization beacon
typedef struct {
  PacketHeader hdr;
  uint32 timestamp;
} SyncBeaconPacket;

// Slave's sensor data packet
typedef struct {
  PacketHeader hdr;
  uint16 temperature;
  uint16 vibration;
} SensorDataPacket;

// Master's internal list of connected slaves
typedef struct {
  uint8 id;
  uint8 slot;
  uint8 used;
} SlaveInfo;

/***********************************************************************************
 * LOCAL VARIABLES
 */
static basicRfCfg_t basicRfConfig;
static uint8 txBuffer[128], rxBuffer[128];

// Common state variables
static volatile uint32 timer_ms_count = 0; // Millisecond system timer
static uint8 myNodeId = NODE_ID;

#ifdef MASTER_NODE
static SlaveInfo slaveList[MAX_SLAVES];
static NetworkState networkState = STATE_INIT;
static uint8 numSlavesFound = 0;
#endif

#ifdef SLAVE_NODE
static uint8 mySlot = 0xFF; // 0xFF means no slot assigned yet
static volatile uint8 networkSynced = FALSE;
static volatile uint32 lastSyncTimestamp = 0;
#endif

/***********************************************************************************
 * HELPER & DEBUG FUNCTIONS
 */
// UART print helpers
static void debugPrint(const char *str) {
  halUartWrite((uint8 *)str, strlen(str));
}
static void debugPrintValue(const char *label, uint32 value) {
  char buf[12];
  char *p = buf + 11;
  *p-- = '\0';
  if (value == 0) {
    *p = '0';
  } else {
    while (value > 0) {
      *p-- = (value % 10) + '0';
      value /= 10;
    }
  }
  debugPrint(label);
  debugPrint(p + 1);
  debugPrint("\r\n");
}

static void uart_flush_tx(void) {
  // U0CSR.ACTIVE is set when the UART is busy transmitting.
  // We wait until it becomes 0.
  while (U0CSR & 0x01)
    ;
}

// System configuration is now handled by compile-time macros only

/***********************************************************************************
 * DHT11 SENSOR FUNCTIONS
 */
// DHT11 delay functions
static void Delay_us(void) // 1 us延时
{
  asm("nop");
  asm("nop");
  asm("nop");
  asm("nop");
  asm("nop");
  asm("nop");
  asm("nop");
  asm("nop");
  asm("nop");
}

static void Delay_10us(void) // 10 us延时
{
  Delay_us();
  Delay_us();
  Delay_us();
  Delay_us();
  Delay_us();
  Delay_us();
  Delay_us();
  Delay_us();
  Delay_us();
  Delay_us();
}

static void Delay_ms_dht11(uint Time) // n ms延时 (renamed to avoid conflict)
{
  unsigned char i;
  while (Time--) {
    for (i = 0; i < 100; i++)
      Delay_10us();
  }
}

// DHT11 communication function
static void COM(void) // 温湿度读写
{
  uchar i;
  for (i = 0; i < 8; i++) {
    ucharFLAG = 2;
    while ((!DATA_PIN) && ucharFLAG++)
      ;
    Delay_10us();
    Delay_10us();
    Delay_10us();
    uchartemp = 0;
    if (DATA_PIN)
      uchartemp = 1;
    ucharFLAG = 2;
    while ((DATA_PIN) && ucharFLAG++)
      ;
    if (ucharFLAG == 1)
      break;
    ucharcomdata <<= 1;
    ucharcomdata |= uchartemp;
  }
}

// DHT11 main reading function
static void DHT11(void) // 温湿度传感器处理
{
  DATA_PIN = 0;
  Delay_ms_dht11(19); //>18MS
  DATA_PIN = 1;
  P1DIR &= ~0x08; // 设置数据线IO口方向 (P1.3 = bit 3)
  Delay_10us();
  Delay_10us();
  Delay_10us();
  Delay_10us();
  if (!DATA_PIN) {
    ucharFLAG = 2;
    while ((!DATA_PIN) && ucharFLAG++)
      ;
    ucharFLAG = 2;
    while ((DATA_PIN) && ucharFLAG++)
      ;
    COM();
    ucharRH_data_H_temp = ucharcomdata;
    COM();
    ucharRH_data_L_temp = ucharcomdata;
    COM();
    ucharT_data_H_temp = ucharcomdata;
    COM();
    ucharT_data_L_temp = ucharcomdata;
    COM();
    ucharcheckdata_temp = ucharcomdata;
    DATA_PIN = 1;
    uchartemp = (ucharT_data_H_temp + ucharT_data_L_temp + ucharRH_data_H_temp +
                 ucharRH_data_L_temp);
    if (uchartemp == ucharcheckdata_temp) {
      ucharRH_data_H = ucharRH_data_H_temp;
      ucharRH_data_L = ucharRH_data_L_temp;
      ucharT_data_H = ucharT_data_H_temp;
      ucharT_data_L = ucharT_data_L_temp;
      ucharcheckdata = ucharcheckdata_temp;
    }
    wendu_shi = ucharT_data_H / 10;
    wendu_ge = ucharT_data_H % 10;

    shidu_shi = ucharRH_data_H / 10;
    shidu_ge = ucharRH_data_H % 10;
  } else // 没有成功读取，设置为0
  {
    wendu_shi = 0;
    wendu_ge = 0;

    shidu_shi = 0;
    shidu_ge = 0;
  }

  P1DIR |= 0x08; // IO口需要设置为输出 (P1.3 = bit 3)
}

/***********************************************************************************
 * CORE PROTOCOL FUNCTIONS
 */
static void configure_timer3_for_ms_tick(void) {
  T3CTL = 0x00;   // Stop timer
  T3CTL |= 0x08;  // Enable interrupt
  T3IE = 1;       // Enable T3 interrupt in IEN1
  T3CTL |= 0xE0;  // Prescaler 128 (16MHz/128 = 125kHz)
  T3CTL &= ~0x03; // Auto-reload mode (Modulo)
  T3CC0 = 125;    // Compare value for 1ms tick (125 ticks at 125kHz)
  T3CTL |= 0x10;  // Start timer
  EA = 1;         // Enable global interrupts
  debugPrint("Timer3 configured for 1ms tick.\r\n");
}

#ifdef MASTER_NODE
// Sends a roll call request to discover slaves
static void master_send_roll_call(void) {
  PacketHeader req;
  req.type = PKT_TYPE_ROLL_CALL_REQ;
  req.src_id = myNodeId;
  basicRfSendPacket(BROADCAST_ADDR, (uint8 *)&req, sizeof(req));
  debugPrint("MASTER: Sent Roll Call request.\r\n");
}

// Processes a slave's response to the roll call
static void master_process_roll_call_response(uint8 *pRcv) {
  RollCallRspPacket *rsp = (RollCallRspPacket *)pRcv;
  uint8 slave_id = rsp->hdr.src_id;

  // Check if we already have this slave
  for (int i = 0; i < numSlavesFound; i++) {
    if (slaveList[i].id == slave_id)
      return; // Already exists
  }

  // Add new slave to the list if there's space
  if (numSlavesFound < MAX_SLAVES) {
    slaveList[numSlavesFound].id = slave_id;
    slaveList[numSlavesFound].used = 1;
    numSlavesFound++;
    debugPrintValue("MASTER: Discovered new slave, ID=", slave_id);
  }
}

// Allocates slots and broadcasts them
static void master_allocate_and_send_slots(void) {
  SlotAllocPacket pkt;
  pkt.hdr.type = PKT_TYPE_SLOT_ALLOC;
  pkt.hdr.src_id = myNodeId;
  pkt.num_slaves = numSlavesFound;

  debugPrint("MASTER: Allocating slots...\r\n");
  for (int i = 0; i < numSlavesFound; i++) {
    slaveList[i].slot = SLOT_ALLOC_START_NUM + i;
    pkt.slave_ids[i] = slaveList[i].id;
    pkt.slave_slots[i] = slaveList[i].slot;
    debugPrintValue("  - Slave ID ", slaveList[i].id);
    debugPrintValue("    gets Slot ", slaveList[i].slot);
  }

  basicRfSendPacket(BROADCAST_ADDR, (uint8 *)&pkt, sizeof(pkt));
  debugPrint("MASTER: Broadcasted slot allocation.\r\n");
}

// Sends the periodic sync beacon
static void master_send_sync_beacon(void) {
  SyncBeaconPacket beacon;
  beacon.hdr.type = PKT_TYPE_SYNC_BEACON;
  beacon.hdr.src_id = myNodeId;
  beacon.timestamp = timer_ms_count;
  basicRfSendPacket(BROADCAST_ADDR, (uint8 *)&beacon, sizeof(beacon));
}
#endif

#ifdef SLAVE_NODE
// Responds to the master's roll call with a random delay
static void slave_respond_to_roll_call(void) {
  halMcuWaitMs(rand() % 500); // Random delay to prevent collision
  RollCallRspPacket rsp;
  rsp.hdr.type = PKT_TYPE_ROLL_CALL_RSP;
  rsp.hdr.src_id = myNodeId;
  basicRfSendPacket(MASTER_ADDR, (uint8 *)&rsp, sizeof(rsp));
  debugPrint("SLAVE: Responded to Roll Call.\r\n");
}

// Receives and stores its assigned slot number
static void slave_process_slot_allocation(uint8 *pRcv) {
  SlotAllocPacket *pkt = (SlotAllocPacket *)pRcv;
  for (int i = 0; i < pkt->num_slaves; i++) {
    if (pkt->slave_ids[i] == myNodeId) {
      mySlot = pkt->slave_slots[i];
      debugPrintValue("SLAVE: I have been assigned Slot ", mySlot);
      return;
    }
  }
}

// Processes the sync beacon and updates local timer
static void slave_process_sync_beacon(uint8 *pRcv) {
  (void)pRcv; // We don't even need the timestamp for this simple model, just
              // the fact that it arrived.

  if (!networkSynced) {
    debugPrint("SLAVE: Sync acquired!\r\n");
  }

  networkSynced = TRUE;

  // CRITICAL FIX: The moment a beacon arrives IS our "time zero" for the new
  // superframe.
  lastSyncTimestamp = timer_ms_count;

  halLedSet(4); // Turn on Sync LED
}

// Initialize sensors
static void sensorsInit(void) {
  // Configure P1.2 for vibration sensor (SW-520D)
  P1SEL &= ~0x04; // Clear special function for P1.2
  P1DIR &= ~0x04; // Set P1.2 as input
  P1INP &= ~0x04; // Enable pull-up/pull-down on P1.2

  // Configure P1.3 for DHT11 temperature sensor
  P1SEL &= ~0x08; // Clear special function for P1.3
  P1DIR |= 0x08;  // Set P1.3 as output initially (DHT11 protocol requirement)
  P1INP &= ~0x08; // Enable pull-up/pull-down on P1.3
  P1_3 = 1;       // Set P1.3 high initially

  // Ensure LED pins remain as output
  P1DIR |= 0x1B;  // Set P1.0 (LED1), P1.1 (LED2), P1.4 (LED4) as output
  P1SEL &= ~0x1B; // Clear special function for LED pins

  // Wait for pins to stabilize
  halMcuWaitMs(50);

  debugPrint(
      "SLAVE: Sensors initialized - P1.2 for vibration, P1.3 for DHT11\r\n");
}

// Read temperature sensor value from DHT11
static uint16 readTemperature(void) {
  // Read DHT11 sensor
  DHT11();

  // Convert DHT11 temperature data to the format expected by TDMA system
  // DHT11 returns temperature in whole degrees, TDMA expects tenths of degrees
  uint16 temp = (wendu_shi * 10 + wendu_ge) * 10; // Convert to tenths format

  // Validate temperature range (0-50°C in tenths format = 0-500)
  if (temp > 500) {
    temp = 250; // Default to 25.0°C if reading is invalid
  }

  return temp;
}

// Read vibration sensor value
static uint16 readVibration(void) {
  // SW-520D outputs LOW when vibration detected, HIGH when stable
  uint8 sensorValue = (P1 & 0x04) ? 1 : 0; // Read P1.2 (bit 2)

  // Return inverted value: 0=stable, 1=vibration detected
  return sensorValue ? 0 : 1;
}

// Sends sensor data in its assigned slot
static void slave_send_sensor_data(void) {
  SensorDataPacket pkt;
  uint16 temp, vib;

  // Read actual sensor values
  temp = readTemperature();
  vib = readVibration();

  pkt.hdr.type = PKT_TYPE_SENSOR_DATA;
  pkt.hdr.src_id = myNodeId;
  pkt.temperature = temp;
  pkt.vibration = vib;

  basicRfSendPacket(MASTER_ADDR, (uint8 *)&pkt, sizeof(pkt));

  // Standard format output: "Node 1: Temp=25.0, Vib=0, Sync=1, Slot=1"
  char outputBuffer[64];
  char *ptr = outputBuffer;

  // Build complete message in buffer first
  const char *prefix = "Node ";
  while (*prefix)
    *ptr++ = *prefix++;

  *ptr++ = '0' + myNodeId;

  const char *tempLabel = ": Temp=";
  while (*tempLabel)
    *ptr++ = *tempLabel++;

  // Convert temperature to decimal format (divide by 10 for 1 decimal place)
  uint16 temp_int = temp / 10;
  uint16 temp_dec = temp % 10;

  if (temp_int >= 10) {
    *ptr++ = '0' + (temp_int / 10);
    *ptr++ = '0' + (temp_int % 10);
  } else {
    *ptr++ = '0' + temp_int;
  }
  *ptr++ = '.';
  *ptr++ = '0' + temp_dec;

  const char *vibLabel = ", Vib=";
  while (*vibLabel)
    *ptr++ = *vibLabel++;

  *ptr++ = vib ? '1' : '0';

  const char *syncLabel = ", Sync=1, Slot=";
  while (*syncLabel)
    *ptr++ = *syncLabel++;

  *ptr++ = '0' + mySlot;

  *ptr++ = '\r';
  *ptr++ = '\n';
  *ptr = '\0';

  // Single atomic output to prevent interleaving
  halUartWrite((uint8 *)outputBuffer, ptr - outputBuffer);
}
#endif

/***********************************************************************************
 * APPLICATION LOGIC
 */
#ifdef MASTER_NODE
void master_app() {
  uint32 superframe_start_time;

  // --- Discovery Phase (configurable duration) ---
  char discMsg[64];
  char *ptr = discMsg;
  const char *prefix = "MASTER: Starting ";
  while (*prefix)
    *ptr++ = *prefix++;

  // Add discovery duration
  uint32 discSec = DISCOVERY_DURATION_MS / 1000;
  *ptr++ = '0' + discSec;

  const char *suffix = "s discovery phase...\r\n";
  while (*suffix)
    *ptr++ = *suffix++;
  *ptr = '\0';

  debugPrint(discMsg);
  memset(slaveList, 0, sizeof(slaveList));
  numSlavesFound = 0;
  master_send_roll_call();

  superframe_start_time = timer_ms_count;
  while (timer_ms_count - superframe_start_time < DISCOVERY_DURATION_MS) {
    WDCTL = 0xA0;
    WDCTL = 0x50; // Feed watchdog
    if (basicRfPacketIsReady()) {
      if (basicRfReceive(rxBuffer, 128, NULL) > 0) {
        if (((PacketHeader *)rxBuffer)->type == PKT_TYPE_ROLL_CALL_RSP) {
          master_process_roll_call_response(rxBuffer);
        }
      }
    }
    halMcuWaitMs(10);
  }

  if (numSlavesFound > 0) {
    master_allocate_and_send_slots();
  } else {
    debugPrint(
        "MASTER: No slaves found. Will retry discovery after reboot.\r\n");
    // Loop forever if no slaves, or could implement periodic rediscovery
    while (1) {
      WDCTL = 0xA0;
      WDCTL = 0x50;
      halLedSet(1);
      halMcuWaitMs(100);
      halLedClear(1);
      halMcuWaitMs(100);
      halLedSet(1);
      halMcuWaitMs(100);
      halLedClear(1);
      halMcuWaitMs(100);
      halLedSet(1);
      halMcuWaitMs(100);
      halLedClear(1);
      halMcuWaitMs(100);
      halLedSet(1);
      halMcuWaitMs(100);
      halLedClear(1);
      halMcuWaitMs(700);
    }
  }

  // Give slaves time to receive slot allocation
  halMcuWaitMs(200);

  // --- Synchronized TDMA Phase ---
  debugPrint("\n--- MASTER: Entering Normal Operation ---\n");
  superframe_start_time = timer_ms_count;

  while (1) {
    WDCTL = 0xA0;
    WDCTL = 0x50;

    // 1. Mark the start of the superframe and send beacon
    superframe_start_time = timer_ms_count;
    master_send_sync_beacon();
    halLedSet(1);
    halMcuWaitMs(20);
    halLedClear(1);
    halMcuWaitMs(20);

    // 2. Listen in each allocated slave slot
    for (uint8 i = 0; i < numSlavesFound; i++) {
      // We expect the i-th slave in slot (i+1)
      // Wait for the duration of one slot
      halMcuWaitMs(SLOT_DURATION_MS);

      if (basicRfPacketIsReady()) {
        if (basicRfReceive(rxBuffer, 128, NULL) > 0) {
          PacketHeader *hdr = (PacketHeader *)rxBuffer;
          if (hdr->type == PKT_TYPE_SENSOR_DATA) {
            SensorDataPacket *data = (SensorDataPacket *)rxBuffer;
            halLedSet(2);
            halMcuWaitMs(50);
            halLedClear(2);

            // Standard format output: "Node 1: Temp=25.0, Vib=0, Sync=1,
            // Slot=1"
            char outputBuffer[64];
            char *ptr = outputBuffer;

            // Build complete message in buffer first
            const char *prefix = "Node ";
            while (*prefix)
              *ptr++ = *prefix++;

            *ptr++ = '0' + data->hdr.src_id;

            const char *tempLabel = ": Temp=";
            while (*tempLabel)
              *ptr++ = *tempLabel++;

            // Convert temperature to decimal format (divide by 10 for 1 decimal
            // place)
            uint16 temp_int = data->temperature / 10;
            uint16 temp_dec = data->temperature % 10;

            if (temp_int >= 10) {
              *ptr++ = '0' + (temp_int / 10);
              *ptr++ = '0' + (temp_int % 10);
            } else {
              *ptr++ = '0' + temp_int;
            }
            *ptr++ = '.';
            *ptr++ = '0' + temp_dec;

            const char *vibLabel = ", Vib=";
            while (*vibLabel)
              *ptr++ = *vibLabel++;

            *ptr++ = data->vibration ? '1' : '0';

            const char *syncLabel = ", Sync=1, Slot=";
            while (*syncLabel)
              *ptr++ = *syncLabel++;

            // Find the slot number for this node
            uint8 node_slot = 0;
            for (uint8 j = 0; j < numSlavesFound; j++) {
              if (slaveList[j].id == data->hdr.src_id) {
                node_slot = slaveList[j].slot;
                break;
              }
            }
            *ptr++ = '0' + node_slot;

            *ptr++ = '\r';
            *ptr++ = '\n';
            *ptr = '\0';

            // Single atomic output to prevent interleaving
            halUartWrite((uint8 *)outputBuffer, ptr - outputBuffer);
          }
        }
      }
    }

    // 3. Wait for the remainder of the superframe
    uint32 elapsed_time = timer_ms_count - superframe_start_time;
    if (elapsed_time < SUPERFRAME_PERIOD_MS) {
      halMcuWaitMs(SUPERFRAME_PERIOD_MS - elapsed_time);
    }
  }
}
#endif

#ifdef SLAVE_NODE
void slave_app() {
  uint32 wait_time;

  // --- Discovery & Slot Allocation Phase ---
  debugPrint("SLAVE: Waiting for Roll Call or Slot Allocation...\r\n");
  while (mySlot == 0xFF) {
    WDCTL = 0xA0;
    WDCTL = 0x50;
    if (basicRfPacketIsReady()) {
      if (basicRfReceive(rxBuffer, 128, NULL) > 0) {
        PacketHeader *hdr = (PacketHeader *)rxBuffer;
        if (hdr->type == PKT_TYPE_ROLL_CALL_REQ) {
          slave_respond_to_roll_call();
        } else if (hdr->type == PKT_TYPE_SLOT_ALLOC) {
          slave_process_slot_allocation(rxBuffer);
        }
      }
    }
    halMcuWaitMs(10);
  }

  if (mySlot == 0xFF) {
    debugPrint("SLAVE: Failed to get a slot. Halting.\r\n");
    while (1) {
      WDCTL = 0xA0;
      WDCTL = 0x50;
      halLedSet(1);
      halMcuWaitMs(100);
      halLedClear(1);
      halMcuWaitMs(200);
      halLedSet(1);
      halMcuWaitMs(100);
      halLedClear(1);
      halMcuWaitMs(200);
      halLedSet(1);
      halMcuWaitMs(100);
      halLedClear(1);
      halMcuWaitMs(500);
    }
  }

  // --- Synchronized TDMA Phase ---
  debugPrint("\n--- SLAVE: Entering Normal Operation ---\n");

  while (1) {
    WDCTL = 0xA0;
    WDCTL = 0x50;

    // 1. Wait for the sync beacon (with a timeout)
    networkSynced = FALSE;
    halLedClear(4);

    uint32 beacon_wait_start = timer_ms_count;
    // Wait for beacon for a bit longer than a superframe, e.g., 1.5s
    while (timer_ms_count - beacon_wait_start < (SUPERFRAME_PERIOD_MS + 500)) {
      if (basicRfPacketIsReady()) {
        if (basicRfReceive(rxBuffer, 128, NULL) > 0) {
          if (((PacketHeader *)rxBuffer)->type == PKT_TYPE_SYNC_BEACON) {
            networkSynced = TRUE;
            halLedSet(4);
            debugPrint("SLAVE: Beacon received, SYNC OK.\r\n");
            break; // Beacon found, exit wait loop
          }
        }
      }
      halMcuWaitMs(1);
    }

    // 2. If synced, proceed to send data in our slot
    if (networkSynced) {
      // Calculate how long to wait until our slot
      // Beacon is in slot 0. Our slot is `mySlot`.
      wait_time = mySlot * SLOT_DURATION_MS;

      // We add a small delay to compensate for master's processing and let it
      // enter receive mode
      halMcuWaitMs(wait_time + 5);

      // Send data
      slave_send_sensor_data();

      // Now, wait out the rest of the superframe
      uint32 remaining_slots = (MAX_SLAVES - mySlot);
      uint32 remaining_time = remaining_slots * SLOT_DURATION_MS;
      halMcuWaitMs(remaining_time);

    } else {
      // Sync Lost
      debugPrint("SLAVE: Sync Lost! Will wait for next beacon...\r\n");
      // The loop will automatically restart and wait for a beacon again
    }
  }
}
#endif

/***********************************************************************************
 * MAIN & ISR
 */
#pragma vector = T3_VECTOR
__interrupt void timer3_isr(void) {
  IRCON = 0x00;
  timer_ms_count++;
  T3CC0 += 125;
}

void main(void) {
  WDCTL = 0x00; // Disable watchdog during init
  halBoardInit();
  halUartInit(115200);

  configure_timer3_for_ms_tick();

  debugPrint("\r\n\r\n=== TDMA System (Optimized Design) ===\r\n");
#ifdef MASTER_NODE
  debugPrint("Role: MASTER, ID: 1\r\n");
#else
  debugPrint("Role: SLAVE, ID: ");
  char id_buf[4];
  id_buf[0] = (myNodeId / 10) + '0';
  id_buf[1] = (myNodeId % 10) + '0';
  id_buf[2] = '\0';
  debugPrint(id_buf);
  debugPrint("\r\n");
#endif

  debugPrint("System initialized successfully.\r\n");
  // Init RF
  basicRfConfig.panId = PAN_ID;
  basicRfConfig.channel = RF_CHANNEL;
  basicRfConfig.ackRequest = FALSE;
#ifdef SLAVE_NODE
  basicRfConfig.myAddr = NODE_ID; // Use node ID as address for simplicity
#else
  basicRfConfig.myAddr = 1;
#endif
  basicRfInit(&basicRfConfig);
  basicRfReceiveOn();

  // Init peripherals
  P1DIR |= 0x1B; // LEDs as output

#ifdef SLAVE_NODE
  // Initialize sensors for slave node
  sensorsInit();
#else
  // For master node, just configure basic pins
  P1SEL &= ~0x04;
  P1DIR &= ~0x04; // P1.2 as input for vibration (if needed)
#endif

  // Seed the random number generator
  srand(NODE_ID);

  WDCTL = 0xAC; // Enable watchdog with ~1s timeout

  debugPrint("Initialization complete. Starting application...\r\n");

#ifdef MASTER_NODE
  master_app();
#else
  slave_app();
#endif
}