#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
TDMA传感器网络可视化监控系统
主程序入口
"""

import tkinter as tk
from tkinter import ttk, messagebox
import threading
import time
import queue
import serial
import serial.tools.list_ports
import re
import json
from datetime import datetime
import matplotlib.pyplot as plt
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg
from matplotlib.figure import Figure
import matplotlib.animation as animation
from collections import deque
import sqlite3
import os

class TDMAVisualizationApp:
    def __init__(self, root):
        self.root = root
        self.root.title("TDMA传感器网络监控系统")
        self.root.geometry("1200x800")
        
        # 数据存储
        self.data_queue = queue.Queue()
        self.sensor_data = {
            'timestamps': deque(maxlen=100),
            'temperatures': deque(maxlen=100),
            'vibrations': deque(maxlen=100),
            'node_status': {}
        }
        
        # 串口相关
        self.serial_port = None
        self.is_connected = False
        self.read_thread = None
        
        # 初始化数据库
        self.init_database()
        
        # 创建界面
        self.create_widgets()
        
        # 启动数据处理线程
        self.start_data_processing()
        
    def init_database(self):
        """初始化SQLite数据库"""
        if not os.path.exists('data'):
            os.makedirs('data')
            
        self.db_conn = sqlite3.connect('data/sensor_data.db', check_same_thread=False)
        cursor = self.db_conn.cursor()
        
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS sensor_readings (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                timestamp TEXT,
                node_id INTEGER,
                temperature REAL,
                vibration INTEGER,
                sync_status INTEGER,
                slot_number INTEGER
            )
        ''')
        self.db_conn.commit()
        
    def create_widgets(self):
        """创建主界面"""
        # 主框架
        main_frame = ttk.Frame(self.root)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # 顶部控制面板
        self.create_control_panel(main_frame)
        
        # 中间数据显示区域
        self.create_data_display(main_frame)
        
        # 底部图表区域
        self.create_charts(main_frame)
        
    def create_control_panel(self, parent):
        """创建控制面板"""
        control_frame = ttk.LabelFrame(parent, text="连接控制", padding=10)
        control_frame.pack(fill=tk.X, pady=(0, 10))
        
        # 串口选择
        ttk.Label(control_frame, text="串口:").grid(row=0, column=0, padx=5)
        self.port_var = tk.StringVar()
        self.port_combo = ttk.Combobox(control_frame, textvariable=self.port_var, width=15)
        self.port_combo.grid(row=0, column=1, padx=5)
        
        # 波特率选择
        ttk.Label(control_frame, text="波特率:").grid(row=0, column=2, padx=5)
        self.baud_var = tk.StringVar(value="115200")
        baud_combo = ttk.Combobox(control_frame, textvariable=self.baud_var, 
                                 values=["9600", "115200", "230400"], width=10)
        baud_combo.grid(row=0, column=3, padx=5)
        
        # 控制按钮
        self.refresh_btn = ttk.Button(control_frame, text="刷新串口", command=self.refresh_ports)
        self.refresh_btn.grid(row=0, column=4, padx=5)
        
        self.connect_btn = ttk.Button(control_frame, text="连接", command=self.toggle_connection)
        self.connect_btn.grid(row=0, column=5, padx=5)
        
        # 状态显示
        self.status_var = tk.StringVar(value="未连接")
        status_label = ttk.Label(control_frame, textvariable=self.status_var, foreground="red")
        status_label.grid(row=0, column=6, padx=20)
        
        # 初始化串口列表
        self.refresh_ports()
        
    def create_data_display(self, parent):
        """创建数据显示区域"""
        data_frame = ttk.LabelFrame(parent, text="实时数据", padding=10)
        data_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 10))
        
        # 左侧：网络状态
        left_frame = ttk.Frame(data_frame)
        left_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        
        # 网络拓扑显示
        topology_frame = ttk.LabelFrame(left_frame, text="网络拓扑")
        topology_frame.pack(fill=tk.BOTH, expand=True, padx=(0, 10))
        
        self.topology_text = tk.Text(topology_frame, height=8, width=40)
        topology_scroll = ttk.Scrollbar(topology_frame, orient=tk.VERTICAL, command=self.topology_text.yview)
        self.topology_text.configure(yscrollcommand=topology_scroll.set)
        self.topology_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        topology_scroll.pack(side=tk.RIGHT, fill=tk.Y)
        
        # 右侧：传感器数据
        right_frame = ttk.Frame(data_frame)
        right_frame.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True)
        
        # 传感器数据表格
        sensor_frame = ttk.LabelFrame(right_frame, text="传感器数据")
        sensor_frame.pack(fill=tk.BOTH, expand=True)
        
        # 创建表格
        columns = ('节点ID', '温度(°C)', '振动', '同步状态', '时隙', '时间')
        self.sensor_tree = ttk.Treeview(sensor_frame, columns=columns, show='headings', height=8)
        
        for col in columns:
            self.sensor_tree.heading(col, text=col)
            self.sensor_tree.column(col, width=80)
            
        sensor_scroll = ttk.Scrollbar(sensor_frame, orient=tk.VERTICAL, command=self.sensor_tree.yview)
        self.sensor_tree.configure(yscrollcommand=sensor_scroll.set)
        self.sensor_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        sensor_scroll.pack(side=tk.RIGHT, fill=tk.Y)
        
    def create_charts(self, parent):
        """创建图表区域"""
        chart_frame = ttk.LabelFrame(parent, text="历史数据图表", padding=10)
        chart_frame.pack(fill=tk.BOTH, expand=True)
        
        # 创建matplotlib图表
        self.fig = Figure(figsize=(12, 4), dpi=100)
        
        # 温度图表
        self.temp_ax = self.fig.add_subplot(121)
        self.temp_ax.set_title('温度趋势')
        self.temp_ax.set_xlabel('时间')
        self.temp_ax.set_ylabel('温度 (°C)')
        
        # 振动图表
        self.vib_ax = self.fig.add_subplot(122)
        self.vib_ax.set_title('振动状态')
        self.vib_ax.set_xlabel('时间')
        self.vib_ax.set_ylabel('振动值')
        
        # 嵌入到tkinter
        self.canvas = FigureCanvasTkAgg(self.fig, chart_frame)
        self.canvas.get_tk_widget().pack(fill=tk.BOTH, expand=True)
        
        # 启动图表更新
        self.ani = animation.FuncAnimation(self.fig, self.update_charts, interval=1000, blit=False)
        
    def refresh_ports(self):
        """刷新可用串口列表"""
        ports = [port.device for port in serial.tools.list_ports.comports()]
        self.port_combo['values'] = ports
        if ports:
            self.port_combo.set(ports[0])
            
    def toggle_connection(self):
        """切换串口连接状态"""
        if not self.is_connected:
            self.connect_serial()
        else:
            self.disconnect_serial()
            
    def connect_serial(self):
        """连接串口"""
        try:
            port = self.port_var.get()
            baud = int(self.baud_var.get())
            
            if not port:
                messagebox.showerror("错误", "请选择串口")
                return
                
            self.serial_port = serial.Serial(port, baud, timeout=1)
            self.is_connected = True
            
            # 启动读取线程
            self.read_thread = threading.Thread(target=self.read_serial_data, daemon=True)
            self.read_thread.start()
            
            # 更新界面
            self.status_var.set(f"已连接 - {port}")
            self.connect_btn.config(text="断开")
            
        except Exception as e:
            messagebox.showerror("连接错误", f"无法连接串口: {str(e)}")
            
    def disconnect_serial(self):
        """断开串口连接"""
        self.is_connected = False
        if self.serial_port:
            self.serial_port.close()
            self.serial_port = None
            
        self.status_var.set("未连接")
        self.connect_btn.config(text="连接")
        
    def read_serial_data(self):
        """读取串口数据线程"""
        while self.is_connected and self.serial_port:
            try:
                if self.serial_port.in_waiting:
                    line = self.serial_port.readline().decode('utf-8', errors='ignore').strip()
                    if line:
                        self.data_queue.put(line)
            except Exception as e:
                print(f"串口读取错误: {e}")
                break
                
    def start_data_processing(self):
        """启动数据处理"""
        self.process_data()
        
    def process_data(self):
        """处理接收到的数据"""
        try:
            while not self.data_queue.empty():
                line = self.data_queue.get_nowait()
                self.parse_and_display_data(line)
        except queue.Empty:
            pass
        finally:
            # 每100ms处理一次数据
            self.root.after(100, self.process_data)
            
    def parse_and_display_data(self, line):
        """解析并显示数据"""
        # 这里需要根据您的TDMA系统实际输出格式来解析
        # 示例解析模式（需要根据实际情况调整）
        
        # 解析传感器数据: "Node 1: Temp=25.6, Vib=0, Sync=1, Slot=1"
        sensor_pattern = r'Node (\d+).*?Temp=([0-9.]+).*?Vib=(\d+).*?Sync=(\d+).*?Slot=(\d+)'
        match = re.search(sensor_pattern, line)
        
        if match:
            node_id = int(match.group(1))
            temperature = float(match.group(2))
            vibration = int(match.group(3))
            sync_status = int(match.group(4))
            slot_number = int(match.group(5))
            
            timestamp = datetime.now()
            
            # 更新数据存储
            self.sensor_data['timestamps'].append(timestamp)
            self.sensor_data['temperatures'].append(temperature)
            self.sensor_data['vibrations'].append(vibration)
            
            # 更新表格显示
            self.update_sensor_table(node_id, temperature, vibration, sync_status, slot_number, timestamp)
            
            # 保存到数据库
            self.save_to_database(timestamp, node_id, temperature, vibration, sync_status, slot_number)
            
        # 更新网络拓扑显示
        self.update_topology_display(line)
        
    def update_sensor_table(self, node_id, temp, vib, sync, slot, timestamp):
        """更新传感器数据表格"""
        # 清除旧数据（保留最近10条）
        children = self.sensor_tree.get_children()
        if len(children) >= 10:
            self.sensor_tree.delete(children[0])
            
        # 添加新数据
        sync_text = "同步" if sync else "失步"
        time_str = timestamp.strftime("%H:%M:%S")
        
        self.sensor_tree.insert('', 'end', values=(
            f"节点{node_id}", f"{temp:.1f}", vib, sync_text, slot, time_str
        ))
        
        # 滚动到最新数据
        children = self.sensor_tree.get_children()
        if children:
            self.sensor_tree.see(children[-1])
            
    def update_topology_display(self, line):
        """更新网络拓扑显示"""
        self.topology_text.insert(tk.END, f"{datetime.now().strftime('%H:%M:%S')} {line}\n")
        self.topology_text.see(tk.END)
        
        # 限制显示行数
        lines = self.topology_text.get("1.0", tk.END).split('\n')
        if len(lines) > 50:
            self.topology_text.delete("1.0", "10.0")
            
    def update_charts(self, frame):
        """更新图表"""
        if len(self.sensor_data['timestamps']) > 1:
            # 清除旧图表
            self.temp_ax.clear()
            self.vib_ax.clear()
            
            # 绘制温度图表
            self.temp_ax.plot(list(self.sensor_data['timestamps']), 
                             list(self.sensor_data['temperatures']), 'b-')
            self.temp_ax.set_title('温度趋势')
            self.temp_ax.set_ylabel('温度 (°C)')
            
            # 绘制振动图表
            self.vib_ax.plot(list(self.sensor_data['timestamps']), 
                            list(self.sensor_data['vibrations']), 'r-')
            self.vib_ax.set_title('振动状态')
            self.vib_ax.set_ylabel('振动值')
            
            # 格式化x轴时间显示
            self.fig.autofmt_xdate()
            
        return []
        
    def save_to_database(self, timestamp, node_id, temp, vib, sync, slot):
        """保存数据到数据库"""
        try:
            cursor = self.db_conn.cursor()
            cursor.execute('''
                INSERT INTO sensor_readings 
                (timestamp, node_id, temperature, vibration, sync_status, slot_number)
                VALUES (?, ?, ?, ?, ?, ?)
            ''', (timestamp.isoformat(), node_id, temp, vib, sync, slot))
            self.db_conn.commit()
        except Exception as e:
            print(f"数据库保存错误: {e}")

def main():
    root = tk.Tk()
    app = TDMAVisualizationApp(root)
    root.mainloop()

if __name__ == "__main__":
    main()
