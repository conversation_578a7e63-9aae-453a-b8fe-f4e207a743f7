# TDMA项目编译指南

## 问题原因分析

您遇到的编译错误 `Unable to open file 'lnk51ew_CC2530_banked.xcl'` 的根本原因是：

1. **新创建的项目配置文件缺少完整的链接器设置**
2. **xcl文件路径或文件名不正确**

原始的 `light_switch.ewp` 项目能够成功编译，是因为它包含了完整且正确的IAR项目配置，特别是XLINK链接器设置。

## 解决方案：使用现有项目配置

为了避免xcl配置问题，建议直接使用现有的 `light_switch.ewp` 项目来编译TDMA代码。

### 编译步骤

#### 1. 编译主节点

1. 打开 `ide/srf05_cc2530/iar/light_switch.ewp`
2. 在 `source/apps/light_switch/light_switch.c` 文件第41-44行，确保配置为：
   ```c
   #define MASTER_NODE              // 主节点配置（取消注释）
   // #define SLAVE_NODE            // 从节点配置（注释掉）
   #define NODE_ID 1                // 从节点ID
   ```
3. 编译项目（Build → Make）
4. 生成的固件文件：`ide/srf05_cc2530/iar/temp/light_switch/light_switch.d51`
5. 将固件重命名为 `master_node.d51` 以便区分

#### 2. 编译从节点1

1. 在同一个项目中修改配置为：
   ```c
   // #define MASTER_NODE           // 主节点配置（注释掉）
   #define SLAVE_NODE               // 从节点配置（取消注释）
   #define NODE_ID 1                // 从节点ID设为1
   ```
2. 重新编译项目
3. 将生成的固件重命名为 `slave_node_1.d51`

#### 3. 编译从节点2（如需要）

1. 修改配置为：
   ```c
   // #define MASTER_NODE           // 主节点配置（注释掉）
   #define SLAVE_NODE               // 从节点配置（取消注释）
   #define NODE_ID 2                // 从节点ID设为2
   ```
2. 重新编译项目
3. 将生成的固件重命名为 `slave_node_2.d51`

#### 4. 编译从节点3（如需要）

1. 修改配置为：
   ```c
   // #define MASTER_NODE           // 主节点配置（注释掉）
   #define SLAVE_NODE               // 从节点配置（取消注释）
   #define NODE_ID 3                // 从节点ID设为3
   ```
2. 重新编译项目
3. 将生成的固件重命名为 `slave_node_3.d51`

### 为什么这样做可以解决问题？

1. **使用已验证的配置**：原始项目的链接器配置是正确的，包含了正确的xcl文件路径
2. **避免配置错误**：不需要手动创建新的项目配置文件
3. **快速部署**：可以立即开始编译和测试

### 注意事项

1. **每次编译前**：务必检查并修改正确的编译配置宏定义
2. **固件管理**：编译后立即重命名固件文件，避免被下次编译覆盖
3. **版本控制**：建议备份原始的 `light_switch.c` 文件

### 烧录和测试

1. **烧录固件**：
   - 使用CC Debugger和SmartRF Flash Programmer
   - 主节点烧录 `master_node.d51`
   - 从节点分别烧录对应的 `slave_node_x.d51`

2. **硬件连接**：
   - 主节点：连接串口到PC
   - 从节点：连接传感器
     - DTH11温度传感器 → P0.0
     - SW-520D震动传感器 → P0.1

3. **功能测试**：
   - 串口监控（115200波特率）观察传感器数据
   - 发送LED控制命令测试：`LED,1,1`

## 总结

通过使用现有的项目配置，您可以：
- ✅ 避免xcl文件配置问题
- ✅ 快速编译TDMA代码
- ✅ 立即开始功能测试
- ✅ 确保编译配置的正确性

这种方法既简单又可靠，是解决当前编译问题的最佳方案。
