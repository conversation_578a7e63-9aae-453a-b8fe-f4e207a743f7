#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
TDMA数据解析模块
用于解析不同格式的串口数据
"""

import re
from datetime import datetime
from typing import Dict, Optional, Tuple

class TDMADataParser:
    """TDMA数据解析器"""
    
    def __init__(self):
        # 定义各种数据格式的正则表达式
        self.patterns = {
            # 传感器数据格式: "Node 1: Temp=25.6, Vib=0, Sync=1, Slot=1"
            'sensor_data': r'Node\s+(\d+).*?Temp=([0-9.]+).*?Vib=(\d+).*?Sync=(\d+).*?Slot=(\d+)',
            
            # 主节点状态: "MASTER: Network synchronized"
            'master_status': r'MASTER:\s*(.+)',
            
            # 从节点状态: "SLAVE: Sync Lost! Will wait for next beacon..."
            'slave_status': r'SLAVE:\s*(.+)',
            
            # 发现阶段: "MASTER: Found slave node 2"
            'discovery': r'MASTER:\s*Found slave node\s+(\d+)',
            
            # 时隙分配: "MASTER: Allocating slots..."
            'slot_allocation': r'MASTER:\s*Allocating slots',
            
            # 同步信标: "MASTER: Sending sync beacon"
            'sync_beacon': r'MASTER:\s*Sending sync beacon',
            
            # 网络拓扑: "Network: Master=1, Slaves=[2,3,4]"
            'network_topology': r'Network:\s*Master=(\d+),\s*Slaves=\[([0-9,\s]*)\]',
            
            # 时间戳格式: "12:34:56.789"
            'timestamp': r'(\d{2}:\d{2}:\d{2}(?:\.\d{3})?)',
            
            # 错误信息: "ERROR: Connection lost"
            'error': r'ERROR:\s*(.+)',
            
            # 调试信息: "DEBUG: Timer interrupt count=123"
            'debug': r'DEBUG:\s*(.+)'
        }
    
    def parse_line(self, line: str) -> Dict:
        """
        解析单行数据
        
        Args:
            line: 串口接收到的一行数据
            
        Returns:
            解析结果字典，包含数据类型和具体内容
        """
        line = line.strip()
        if not line:
            return {'type': 'empty', 'data': None}
        
        result = {
            'type': 'unknown',
            'raw_line': line,
            'timestamp': datetime.now(),
            'data': {}
        }
        
        # 尝试匹配各种数据格式
        for pattern_name, pattern in self.patterns.items():
            match = re.search(pattern, line, re.IGNORECASE)
            if match:
                result['type'] = pattern_name
                result['data'] = self._extract_data(pattern_name, match)
                break
        
        return result
    
    def _extract_data(self, pattern_name: str, match) -> Dict:
        """根据匹配的模式提取具体数据"""
        
        if pattern_name == 'sensor_data':
            return {
                'node_id': int(match.group(1)),
                'temperature': float(match.group(2)),
                'vibration': int(match.group(3)),
                'sync_status': int(match.group(4)),
                'slot_number': int(match.group(5))
            }
        
        elif pattern_name == 'master_status':
            return {
                'status': match.group(1).strip()
            }
        
        elif pattern_name == 'slave_status':
            return {
                'status': match.group(1).strip()
            }
        
        elif pattern_name == 'discovery':
            return {
                'slave_node_id': int(match.group(1))
            }
        
        elif pattern_name == 'network_topology':
            slaves_str = match.group(2).strip()
            slaves = []
            if slaves_str:
                slaves = [int(x.strip()) for x in slaves_str.split(',') if x.strip()]
            
            return {
                'master_id': int(match.group(1)),
                'slave_ids': slaves
            }
        
        elif pattern_name == 'timestamp':
            return {
                'time_str': match.group(1)
            }
        
        elif pattern_name in ['error', 'debug']:
            return {
                'message': match.group(1).strip()
            }
        
        else:
            # 默认返回所有匹配组
            return {
                'groups': match.groups()
            }
    
    def parse_sensor_data_advanced(self, line: str) -> Optional[Dict]:
        """
        高级传感器数据解析，支持更多格式
        
        支持的格式:
        1. "Node 1: Temp=25.6, Vib=0, Sync=1, Slot=1"
        2. "ID:1 T:25.6 V:0 S:1 SL:1"
        3. "1,25.6,0,1,1"
        4. "Node1 Temperature:25.6 Vibration:0 Synchronized:Yes Slot:1"
        """
        
        # 格式1: 标准格式 "Node 1: Temp=25.6, Vib=0, Sync=1, Slot=1"
        pattern1 = r'Node\s+(\d+).*?Temp=([0-9.]+).*?Vib=(\d+).*?Sync=(\d+).*?Slot=(\d+)'
        match1 = re.search(pattern1, line, re.IGNORECASE)
        if match1:
            return {
                'node_id': int(match1.group(1)),
                'temperature': float(match1.group(2)),
                'vibration': int(match1.group(3)),
                'sync_status': int(match1.group(4)),
                'slot_number': int(match1.group(5)),
                'format': 'standard'
            }

        # 格式2: 新格式 "Node 2: Temp=25.0, Vib=0,Slot=1" (缺少Sync字段)
        pattern2 = r'Node\s+(\d+):\s*Temp=([0-9.]+),\s*Vib=(\d+),\s*Slot=(\d+)'
        match2 = re.search(pattern2, line, re.IGNORECASE)
        if match2:
            return {
                'node_id': int(match2.group(1)),
                'temperature': float(match2.group(2)),
                'vibration': int(match2.group(3)),
                'sync_status': 1,  # 默认认为已同步（能收到数据说明已同步）
                'slot_number': int(match2.group(4)),
                'format': 'new_format'
            }
        
        # 格式2: 简化格式
        pattern2 = r'ID:(\d+)\s+T:([0-9.]+)\s+V:(\d+)\s+S:(\d+)\s+SL:(\d+)'
        match2 = re.search(pattern2, line)
        if match2:
            return {
                'node_id': int(match2.group(1)),
                'temperature': float(match2.group(2)),
                'vibration': int(match2.group(3)),
                'sync_status': int(match2.group(4)),
                'slot_number': int(match2.group(5)),
                'format': 'compact'
            }
        
        # 格式3: CSV格式
        pattern3 = r'^(\d+),([0-9.]+),(\d+),(\d+),(\d+)$'
        match3 = re.search(pattern3, line.strip())
        if match3:
            return {
                'node_id': int(match3.group(1)),
                'temperature': float(match3.group(2)),
                'vibration': int(match3.group(3)),
                'sync_status': int(match3.group(4)),
                'slot_number': int(match3.group(5)),
                'format': 'csv'
            }
        
        # 格式4: 详细格式
        pattern4 = r'Node(\d+)\s+Temperature:([0-9.]+)\s+Vibration:(\d+)\s+Synchronized:(Yes|No|1|0)\s+Slot:(\d+)'
        match4 = re.search(pattern4, line, re.IGNORECASE)
        if match4:
            sync_text = match4.group(4).lower()
            sync_status = 1 if sync_text in ['yes', '1'] else 0
            
            return {
                'node_id': int(match4.group(1)),
                'temperature': float(match4.group(2)),
                'vibration': int(match4.group(3)),
                'sync_status': sync_status,
                'slot_number': int(match4.group(5)),
                'format': 'verbose'
            }
        
        return None
    
    def validate_sensor_data(self, data: Dict) -> Tuple[bool, str]:
        """
        验证传感器数据的有效性
        
        Returns:
            (is_valid, error_message)
        """
        if not isinstance(data, dict):
            return False, "数据不是字典格式"
        
        required_fields = ['node_id', 'temperature', 'vibration', 'sync_status', 'slot_number']
        for field in required_fields:
            if field not in data:
                return False, f"缺少必需字段: {field}"
        
        # 数据范围检查
        if not (1 <= data['node_id'] <= 255):
            return False, f"节点ID超出范围: {data['node_id']}"
        
        if not (-50 <= data['temperature'] <= 100):
            return False, f"温度值超出合理范围: {data['temperature']}"
        
        if data['vibration'] not in [0, 1]:
            return False, f"振动值应为0或1: {data['vibration']}"
        
        if data['sync_status'] not in [0, 1]:
            return False, f"同步状态应为0或1: {data['sync_status']}"
        
        if not (0 <= data['slot_number'] <= 15):
            return False, f"时隙号超出范围: {data['slot_number']}"
        
        return True, "数据有效"
    
    def get_network_status(self, recent_data: list) -> Dict:
        """
        根据最近的数据分析网络状态
        
        Args:
            recent_data: 最近接收到的数据列表
            
        Returns:
            网络状态分析结果
        """
        if not recent_data:
            return {
                'total_nodes': 0,
                'active_nodes': 0,
                'synchronized_nodes': 0,
                'network_health': 'unknown'
            }
        
        # 统计节点信息
        nodes = {}
        for data in recent_data:
            if data.get('type') == 'sensor_data' and data.get('data'):
                node_id = data['data'].get('node_id')
                if node_id:
                    if node_id not in nodes:
                        nodes[node_id] = {
                            'last_seen': data['timestamp'],
                            'sync_status': data['data'].get('sync_status', 0),
                            'data_count': 0
                        }
                    nodes[node_id]['data_count'] += 1
                    nodes[node_id]['last_seen'] = max(nodes[node_id]['last_seen'], data['timestamp'])
                    nodes[node_id]['sync_status'] = data['data'].get('sync_status', 0)
        
        total_nodes = len(nodes)
        synchronized_nodes = sum(1 for node in nodes.values() if node['sync_status'] == 1)
        
        # 计算网络健康度
        if total_nodes == 0:
            health = 'no_data'
        elif synchronized_nodes == total_nodes:
            health = 'excellent'
        elif synchronized_nodes >= total_nodes * 0.8:
            health = 'good'
        elif synchronized_nodes >= total_nodes * 0.5:
            health = 'fair'
        else:
            health = 'poor'
        
        return {
            'total_nodes': total_nodes,
            'active_nodes': total_nodes,  # 假设所有检测到的节点都是活跃的
            'synchronized_nodes': synchronized_nodes,
            'network_health': health,
            'sync_ratio': synchronized_nodes / total_nodes if total_nodes > 0 else 0,
            'node_details': nodes
        }

# 使用示例
if __name__ == "__main__":
    parser = TDMADataParser()
    
    # 测试数据
    test_lines = [
        "Node 1: Temp=25.6, Vib=0, Sync=1, Slot=1",
        "MASTER: Network synchronized",
        "SLAVE: Sync Lost! Will wait for next beacon...",
        "ID:2 T:26.2 V:1 S:1 SL:2",
        "3,27.1,0,1,3",
        "Node4 Temperature:24.8 Vibration:1 Synchronized:Yes Slot:4",
        "ERROR: Connection timeout"
    ]
    
    print("=== TDMA数据解析测试 ===")
    for line in test_lines:
        result = parser.parse_line(line)
        print(f"\n输入: {line}")
        print(f"类型: {result['type']}")
        print(f"数据: {result['data']}")
        
        if result['type'] == 'sensor_data':
            is_valid, msg = parser.validate_sensor_data(result['data'])
            print(f"验证: {msg}")
