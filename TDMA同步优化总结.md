# TDMA同步代码优化总结

## 🎯 优化目标

基于您的建议文档和同步流程文档，对TDMA同步代码进行了以下关键优化：

## 🔧 主要优化内容

### 1. 宏定义互斥性改进

**优化前：**
```c
// Uncomment ONE of the following:
#define MASTER_NODE // Master node configuration
// #define SLAVE_NODE // Slave node configuration
```

**优化后：**
```c
// Uncomment ONE of the following:
#define MASTER_NODE // Master node configuration
// #define SLAVE_NODE // Slave node configuration

// Ensure only one node type is defined
#ifdef MASTER_NODE
  #ifdef SLAVE_NODE
    #error "Cannot define both MASTER_NODE and SLAVE_NODE"
  #endif
#else
  #ifndef SLAVE_NODE
    #define SLAVE_NODE // Default to slave node if neither is defined
  #endif
#endif
```

**改进效果：**
- ✅ 防止同时定义主从节点导致的逻辑错误
- ✅ 确保至少有一个节点类型被定义
- ✅ 编译时错误检查，避免运行时问题

### 2. 从节点Timer3配置优化

**优化前：** 从节点在初始化时立即配置Timer3
**优化后：** 从节点等待主节点beacon后再配置Timer3

**新增函数：**
```c
static void tdmaConfigureTimer3(void);
```

**优化的tdmaInit函数：**
```c
static void tdmaInit(void) {
  // 初始化TDMA变量
  currentSlot = 0;
  currentSuperframe = 0;
  timerTestCount = 0;
  slotFlags = 0;
  networkSynced = FALSE;
  timerWorking = FALSE;

#ifdef MASTER_NODE
  // 主节点：立即配置Timer3（它是时间基准）
  tdmaConfigureTimer3();
#else
  // 从节点：等待beacon后再配置Timer3
  // Timer3将在tdmaProcessBeacon()中配置
#endif
}
```

**改进效果：**
- ✅ 符合同步流程文档的要求
- ✅ 从节点等待主节点时间戳后再同步Timer3
- ✅ 避免从节点独立启动定时器导致的同步问题

### 3. 同步逻辑严格化

**优化的tdmaProcessBeacon函数：**
```c
static void tdmaProcessBeacon(uint8 *data, uint8 length) {
  if (beacon->header.frameType == FRAME_TYPE_BEACON &&
      beacon->header.nodeId == 0) { // 验证来自主节点
    
#ifdef SLAVE_NODE
    // 首次同步：如果Timer3未配置则配置它
    if (!timerWorking) {
      tdmaConfigureTimer3();
    }
#endif

    // 获取主节点时间戳和时隙长度
    uint32 masterTimestamp = beacon->masterTimestamp;
    uint32 slotLength = beacon->slotLength;
    
    // 计算网络延迟补偿
    uint32 estimatedDelay = 50; // 假设50个定时器滴答延迟
    
    // 同步Timer3计数器
    EA = 0; // 禁用中断避免竞争条件
    timerTestCount = masterTimestamp + estimatedDelay;
    currentSlot = 0;
    currentSuperframe = beacon->superframeId;
    slotTimer = 0;
    slotFlags = 0;
    EA = 1; // 重新启用中断
    
    // 仅在成功处理后标记为已同步
    networkSynced = TRUE;
    syncTimeout = 0;
  }
}
```

**改进效果：**
- ✅ 严格验证beacon帧来源（nodeId == 0）
- ✅ 首次同步时配置Timer3
- ✅ 使用主节点时间戳进行精确同步
- ✅ 原子操作避免竞争条件
- ✅ 仅在成功处理后设置networkSynced

### 4. 时隙等待逻辑优化

**现有的tdmaWaitForSlot函数已经正确实现：**
```c
#ifdef MASTER_NODE
  // 主节点：仅需要定时器工作（它是时间基准）
  if (!timerWorking) {
    halUartWrite("TDMA: Master using software timing\r\n");
    halMcuWaitMs(TDMA_SLOT_LENGTH);
    return;
  }
#else
  // 从节点：需要定时器工作且网络已同步
  if (!timerWorking || !networkSynced) {
    halUartWrite("TDMA: Slave using software timing\r\n");
    halMcuWaitMs(TDMA_SLOT_LENGTH);
    return;
  }
#endif
```

**改进效果：**
- ✅ 主节点不等待networkSynced状态（符合建议文档）
- ✅ 从节点需要同时满足定时器工作和网络同步
- ✅ 软件定时器作为硬件定时器失效时的备用方案

## 📋 同步流程总结

### 主节点流程：
1. 启动时立即配置Timer3（时间基准）
2. 定期发送包含时间戳的beacon帧
3. 使用固定时序，不依赖同步状态
4. 监听从节点数据

### 从节点流程：
1. 启动时进入监听状态
2. 接收到有效beacon后配置Timer3
3. 使用主节点时间戳同步本地Timer3
4. 进入同步模式，按时隙发送数据

## 🎉 预期效果

通过这些优化，TDMA同步应该更加稳定和可靠：

- **更严格的同步验证**：确保只有来自主节点的有效beacon才能触发同步
- **正确的初始化顺序**：从节点等待主节点信号后再配置定时器
- **防错机制**：编译时检查防止配置错误
- **原子同步操作**：避免中断导致的竞争条件
- **合理的降级策略**：硬件定时器失效时使用软件定时器

## 🔄 下一步建议

1. 编译并测试优化后的代码
2. 验证主从节点同步是否更加稳定
3. 观察LED指示和串口输出确认同步状态
4. 如有问题，可进一步调整延迟补偿参数
