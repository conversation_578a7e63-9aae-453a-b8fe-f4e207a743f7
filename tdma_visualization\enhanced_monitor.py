#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
TDMA传感器网络增强监控系统
包含数据库存储和图形化显示功能
"""

import tkinter as tk
from tkinter import ttk, messagebox, scrolledtext
import threading
import time
import queue
import re
from datetime import datetime, timedelta
import os
import sqlite3
from collections import defaultdict, deque

try:
    import serial
    import serial.tools.list_ports
    SERIAL_AVAILABLE = True
except ImportError:
    SERIAL_AVAILABLE = False
    print("警告: pyserial未安装，将使用模拟数据模式")

try:
    import matplotlib.pyplot as plt
    from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg
    from matplotlib.figure import Figure
    import matplotlib.animation as animation
    import matplotlib.dates as mdates
    import matplotlib.font_manager as fm

    # 配置matplotlib中文字体支持
    def configure_chinese_font():
        """配置matplotlib中文字体"""
        try:
            # 尝试不同的中文字体
            chinese_fonts = [
                'SimHei',           # 黑体 (Windows)
                'Microsoft YaHei',  # 微软雅黑 (Windows)
                'PingFang SC',      # 苹方 (macOS)
                'Hiragino Sans GB', # 冬青黑体 (macOS)
                'WenQuanYi Micro Hei', # 文泉驿微米黑 (Linux)
                'Noto Sans CJK SC', # 思源黑体 (Linux)
                'DejaVu Sans',      # 备用字体
                'Arial Unicode MS', # 备用字体
                'sans-serif'        # 系统默认
            ]

            # 检查可用字体
            available_fonts = [f.name for f in fm.fontManager.ttflist]
            selected_font = None

            for font in chinese_fonts:
                if font in available_fonts:
                    selected_font = font
                    break

            if selected_font:
                plt.rcParams['font.sans-serif'] = [selected_font] + chinese_fonts
                print(f"已配置中文字体: {selected_font}")
            else:
                plt.rcParams['font.sans-serif'] = chinese_fonts
                print("使用默认字体配置，可能无法正确显示中文")

            plt.rcParams['axes.unicode_minus'] = False  # 解决负号显示问题
            return True
        except Exception as e:
            print(f"字体配置失败: {e}")
            return False

    font_configured = configure_chinese_font()
    MATPLOTLIB_AVAILABLE = True
    print("matplotlib已加载" + ("，中文字体支持已配置" if font_configured else "，字体配置可能有问题"))
except ImportError:
    MATPLOTLIB_AVAILABLE = False
    print("警告: matplotlib未安装，图表功能将被禁用")

class EnhancedTDMAMonitor:
    def __init__(self, root):
        self.root = root
        self.root.title("TDMA传感器网络增强监控系统")
        self.root.geometry("1400x900")
        
        # 数据存储
        self.data_queue = queue.Queue()
        self.sensor_data_list = []
        
        # 多节点数据存储 (node_id -> deque of data points) - 减少内存使用
        self.node_data = defaultdict(lambda: {
            'timestamps': deque(maxlen=50),  # 减少到50个数据点
            'temperatures': deque(maxlen=50),
            'vibrations': deque(maxlen=50),
            'slots': deque(maxlen=50)
        })
        
        # 串口相关
        self.serial_port = None
        self.is_connected = False
        self.read_thread = None
        
        # 模拟数据（用于测试）
        self.demo_mode = not SERIAL_AVAILABLE
        self.demo_counter = 0
        
        # 图表相关
        self.chart_enabled = MATPLOTLIB_AVAILABLE
        self.chart_update_interval = 5000  # 5秒更新一次（优化性能）
        self.chart_needs_update = False  # 标记是否需要更新图表
        self.last_chart_update = datetime.now()  # 上次图表更新时间
        
        # 初始化数据库
        self.init_database()
        
        # 创建界面
        self.create_widgets()
        
        # 启动数据处理
        self.start_data_processing()
        
        # 如果是演示模式，启动模拟数据
        if self.demo_mode:
            self.start_demo_data()
            
        # 启动图表更新
        if self.chart_enabled:
            self.start_chart_updates()
        
    def init_database(self):
        """初始化SQLite数据库"""
        if not os.path.exists('data'):
            os.makedirs('data')
            
        self.db_conn = sqlite3.connect('data/tdma_sensor_data.db', check_same_thread=False)
        cursor = self.db_conn.cursor()
        
        # 创建传感器数据表
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS sensor_readings (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                timestamp TEXT NOT NULL,
                node_id INTEGER NOT NULL,
                temperature REAL NOT NULL,
                vibration INTEGER NOT NULL,
                sync_status INTEGER NOT NULL,
                slot_number INTEGER NOT NULL,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # 创建节点信息表
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS node_info (
                node_id INTEGER PRIMARY KEY,
                node_name TEXT,
                last_seen DATETIME,
                total_packets INTEGER DEFAULT 0,
                is_active BOOLEAN DEFAULT 1
            )
        ''')
        
        # 创建索引提高查询性能
        cursor.execute('''
            CREATE INDEX IF NOT EXISTS idx_timestamp ON sensor_readings(timestamp)
        ''')
        cursor.execute('''
            CREATE INDEX IF NOT EXISTS idx_node_id ON sensor_readings(node_id)
        ''')
        
        self.db_conn.commit()
        print("数据库初始化完成")
        
    def create_widgets(self):
        """创建主界面"""
        # 创建主要的Notebook容器
        self.notebook = ttk.Notebook(self.root)
        self.notebook.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # 创建各个标签页
        self.create_monitor_tab()
        if self.chart_enabled:
            self.create_chart_tab()
        self.create_database_tab()
        
    def create_monitor_tab(self):
        """创建监控标签页"""
        monitor_frame = ttk.Frame(self.notebook)
        self.notebook.add(monitor_frame, text="实时监控")
        
        # 顶部控制面板
        self.create_control_panel(monitor_frame)
        
        # 中间数据显示区域
        self.create_data_display(monitor_frame)
        
        # 底部日志区域
        self.create_log_area(monitor_frame)
        
    def create_chart_tab(self):
        """创建图表标签页"""
        if not self.chart_enabled:
            return
            
        chart_frame = ttk.Frame(self.notebook)
        self.notebook.add(chart_frame, text="数据图表")
        
        # 图表控制面板
        control_frame = ttk.LabelFrame(chart_frame, text="图表控制", padding=10)
        control_frame.pack(fill=tk.X, pady=(0, 10))
        
        # 节点选择
        ttk.Label(control_frame, text="显示节点:").grid(row=0, column=0, padx=5)
        self.chart_node_var = tk.StringVar(value="全部")
        self.chart_node_combo = ttk.Combobox(control_frame, textvariable=self.chart_node_var, 
                                           values=["全部"], width=15)
        self.chart_node_combo.grid(row=0, column=1, padx=5)
        self.chart_node_combo.bind('<<ComboboxSelected>>', self.on_chart_node_changed)
        
        # 时间范围选择
        ttk.Label(control_frame, text="时间范围:").grid(row=0, column=2, padx=5)
        self.time_range_var = tk.StringVar(value="最近10分钟")
        time_range_combo = ttk.Combobox(control_frame, textvariable=self.time_range_var,
                                       values=["最近1分钟", "最近5分钟", "最近10分钟", 
                                              "最近30分钟", "最近1小时", "全部"], width=12)
        time_range_combo.grid(row=0, column=3, padx=5)
        time_range_combo.bind('<<ComboboxSelected>>', self.on_time_range_changed)
        
        # 图表类型选择
        ttk.Label(control_frame, text="图表类型:").grid(row=0, column=4, padx=5)
        self.chart_type_var = tk.StringVar(value="温度+振动")
        chart_type_combo = ttk.Combobox(control_frame, textvariable=self.chart_type_var,
                                       values=["温度", "振动", "温度+振动", "同步状态"], width=12)
        chart_type_combo.grid(row=0, column=5, padx=5)
        chart_type_combo.bind('<<ComboboxSelected>>', self.on_chart_type_changed)
        
        # 刷新按钮
        refresh_btn = ttk.Button(control_frame, text="刷新图表", command=self.refresh_charts)
        refresh_btn.grid(row=0, column=6, padx=10)
        
        # 创建图表区域
        self.create_chart_area(chart_frame)
        
    def create_database_tab(self):
        """创建数据库管理标签页"""
        db_frame = ttk.Frame(self.notebook)
        self.notebook.add(db_frame, text="数据库管理")
        
        # 数据库控制面板
        db_control_frame = ttk.LabelFrame(db_frame, text="数据库操作", padding=10)
        db_control_frame.pack(fill=tk.X, pady=(0, 10))
        
        # 统计信息
        ttk.Button(db_control_frame, text="刷新统计", command=self.refresh_db_stats).grid(row=0, column=0, padx=5)
        ttk.Button(db_control_frame, text="导出数据", command=self.export_database).grid(row=0, column=1, padx=5)
        ttk.Button(db_control_frame, text="清理旧数据", command=self.cleanup_old_data).grid(row=0, column=2, padx=5)
        
        # 查询条件
        ttk.Label(db_control_frame, text="查询节点:").grid(row=0, column=3, padx=5)
        self.db_node_var = tk.StringVar(value="全部")
        self.db_node_combo = ttk.Combobox(db_control_frame, textvariable=self.db_node_var, 
                                         values=["全部"], width=10)
        self.db_node_combo.grid(row=0, column=4, padx=5)
        
        ttk.Button(db_control_frame, text="查询", command=self.query_database).grid(row=0, column=5, padx=5)
        
        # 数据库统计信息显示
        stats_frame = ttk.LabelFrame(db_frame, text="数据库统计", padding=10)
        stats_frame.pack(fill=tk.X, pady=(0, 10))
        
        self.stats_text = tk.Text(stats_frame, height=4, wrap=tk.WORD)
        self.stats_text.pack(fill=tk.X)
        
        # 查询结果显示
        result_frame = ttk.LabelFrame(db_frame, text="查询结果", padding=10)
        result_frame.pack(fill=tk.BOTH, expand=True)
        
        # 创建查询结果表格
        columns = ('ID', '时间', '节点ID', '温度(°C)', '振动', '时隙')
        self.db_tree = ttk.Treeview(result_frame, columns=columns, show='headings', height=15)

        for col in columns:
            self.db_tree.heading(col, text=col)
            if col == 'ID':
                self.db_tree.column(col, width=50)
            elif col == '时间':
                self.db_tree.column(col, width=150)
            else:
                self.db_tree.column(col, width=100)
                
        db_scroll = ttk.Scrollbar(result_frame, orient=tk.VERTICAL, command=self.db_tree.yview)
        self.db_tree.configure(yscrollcommand=db_scroll.set)
        
        self.db_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        db_scroll.pack(side=tk.RIGHT, fill=tk.Y)
        
        # 初始化统计信息
        self.refresh_db_stats()

    def create_chart_area(self, parent):
        """创建图表显示区域"""
        if not self.chart_enabled:
            return

        # 创建matplotlib图表
        self.fig = Figure(figsize=(14, 8), dpi=100)

        # 创建子图
        self.temp_ax = self.fig.add_subplot(221)  # 温度图表
        self.vib_ax = self.fig.add_subplot(222)   # 振动图表
        self.sync_ax = self.fig.add_subplot(223)  # 同步状态图表
        self.combined_ax = self.fig.add_subplot(224)  # 综合图表

        # 设置图表标题（使用安全的中文显示）
        self.set_chart_title(self.temp_ax, '温度趋势', 'Temperature Trend')
        self.set_chart_label(self.temp_ax, 'ylabel', '温度 (°C)', 'Temperature (°C)')

        self.set_chart_title(self.vib_ax, '振动状态', 'Vibration Status')
        self.set_chart_label(self.vib_ax, 'ylabel', '振动值', 'Vibration Value')

        self.set_chart_title(self.sync_ax, '网络同步状态', 'Network Sync Status')
        self.set_chart_label(self.sync_ax, 'ylabel', '同步节点数', 'Synced Nodes')

        self.set_chart_title(self.combined_ax, '多节点综合视图', 'Multi-Node Overview')
        self.set_chart_label(self.combined_ax, 'ylabel', '温度 (°C)', 'Temperature (°C)')

        # 嵌入到tkinter
        self.canvas = FigureCanvasTkAgg(self.fig, parent)
        self.canvas.get_tk_widget().pack(fill=tk.BOTH, expand=True)

        # 调整子图间距
        self.fig.tight_layout()

    def set_chart_title(self, ax, chinese_title, english_title):
        """安全设置图表标题，支持中文回退"""
        try:
            ax.set_title(chinese_title)
            # 测试是否能正确渲染中文
            ax.figure.canvas.draw_idle()
        except Exception:
            # 如果中文显示失败，使用英文标题
            ax.set_title(english_title)

    def set_chart_label(self, ax, label_type, chinese_label, english_label):
        """安全设置图表标签，支持中文回退"""
        try:
            if label_type == 'ylabel':
                ax.set_ylabel(chinese_label)
            elif label_type == 'xlabel':
                ax.set_xlabel(chinese_label)
            # 测试是否能正确渲染中文
            ax.figure.canvas.draw_idle()
        except Exception:
            # 如果中文显示失败，使用英文标签
            if label_type == 'ylabel':
                ax.set_ylabel(english_label)
            elif label_type == 'xlabel':
                ax.set_xlabel(english_label)

    def create_control_panel(self, parent):
        """创建控制面板"""
        control_frame = ttk.LabelFrame(parent, text="连接控制", padding=10)
        control_frame.pack(fill=tk.X, pady=(0, 10))

        if SERIAL_AVAILABLE:
            # 串口选择
            ttk.Label(control_frame, text="串口:").grid(row=0, column=0, padx=5)
            self.port_var = tk.StringVar()
            self.port_combo = ttk.Combobox(control_frame, textvariable=self.port_var, width=15)
            self.port_combo.grid(row=0, column=1, padx=5)

            # 波特率选择
            ttk.Label(control_frame, text="波特率:").grid(row=0, column=2, padx=5)
            self.baud_var = tk.StringVar(value="115200")
            baud_combo = ttk.Combobox(control_frame, textvariable=self.baud_var,
                                     values=["9600", "115200", "230400"], width=10)
            baud_combo.grid(row=0, column=3, padx=5)

            # 控制按钮
            self.refresh_btn = ttk.Button(control_frame, text="刷新串口", command=self.refresh_ports)
            self.refresh_btn.grid(row=0, column=4, padx=5)

            self.connect_btn = ttk.Button(control_frame, text="连接", command=self.toggle_connection)
            self.connect_btn.grid(row=0, column=5, padx=5)

            # 状态显示
            self.status_var = tk.StringVar(value="未连接")
            status_label = ttk.Label(control_frame, textvariable=self.status_var, foreground="red")
            status_label.grid(row=0, column=6, padx=20)

            # 初始化串口列表
            self.refresh_ports()
        else:
            # 演示模式提示
            demo_label = ttk.Label(control_frame, text="演示模式 - 显示模拟数据", foreground="blue")
            demo_label.grid(row=0, column=0, padx=5)

            self.demo_btn = ttk.Button(control_frame, text="开始/停止演示", command=self.toggle_demo)
            self.demo_btn.grid(row=0, column=1, padx=5)

            self.status_var = tk.StringVar(value="演示模式")
            status_label = ttk.Label(control_frame, textvariable=self.status_var, foreground="blue")
            status_label.grid(row=0, column=2, padx=20)

        # 数据管理按钮
        data_mgmt_label = ttk.Label(control_frame, text="数据管理:")
        data_mgmt_label.grid(row=0, column=7, padx=10)

        self.clear_data_btn = ttk.Button(control_frame, text="清除数据", command=self.clear_data)
        self.clear_data_btn.grid(row=0, column=8, padx=5)

        # 数据统计显示
        self.data_count_var = tk.StringVar(value="0条记录")
        count_label = ttk.Label(control_frame, textvariable=self.data_count_var)
        count_label.grid(row=0, column=9, padx=5)

    def create_data_display(self, parent):
        """创建数据显示区域"""
        data_frame = ttk.LabelFrame(parent, text="实时数据", padding=10)
        data_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 10))

        # 传感器数据表格
        columns = ('时间', '节点ID', '温度(°C)', '振动', '时隙')
        self.sensor_tree = ttk.Treeview(data_frame, columns=columns, show='headings', height=12)

        for col in columns:
            self.sensor_tree.heading(col, text=col)
            if col == '时间':
                self.sensor_tree.column(col, width=120)
            else:
                self.sensor_tree.column(col, width=100)

        # 添加滚动条
        tree_scroll = ttk.Scrollbar(data_frame, orient=tk.VERTICAL, command=self.sensor_tree.yview)
        self.sensor_tree.configure(yscrollcommand=tree_scroll.set)

        self.sensor_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        tree_scroll.pack(side=tk.RIGHT, fill=tk.Y)

        # 右键菜单
        self.create_context_menu()

    def create_log_area(self, parent):
        """创建日志区域"""
        log_frame = ttk.LabelFrame(parent, text="系统日志", padding=10)
        log_frame.pack(fill=tk.X, pady=(0, 10))

        # 创建日志控制按钮框架
        log_control_frame = ttk.Frame(log_frame)
        log_control_frame.pack(fill=tk.X, pady=(0, 5))

        # 添加清除日志按钮
        self.clear_log_btn = ttk.Button(log_control_frame, text="清除日志", command=self.clear_log)
        self.clear_log_btn.pack(side=tk.LEFT, padx=(0, 5))

        # 添加保存日志按钮
        self.save_log_btn = ttk.Button(log_control_frame, text="保存日志", command=self.save_log)
        self.save_log_btn.pack(side=tk.LEFT, padx=(0, 5))

        # 添加日志级别过滤
        ttk.Label(log_control_frame, text="过滤:").pack(side=tk.LEFT, padx=(20, 5))
        self.log_filter_var = tk.StringVar(value="全部")
        log_filter_combo = ttk.Combobox(log_control_frame, textvariable=self.log_filter_var,
                                       values=["全部", "SERIAL", "DEBUG", "ERROR", "DEMO"], width=8)
        log_filter_combo.pack(side=tk.LEFT)
        log_filter_combo.bind('<<ComboboxSelected>>', self.on_log_filter_changed)

        self.log_text = scrolledtext.ScrolledText(log_frame, height=4, wrap=tk.WORD)
        self.log_text.pack(fill=tk.BOTH, expand=True)

        # 绑定日志区域右键菜单
        self.log_text.bind("<Button-3>", self.show_log_context_menu)

        # 绑定快捷键
        self.root.bind("<Control-l>", lambda e: self.clear_log())
        self.root.bind("<Control-s>", lambda e: self.save_log())
        self.root.bind("<Control-d>", lambda e: self.clear_data())

        # 存储所有日志消息
        self.all_log_messages = []

        # 添加初始日志
        self.add_log("增强监控系统启动完成")
        self.add_log("快捷键: Ctrl+L=清除日志, Ctrl+S=保存日志, Ctrl+D=清除数据")
        self.add_log("数据库连接正常")
        if self.chart_enabled:
            self.add_log("图表功能已启用")
        else:
            self.add_log("图表功能未启用 - 请安装matplotlib")
        if self.demo_mode:
            self.add_log("演示模式：将显示模拟的TDMA传感器数据")

    def create_context_menu(self):
        """创建右键菜单"""
        # 数据表格右键菜单
        self.context_menu = tk.Menu(self.root, tearoff=0)
        self.context_menu.add_command(label="清除实时数据", command=self.clear_data)
        self.context_menu.add_command(label="清除所有数据", command=self.clear_all_data)
        self.context_menu.add_separator()
        self.context_menu.add_command(label="导出数据", command=self.export_data)

        # 日志区域右键菜单
        self.log_context_menu = tk.Menu(self.root, tearoff=0)
        self.log_context_menu.add_command(label="清除日志", command=self.clear_log)
        self.log_context_menu.add_command(label="保存日志", command=self.save_log)
        self.log_context_menu.add_separator()
        self.log_context_menu.add_command(label="复制选中", command=self.copy_selected_log)

        self.sensor_tree.bind("<Button-3>", self.show_context_menu)

    def show_context_menu(self, event):
        """显示数据表格右键菜单"""
        try:
            self.context_menu.tk_popup(event.x_root, event.y_root)
        finally:
            self.context_menu.grab_release()

    def show_log_context_menu(self, event):
        """显示日志区域右键菜单"""
        try:
            self.log_context_menu.tk_popup(event.x_root, event.y_root)
        finally:
            self.log_context_menu.grab_release()

    def copy_selected_log(self):
        """复制选中的日志内容"""
        try:
            selected_text = self.log_text.selection_get()
            self.root.clipboard_clear()
            self.root.clipboard_append(selected_text)
            self.add_log("已复制选中的日志内容到剪贴板")
        except tk.TclError:
            # 没有选中文本
            messagebox.showwarning("警告", "请先选中要复制的日志内容")

    def on_log_filter_changed(self, event=None):
        """日志过滤器改变事件"""
        filter_type = self.log_filter_var.get()

        # 清除当前显示的日志
        self.log_text.delete("1.0", tk.END)

        # 根据过滤条件重新显示日志
        for log_msg in self.all_log_messages:
            if filter_type == "全部":
                self.log_text.insert(tk.END, log_msg)
            elif filter_type in log_msg:
                self.log_text.insert(tk.END, log_msg)

        # 滚动到最新位置
        self.log_text.see(tk.END)

    def show_context_menu(self, event):
        """显示右键菜单"""
        try:
            self.context_menu.tk_popup(event.x_root, event.y_root)
        finally:
            self.context_menu.grab_release()

    def refresh_ports(self):
        """刷新可用串口列表"""
        if not SERIAL_AVAILABLE:
            return

        ports = [port.device for port in serial.tools.list_ports.comports()]
        self.port_combo['values'] = ports
        if ports:
            self.port_combo.set(ports[0])
            self.add_log(f"发现串口: {', '.join(ports)}")
        else:
            self.add_log("未发现可用串口")

    def toggle_connection(self):
        """切换串口连接状态"""
        if not self.is_connected:
            self.connect_serial()
        else:
            self.disconnect_serial()

    def connect_serial(self):
        """连接串口"""
        if not SERIAL_AVAILABLE:
            return

        try:
            port = self.port_var.get()
            baud = int(self.baud_var.get())

            if not port:
                messagebox.showerror("错误", "请选择串口")
                return

            self.serial_port = serial.Serial(port, baud, timeout=1)
            self.is_connected = True

            # 启动读取线程
            self.read_thread = threading.Thread(target=self.read_serial_data, daemon=True)
            self.read_thread.start()

            # 更新界面
            self.status_var.set(f"已连接 - {port}")
            self.connect_btn.config(text="断开")
            self.add_log(f"已连接到串口: {port}, 波特率: {baud}")

        except Exception as e:
            self.add_log(f"[ERROR] 连接失败: {e}")
            messagebox.showerror("连接错误", f"无法连接串口: {str(e)}")

    def disconnect_serial(self):
        """断开串口连接"""
        self.is_connected = False
        if self.serial_port:
            self.serial_port.close()
            self.serial_port = None

        self.status_var.set("未连接")
        self.connect_btn.config(text="连接")
        self.add_log("串口连接已断开")

    def read_serial_data(self):
        """读取串口数据线程"""
        while self.is_connected and self.serial_port:
            try:
                if self.serial_port.in_waiting:
                    line = self.serial_port.readline().decode('utf-8', errors='ignore').strip()
                    if line:
                        self.data_queue.put(('serial', line))
            except Exception as e:
                self.add_log(f"[ERROR] 串口读取错误: {e}")
                break

    def toggle_demo(self):
        """切换演示模式"""
        self.demo_mode = not self.demo_mode
        if self.demo_mode:
            self.start_demo_data()
            self.status_var.set("演示模式 - 运行中")
            self.add_log("[DEBUG] 演示模式已启动")
        else:
            self.status_var.set("演示模式 - 已停止")
            self.add_log("[DEBUG] 演示模式已停止")

    def start_demo_data(self):
        """启动演示数据生成"""
        if self.demo_mode:
            self.generate_demo_data()
            self.root.after(3000, self.start_demo_data)  # 每3秒生成一次数据

    def generate_demo_data(self):
        """生成演示数据"""
        if not self.demo_mode:
            return

        self.demo_counter += 1

        # 模拟多个节点的数据
        for node_id in [1, 2, 3, 4]:
            temp = 25.0 + (self.demo_counter % 10) + node_id * 0.5 + (node_id * 2)
            vib = (self.demo_counter + node_id) % 2
            sync = 1 if self.demo_counter > 3 else 0
            slot = node_id

            demo_line = f"Node {node_id}: Temp={temp:.1f}, Vib={vib}, Sync={sync}, Slot={slot}"
            self.data_queue.put(('demo', demo_line))

    def start_data_processing(self):
        """启动数据处理"""
        self.process_data()

    def process_data(self):
        """处理接收到的数据"""
        try:
            # 批量处理数据，避免频繁更新界面
            processed_count = 0
            max_process_per_cycle = 10  # 每次最多处理10条数据

            while not self.data_queue.empty() and processed_count < max_process_per_cycle:
                data_type, line = self.data_queue.get_nowait()
                self.parse_and_display_data(line, data_type)
                processed_count += 1

        except queue.Empty:
            pass
        finally:
            # 每200ms处理一次数据（降低频率）
            self.root.after(200, self.process_data)

    def parse_and_display_data(self, line, data_type=""):
        """解析并显示数据"""
        # 解析传感器数据 - 支持多种格式

        # 格式1: 标准格式 "Node 1: Temp=25.6, Vib=0, Sync=1, Slot=1"
        sensor_pattern1 = r'Node\s+(\d+).*?Temp=([0-9.]+).*?Vib=(\d+).*?Sync=(\d+).*?Slot=(\d+)'
        match1 = re.search(sensor_pattern1, line, re.IGNORECASE)

        # 格式2: 标准格式 "Node 1: Temp=25.0, Vib=0, Sync=1, Slot=1" (新的TDMA输出格式)
        sensor_pattern2 = r'Node\s+(\d+):\s*Temp=([0-9.]+),\s*Vib=(\d+),\s*Sync=(\d+),\s*Slot=(\d+)'
        match2 = re.search(sensor_pattern2, line, re.IGNORECASE)

        # 格式3: 新格式 "Node 2: Temp=25.0, Vib=0,Slot=1" (缺少Sync字段)
        sensor_pattern3 = r'Node\s+(\d+):\s*Temp=([0-9.]+),\s*Vib=(\d+),\s*Slot=(\d+)'
        match3 = re.search(sensor_pattern3, line, re.IGNORECASE)

        # 格式4: 旧版MASTER接收格式 "MASTER: Received from Node 3 - Temp=298, Vib=0" (兼容性)
        sensor_pattern4 = r'MASTER:\s*Received from Node\s+(\d+).*?Temp=([0-9.]+).*?Vib=(\d+)'
        match4 = re.search(sensor_pattern4, line, re.IGNORECASE)

        match = None
        node_id = None
        temperature = None
        vibration = None
        sync_status = 1  # 默认同步状态
        slot_number = 0  # 默认时隙

        if match1:
            match = match1
            node_id = int(match.group(1))
            temperature = float(match.group(2))
            vibration = int(match.group(3))
            sync_status = int(match.group(4))
            slot_number = int(match.group(5))
        elif match2:
            match = match2
            node_id = int(match.group(1))
            temperature = float(match.group(2))  # 已经是正确的小数格式
            vibration = int(match.group(3))
            sync_status = int(match.group(4))  # 从数据中获取同步状态
            slot_number = int(match.group(5))  # 从数据中获取时隙号
        elif match3:
            match = match3
            node_id = int(match.group(1))
            temperature = float(match.group(2))  # 新格式已经是正确的小数格式
            vibration = int(match.group(3))
            sync_status = 1  # 新格式默认认为已同步（能收到数据说明已同步）
            slot_number = int(match.group(4))  # 从数据中获取时隙号
        elif match4:
            match = match4
            node_id = int(match.group(1))
            temperature = float(match.group(2)) / 10.0  # 298 -> 29.8°C (旧格式转换)
            vibration = int(match.group(3))
            sync_status = 1  # MASTER收到数据说明已同步
            slot_number = node_id  # 使用节点ID作为时隙号

        if match and node_id is not None:
            timestamp = datetime.now()

            # 更新表格显示
            self.update_sensor_table(timestamp, node_id, temperature, vibration, sync_status, slot_number)

            # 保存到内存数据结构（用于图表）
            self.update_node_data(node_id, timestamp, temperature, vibration, sync_status, slot_number)

            # 保存到数据库
            self.save_to_database(timestamp, node_id, temperature, vibration, sync_status, slot_number)

            # 更新节点列表
            self.update_node_lists(node_id)

            # 更新数据统计
            self.update_data_count()

        # 添加到日志，带类型标记
        if data_type:
            log_prefix = f"[{data_type.upper()}]"
            self.add_log(f"{log_prefix} {line}")
        else:
            self.add_log(f"[SERIAL] {line}")

    def update_sensor_table(self, timestamp, node_id, temp, vib, sync, slot):
        """更新传感器数据表格"""
        # 清除旧数据（保留最近50条）
        children = self.sensor_tree.get_children()
        if len(children) >= 50:
            for i in range(10):  # 删除最旧的10条
                if children:
                    self.sensor_tree.delete(children[i])

        # 添加新数据（移除同步状态列）
        time_str = timestamp.strftime("%H:%M:%S")

        item = self.sensor_tree.insert('', 'end', values=(
            time_str, f"节点{node_id}", f"{temp:.1f}", vib, slot
        ))

        # 滚动到最新数据
        self.sensor_tree.see(item)

    def update_node_data(self, node_id, timestamp, temperature, vibration, sync_status, slot_number):
        """更新节点数据（用于图表显示）"""
        node_data = self.node_data[node_id]
        node_data['timestamps'].append(timestamp)
        node_data['temperatures'].append(temperature)
        node_data['vibrations'].append(vibration)
        node_data['slots'].append(slot_number)

        # 标记图表需要更新
        self.chart_needs_update = True

    def save_to_database(self, timestamp, node_id, temp, vib, sync, slot):
        """保存数据到数据库"""
        try:
            cursor = self.db_conn.cursor()
            cursor.execute('''
                INSERT INTO sensor_readings
                (timestamp, node_id, temperature, vibration, sync_status, slot_number)
                VALUES (?, ?, ?, ?, ?, ?)
            ''', (timestamp.isoformat(), node_id, temp, vib, sync, slot))

            # 更新节点信息
            cursor.execute('''
                INSERT OR REPLACE INTO node_info
                (node_id, node_name, last_seen, total_packets, is_active)
                VALUES (?, ?, ?,
                    COALESCE((SELECT total_packets FROM node_info WHERE node_id = ?) + 1, 1),
                    1)
            ''', (node_id, f"节点{node_id}", timestamp.isoformat(), node_id))

            self.db_conn.commit()
        except Exception as e:
            self.add_log(f"数据库保存错误: {e}")

    def update_node_lists(self, node_id):
        """更新节点选择列表"""
        # 更新图表节点选择
        if hasattr(self, 'chart_node_combo'):
            current_values = list(self.chart_node_combo['values'])
            node_name = f"节点{node_id}"
            if node_name not in current_values:
                current_values.append(node_name)
                current_values.sort()
                self.chart_node_combo['values'] = current_values

        # 更新数据库查询节点选择
        if hasattr(self, 'db_node_combo'):
            current_values = list(self.db_node_combo['values'])
            node_name = f"节点{node_id}"
            if node_name not in current_values:
                current_values.append(node_name)
                current_values.sort()
                self.db_node_combo['values'] = current_values

    def update_data_count(self):
        """更新数据统计显示"""
        try:
            cursor = self.db_conn.cursor()
            cursor.execute("SELECT COUNT(*) FROM sensor_readings")
            count = cursor.fetchone()[0]
            self.data_count_var.set(f"{count}条记录")
        except Exception as e:
            self.data_count_var.set("统计错误")

    def add_log(self, message):
        """添加日志信息"""
        if not hasattr(self, 'log_text') or self.log_text is None:
            print(f"[LOG] {message}")
            return

        timestamp = datetime.now().strftime("%H:%M:%S")
        log_message = f"[{timestamp}] {message}\n"

        # 存储到所有日志消息列表（用于过滤）
        if hasattr(self, 'all_log_messages'):
            self.all_log_messages.append(log_message)

            # 限制存储的日志数量
            if len(self.all_log_messages) > 500:
                self.all_log_messages = self.all_log_messages[-250:]

        # 检查当前过滤设置
        should_display = True
        if hasattr(self, 'log_filter_var'):
            filter_type = self.log_filter_var.get()
            if filter_type != "全部" and filter_type not in message:
                should_display = False

        # 只有符合过滤条件的消息才显示
        if should_display:
            self.log_text.insert(tk.END, log_message)
            self.log_text.see(tk.END)

            # 限制显示的日志行数
            lines = self.log_text.get("1.0", tk.END).split('\n')
            if len(lines) > 200:
                # 删除前100行
                self.log_text.delete("1.0", "100.0")

    def clear_log(self):
        """清除日志"""
        if hasattr(self, 'log_text') and self.log_text:
            self.log_text.delete("1.0", tk.END)
        if hasattr(self, 'all_log_messages'):
            self.all_log_messages.clear()
        self.add_log("日志已清除")

    def save_log(self):
        """保存日志到文件"""
        try:
            filename = f"system_log_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt"
            with open(filename, 'w', encoding='utf-8') as f:
                if hasattr(self, 'log_text') and self.log_text:
                    log_content = self.log_text.get("1.0", tk.END)
                    f.write(log_content)

            self.add_log(f"日志已保存到: {filename}")
            messagebox.showinfo("成功", f"日志已保存到: {filename}")
        except Exception as e:
            self.add_log(f"保存日志失败: {e}")
            messagebox.showerror("错误", f"保存日志失败: {e}")

    def clear_data(self):
        """清除数据"""
        try:
            # 清除表格显示的数据
            for item in self.sensor_tree.get_children():
                self.sensor_tree.delete(item)

            # 清除内存中的数据列表
            self.sensor_data_list.clear()

            # 清除图表数据（多节点数据）
            self.node_data.clear()

            # 清除数据队列
            while not self.data_queue.empty():
                try:
                    self.data_queue.get_nowait()
                except queue.Empty:
                    break

            # 标记图表需要更新
            self.chart_needs_update = True

            # 更新数据统计显示
            self.update_data_count()

            self.add_log("[DEBUG] 实时数据已清除（包括表格、内存、图表数据）")

        except Exception as e:
            self.add_log(f"[ERROR] 清除数据失败: {e}")

    def clear_all_data(self):
        """清除所有数据（包括数据库）"""
        try:
            # 询问用户确认
            result = messagebox.askyesno(
                "确认清除",
                "是否清除所有数据？\n\n这将清除：\n• 实时显示数据\n• 图表数据\n• 数据库中的所有记录\n\n此操作不可撤销！"
            )

            if not result:
                return

            # 先清除实时数据
            self.clear_data()

            # 清除数据库数据
            cursor = self.db_conn.cursor()
            cursor.execute("DELETE FROM sensor_readings")
            cursor.execute("DELETE FROM node_info")
            self.db_conn.commit()

            # 重置节点选择列表
            if hasattr(self, 'chart_node_combo'):
                self.chart_node_combo['values'] = ["全部"]
                self.chart_node_combo.set("全部")

            if hasattr(self, 'db_node_combo'):
                self.db_node_combo['values'] = ["全部"]
                self.db_node_combo.set("全部")

            # 刷新数据库统计
            self.refresh_db_stats()

            self.add_log("[DEBUG] 所有数据已清除（包括数据库）")
            messagebox.showinfo("完成", "所有数据已清除")

        except Exception as e:
            self.add_log(f"[ERROR] 清除所有数据失败: {e}")
            messagebox.showerror("错误", f"清除失败: {e}")

    def export_data(self):
        """导出数据到CSV文件"""
        try:
            filename = f"sensor_data_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv"
            cursor = self.db_conn.cursor()
            cursor.execute('''
                SELECT timestamp, node_id, temperature, vibration, sync_status, slot_number
                FROM sensor_readings
                ORDER BY timestamp DESC
                LIMIT 1000
            ''')

            with open(filename, 'w', encoding='utf-8') as f:
                f.write("时间,节点ID,温度,振动,时隙\n")
                for row in cursor.fetchall():
                    f.write(f"{row[0]},{row[1]},{row[2]},{row[3]},{row[5]}\n")

            self.add_log(f"数据已导出到: {filename}")
            messagebox.showinfo("成功", f"数据已导出到: {filename}")
        except Exception as e:
            self.add_log(f"导出失败: {e}")
            messagebox.showerror("错误", f"导出失败: {e}")

    # 图表相关方法
    def start_chart_updates(self):
        """启动图表更新"""
        if self.chart_enabled:
            self.update_charts()

    def update_charts(self):
        """更新图表显示"""
        if not self.chart_enabled:
            return

        try:
            # 只在有新数据或需要更新时才刷新图表
            if self.chart_needs_update:
                self.refresh_charts()
                self.chart_needs_update = False
                self.last_chart_update = datetime.now()
        except Exception as e:
            self.add_log(f"图表更新错误: {e}")
        finally:
            # 定期检查是否需要更新图表
            self.root.after(self.chart_update_interval, self.update_charts)

    def refresh_charts(self):
        """刷新图表数据"""
        if not self.chart_enabled:
            return

        try:
            # 清除所有子图
            self.temp_ax.clear()
            self.vib_ax.clear()
            self.sync_ax.clear()
            self.combined_ax.clear()

            # 获取选中的节点和时间范围
            selected_node = self.chart_node_var.get()
            time_range = self.time_range_var.get()
            chart_type = self.chart_type_var.get()

            # 计算时间范围
            now = datetime.now()
            if time_range == "最近1分钟":
                start_time = now - timedelta(minutes=1)
            elif time_range == "最近5分钟":
                start_time = now - timedelta(minutes=5)
            elif time_range == "最近10分钟":
                start_time = now - timedelta(minutes=10)
            elif time_range == "最近30分钟":
                start_time = now - timedelta(minutes=30)
            elif time_range == "最近1小时":
                start_time = now - timedelta(hours=1)
            else:
                start_time = None

            # 绘制图表
            if selected_node == "全部":
                self.plot_all_nodes(start_time, chart_type)
            else:
                node_id = int(selected_node.replace("节点", ""))
                self.plot_single_node(node_id, start_time, chart_type)

            # 使用draw_idle()代替draw()以提高性能
            self.canvas.draw_idle()

        except Exception as e:
            self.add_log(f"图表刷新错误: {e}")

    def plot_all_nodes(self, start_time, chart_type):
        """绘制所有节点的图表"""
        colors = ['blue', 'red', 'green', 'orange', 'purple', 'brown']

        for i, (node_id, data) in enumerate(self.node_data.items()):
            if not data['timestamps']:
                continue

            # 过滤时间范围
            timestamps = list(data['timestamps'])
            temperatures = list(data['temperatures'])
            vibrations = list(data['vibrations'])

            if start_time:
                filtered_data = [(t, temp, vib) for t, temp, vib in
                               zip(timestamps, temperatures, vibrations) if t >= start_time]
                if filtered_data:
                    timestamps, temperatures, vibrations = zip(*filtered_data)
                else:
                    continue

            color = colors[i % len(colors)]
            label = f"节点{node_id}"

            # 根据图表类型绘制
            if chart_type in ["温度", "温度+振动"]:
                self.temp_ax.plot(timestamps, temperatures, color=color, label=label, marker='o', markersize=3)

            if chart_type in ["振动", "温度+振动"]:
                self.vib_ax.plot(timestamps, vibrations, color=color, label=label, marker='s', markersize=3)

        # 设置图表标题和标签
        if chart_type in ["温度", "温度+振动"]:
            self.set_chart_title(self.temp_ax, '多节点温度趋势', 'Multi-Node Temperature Trend')
            self.set_chart_label(self.temp_ax, 'ylabel', '温度 (°C)', 'Temperature (°C)')
            self.temp_ax.legend()
            self.temp_ax.grid(True, alpha=0.3)

        if chart_type in ["振动", "温度+振动"]:
            self.set_chart_title(self.vib_ax, '多节点振动状态', 'Multi-Node Vibration Status')
            self.set_chart_label(self.vib_ax, 'ylabel', '振动值', 'Vibration Value')
            self.vib_ax.legend()
            self.vib_ax.grid(True, alpha=0.3)

    def plot_single_node(self, node_id, start_time, chart_type):
        """绘制单个节点的图表"""
        if node_id not in self.node_data:
            return

        data = self.node_data[node_id]
        if not data['timestamps']:
            return

        # 过滤时间范围
        timestamps = list(data['timestamps'])
        temperatures = list(data['temperatures'])
        vibrations = list(data['vibrations'])

        if start_time:
            filtered_data = [(t, temp, vib) for t, temp, vib in
                           zip(timestamps, temperatures, vibrations) if t >= start_time]
            if filtered_data:
                timestamps, temperatures, vibrations = zip(*filtered_data)
            else:
                return

        # 绘制图表
        if chart_type in ["温度", "温度+振动"]:
            self.temp_ax.plot(timestamps, temperatures, 'b-o', markersize=4)
            self.set_chart_title(self.temp_ax, f'节点{node_id} - 温度趋势', f'Node{node_id} - Temperature Trend')
            self.set_chart_label(self.temp_ax, 'ylabel', '温度 (°C)', 'Temperature (°C)')
            self.temp_ax.grid(True, alpha=0.3)

        if chart_type in ["振动", "温度+振动"]:
            self.vib_ax.plot(timestamps, vibrations, 'r-s', markersize=4)
            self.set_chart_title(self.vib_ax, f'节点{node_id} - 振动状态', f'Node{node_id} - Vibration Status')
            self.set_chart_label(self.vib_ax, 'ylabel', '振动值', 'Vibration Value')
            self.vib_ax.grid(True, alpha=0.3)



    def on_chart_node_changed(self, event=None):
        """图表节点选择改变事件"""
        self.chart_needs_update = True
        self.refresh_charts_if_needed()

    def on_time_range_changed(self, event=None):
        """时间范围改变事件"""
        self.chart_needs_update = True
        self.refresh_charts_if_needed()

    def on_chart_type_changed(self, event=None):
        """图表类型改变事件"""
        self.chart_needs_update = True
        self.refresh_charts_if_needed()

    def refresh_charts_if_needed(self):
        """只在需要时刷新图表"""
        now = datetime.now()
        # 限制图表更新频率，避免过于频繁的刷新
        if (now - self.last_chart_update).total_seconds() > 1.0:  # 最少间隔1秒
            self.refresh_charts()
            self.last_chart_update = now
            self.chart_needs_update = False

    # 数据库管理方法
    def refresh_db_stats(self):
        """刷新数据库统计信息"""
        try:
            cursor = self.db_conn.cursor()

            # 总记录数
            cursor.execute("SELECT COUNT(*) FROM sensor_readings")
            total_records = cursor.fetchone()[0]

            # 活跃节点数
            cursor.execute("SELECT COUNT(DISTINCT node_id) FROM sensor_readings")
            active_nodes = cursor.fetchone()[0]

            # 最新记录时间
            cursor.execute("SELECT MAX(timestamp) FROM sensor_readings")
            latest_time = cursor.fetchone()[0]

            # 数据库大小
            cursor.execute("SELECT page_count * page_size as size FROM pragma_page_count(), pragma_page_size()")
            db_size = cursor.fetchone()[0] if cursor.fetchone() else 0

            stats_text = f"""数据库统计信息:
总记录数: {total_records}
活跃节点数: {active_nodes}
最新记录: {latest_time or '无'}
数据库大小: {db_size / 1024:.1f} KB"""

            self.stats_text.delete("1.0", tk.END)
            self.stats_text.insert("1.0", stats_text)

        except Exception as e:
            self.add_log(f"刷新统计信息失败: {e}")

    def query_database(self):
        """查询数据库"""
        try:
            # 清除现有结果
            for item in self.db_tree.get_children():
                self.db_tree.delete(item)

            cursor = self.db_conn.cursor()

            # 构建查询条件
            selected_node = self.db_node_var.get()
            if selected_node == "全部":
                query = '''
                    SELECT id, timestamp, node_id, temperature, vibration, sync_status, slot_number
                    FROM sensor_readings
                    ORDER BY timestamp DESC
                    LIMIT 100
                '''
                cursor.execute(query)
            else:
                node_id = int(selected_node.replace("节点", ""))
                query = '''
                    SELECT id, timestamp, node_id, temperature, vibration, sync_status, slot_number
                    FROM sensor_readings
                    WHERE node_id = ?
                    ORDER BY timestamp DESC
                    LIMIT 100
                '''
                cursor.execute(query, (node_id,))

            # 显示结果
            for row in cursor.fetchall():
                # 格式化时间显示
                try:
                    timestamp = datetime.fromisoformat(row[1]).strftime("%H:%M:%S")
                except:
                    timestamp = row[1]

                self.db_tree.insert('', 'end', values=(
                    row[0], timestamp, f"节点{row[2]}", f"{row[3]:.1f}",
                    row[4], row[6]
                ))

        except Exception as e:
            self.add_log(f"查询数据库失败: {e}")
            messagebox.showerror("错误", f"查询失败: {e}")

    def export_database(self):
        """导出数据库数据"""
        try:
            filename = f"database_export_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv"
            cursor = self.db_conn.cursor()
            cursor.execute('''
                SELECT timestamp, node_id, temperature, vibration, sync_status, slot_number
                FROM sensor_readings
                ORDER BY timestamp DESC
            ''')

            with open(filename, 'w', encoding='utf-8') as f:
                f.write("时间,节点ID,温度,振动,同步状态,时隙\n")
                for row in cursor.fetchall():
                    f.write(f"{row[0]},{row[1]},{row[2]},{row[3]},{row[4]},{row[5]}\n")

            self.add_log(f"数据库已导出到: {filename}")
            messagebox.showinfo("成功", f"数据库已导出到: {filename}")
        except Exception as e:
            self.add_log(f"导出数据库失败: {e}")
            messagebox.showerror("错误", f"导出失败: {e}")

    def cleanup_old_data(self):
        """清理旧数据"""
        try:
            # 询问用户确认
            result = messagebox.askyesno("确认", "是否删除7天前的数据？此操作不可撤销。")
            if not result:
                return

            cursor = self.db_conn.cursor()
            cutoff_date = (datetime.now() - timedelta(days=7)).isoformat()

            cursor.execute("DELETE FROM sensor_readings WHERE timestamp < ?", (cutoff_date,))
            deleted_count = cursor.rowcount

            self.db_conn.commit()

            self.add_log(f"已删除 {deleted_count} 条旧数据")
            messagebox.showinfo("完成", f"已删除 {deleted_count} 条7天前的数据")

            # 刷新统计信息
            self.refresh_db_stats()

        except Exception as e:
            self.add_log(f"清理数据失败: {e}")
            messagebox.showerror("错误", f"清理失败: {e}")

def main():
    root = tk.Tk()
    app = EnhancedTDMAMonitor(root)

    # 设置窗口关闭事件
    def on_closing():
        if app.is_connected:
            app.disconnect_serial()
        if hasattr(app, 'db_conn'):
            app.db_conn.close()
        root.destroy()

    root.protocol("WM_DELETE_WINDOW", on_closing)
    root.mainloop()

if __name__ == "__main__":
    main()
