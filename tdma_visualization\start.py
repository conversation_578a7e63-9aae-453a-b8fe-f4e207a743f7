#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
TDMA可视化系统启动脚本
自动检查依赖并启动主程序
"""

import sys
import subprocess
import os

def check_python_version():
    """检查Python版本"""
    if sys.version_info < (3, 7):
        print("错误: 需要Python 3.7或更高版本")
        print(f"当前版本: {sys.version}")
        return False
    print(f"Python版本: {sys.version}")
    return True

def install_requirements():
    """安装依赖包"""
    requirements_file = "requirements.txt"
    if not os.path.exists(requirements_file):
        print("警告: requirements.txt文件不存在")
        return True
    
    try:
        print("正在安装依赖包...")
        subprocess.check_call([sys.executable, "-m", "pip", "install", "-r", requirements_file])
        print("依赖包安装完成")
        return True
    except subprocess.CalledProcessError as e:
        print(f"依赖包安装失败: {e}")
        return False

def start_main_program():
    """启动主程序"""
    try:
        print("启动TDMA可视化监控系统...")

        # 首先尝试完整版本
        try:
            import main
            main.main()
        except ImportError as e:
            print(f"完整版本导入失败: {e}")
            print("尝试启动简化版本...")

            # 如果完整版本失败，使用简化版本
            import simple_monitor
            simple_monitor.main()

    except ImportError as e:
        print(f"导入错误: {e}")
        print("请确保所有依赖包已正确安装")
        return False
    except Exception as e:
        print(f"程序运行错误: {e}")
        return False
    return True

def main():
    """主函数"""
    print("=" * 50)
    print("TDMA传感器网络可视化监控系统")
    print("=" * 50)
    print()
    
    # 检查Python版本
    if not check_python_version():
        input("按回车键退出...")
        return
    
    # 安装依赖
    if not install_requirements():
        input("按回车键退出...")
        return
    
    print()
    
    # 启动主程序
    if not start_main_program():
        input("按回车键退出...")
        return
    
    print("\n程序已退出")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n程序被用户中断")
    except Exception as e:
        print(f"\n启动脚本错误: {e}")
    finally:
        input("按回车键退出...")
