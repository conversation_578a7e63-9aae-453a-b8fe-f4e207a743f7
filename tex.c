/***********************************************************************************
  Filename: light_switch.c

  Description:  TDMA Sensor Network Implementation
  This application implements a TDMA-based sensor network with master and slave
nodes.

  Configuration:
  - Uncomment #define MASTER_NODE for master node
  - Uncomment #define SLAVE_NODE for slave node
  - Set NODE_ID for slave nodes (1, 2, or 3)

***********************************************************************************/

/***********************************************************************************
 * INCLUDES
 */
#include "basic_rf.h"
#include "hal_button.h"
#include "hal_mcu.h"
#include "hal_rf.h"
#include "hal_uart.h"
#include "util_lcd.h"
#include <hal_assert.h>
#include <hal_board.h>
#include <hal_int.h>
#include <hal_joystick.h>
#include <hal_lcd.h>
#include <hal_led.h>

// Include CC2530 specific headers for timer and GPIO
#if (chip == 2530 || chip == 2531)
#include <ioCC2530.h>
#endif

// Include standard C library
#include <string.h>

/***********************************************************************************
 * NODE CONFIGURATION - MODIFY THESE FOR DIFFERENT NODES
 */
// Uncomment ONE of the following:
// #define MASTER_NODE // Master node configuration
#define SLAVE_NODE // Slave node configuration

// Ensure only one node type is defined
#ifdef MASTER_NODE
#ifdef SLAVE_NODE
#error "Cannot define both MASTER_NODE and SLAVE_NODE"
#endif
#else
#ifndef SLAVE_NODE
#define SLAVE_NODE
#endif
#endif

#define NODE_ID 1 // Node ID: 0=Master, 1-3=Slave nodes

/***********************************************************************************
 * TDMA Protocol Constants
 */
#define RF_CHANNEL 25 // 2.4 GHz RF channel
#define PAN_ID 0x2013 // Network PAN ID

// Node address definitions
#define MASTER_NODE_ADDR 0x0000     // Master node address
#define SLAVE_NODE_BASE_ADDR 0x1000 // Slave node base address
#define SLAVE_NODE_1_ADDR (SLAVE_NODE_BASE_ADDR + 1)
#define SLAVE_NODE_2_ADDR (SLAVE_NODE_BASE_ADDR + 2)
#define SLAVE_NODE_3_ADDR (SLAVE_NODE_BASE_ADDR + 3)

// TDMA slot parameters
#define TDMA_SUPERFRAME_LENGTH 100 // Superframe length 100ms
#define TDMA_SLOT_LENGTH 20        // Slot length 20ms
#define TDMA_TOTAL_SLOTS 5         // Total number of slots
#define TDMA_GUARD_TIME 2          // Guard time 2ms

// Slot allocation
#define SLOT_MASTER_BEACON 0 // Master node beacon slot
#define SLOT_SLAVE_1 1       // Slave node 1 data slot
#define SLOT_SLAVE_2 2       // Slave node 2 data slot
#define SLOT_SLAVE_3 3       // Slave node 3 data slot
#define SLOT_MAINTENANCE 4   // Maintenance slot

// Packet types
#define FRAME_TYPE_DATA 0x01    // Data frame
#define FRAME_TYPE_CONTROL 0x02 // Control frame
#define FRAME_TYPE_BEACON 0x03  // Beacon frame

// LED control commands
#define LED_CMD_OFF 0x00    // Turn off LED
#define LED_CMD_ON 0x01     // Turn on LED
#define LED_CMD_TOGGLE 0x02 // Toggle LED

// Maximum payload length
#define MAX_PAYLOAD_LENGTH 64

// Legacy compatibility definitions
#define SWITCH_ADDR 0x2520
#define LIGHT_ADDR 0xBEEF
#define APP_PAYLOAD_LENGTH 16
#define LIGHT_TOGGLE_CMD 0

// Application role (for compatibility)
#define NONE 0
#define SWITCH 1
#define LIGHT 2

/***********************************************************************************
 * DATA STRUCTURES
 */

// TDMA frame header (compatible with master node)
typedef struct {
  uint8 frameType;   // Frame type: 0x01=data, 0x02=control, 0x03=beacon
  uint8 nodeId;      // Node ID: 0=master, 1-3=slave
  uint8 timeSlot;    // Time slot number
  uint8 sequenceNum; // Sequence number
  uint16 timestamp;  // Timestamp
} tdma_header_t;

// Beacon frame structure (enhanced with timestamp for TDMA sync)
typedef struct {
  tdma_header_t header;                 // TDMA header
  uint16 superframeId;                  // Superframe ID
  uint32 masterTimestamp;               // Master node Timer3 timestamp for sync
  uint16 slotLength;                    // Slot length in ms
  uint8 slotSchedule[TDMA_TOTAL_SLOTS]; // Slot schedule
  uint8 networkStatus;                  // Network status
} beacon_frame_t;

// Sensor data frame structure (slave node, compatible with master)
typedef struct {
  tdma_header_t header; // TDMA header
  uint16 temperature;   // Temperature value
  uint16 vibration;     // Vibration value
  uint8 ledStatus;      // LED status
  uint8 batteryLevel;   // Battery level
  uint8 checksum;       // Checksum
} sensor_data_packet_t;

// LED control frame structure (master to slave, compatible)
typedef struct {
  tdma_header_t header; // TDMA header
  uint8 targetNode;     // Target node ID
  uint8 ledCommand;     // LED command
  uint8 reserved[2];    // Reserved bytes
} led_control_packet_t;

/***********************************************************************************
 * LOCAL VARIABLES
 */
static basicRfCfg_t basicRfConfig;
static uint8 txBuffer[MAX_PAYLOAD_LENGTH];
static uint8 rxBuffer[MAX_PAYLOAD_LENGTH];

// TDMA protocol variables
static uint16 currentSuperframe = 0; // Current superframe counter
static uint8 currentSlot = 0;        // Current slot
static uint16 slotTimer = 0;         // Slot timer
static uint8 sequenceNumber = 0;     // Sequence number counter
static uint8 networkSynced = FALSE;  // Network synchronization status
static uint16 syncTimeout = 0;       // Sync timeout counter

// Interrupt-driven slot notification flags
static volatile uint8 slotFlags = 0; // Bit flags for slot arrival notifications
#define SLOT_FLAG_BEACON 0x01        // Beacon slot arrived
#define SLOT_FLAG_SLAVE_1 0x02       // Slave 1 slot arrived
#define SLOT_FLAG_SLAVE_2 0x04       // Slave 2 slot arrived
#define SLOT_FLAG_SLAVE_3 0x08       // Slave 3 slot arrived

// Sensor data
static uint16 temperatureValue = 0; // Temperature value
static uint16 vibrationValue = 0;   // Vibration value
static uint8 ledState = 0;          // LED status

// Send statistics
static uint16 sendSuccessCount = 0; // Successful send count
static uint16 sendFailedCount = 0;  // Failed send count
static uint8 lastSendStatus = 0;    // Last send status (0=success, 1=failed)

// Timer test variables
static uint32 timerTestCount = 0;  // Timer interrupt counter
static uint8 timerWorking = FALSE; // Timer working status
static uint32 lastBeaconTime = 0;  // 修复：添加beacon时间跟踪

// Node configuration
static uint16 myNodeAddr = SLAVE_NODE_BASE_ADDR + NODE_ID;
static uint8 myNodeId = NODE_ID;

// Legacy compatibility variables
static uint8 pTxData[APP_PAYLOAD_LENGTH];
static uint8 pRxData[APP_PAYLOAD_LENGTH];

/***********************************************************************************
 * LOCAL FUNCTIONS
 */
// TDMA protocol functions
static void tdmaInit(void);
static void tdmaConfigureTimer3(void);
static uint8 tdmaGetCurrentSlot(void);
static void tdmaWaitForSlot(uint8 slot);
static void tdmaProcessBeacon(uint8 *data, uint8 length);

// Sensor driver functions
static void sensorsInit(void);
static uint16 readTemperature(void);
static uint16 readVibration(void);

// Data processing functions
static void sendSensorData(void);
static void processControlCommand(uint8 *data, uint8 length);
static uint8 calculateChecksum(uint8 *data, uint8 length);

// Timer test functions
static void timerTestInit(void);
static void timerTestCheck(void);

// Debug functions
static void debugInit(void);
static void debugPrintSensorData(uint16 temp, uint16 vib);
static void debugPrintString(char *str);
static void debugTestVibrationPins(void);
static void debugManualPinTest(void);

// Application layer functions
#ifdef MASTER_NODE
static void masterNodeApp(void);
static void sendBeaconFrame(void);
#endif

#ifdef SLAVE_NODE
static void slaveNodeApp(void);
#endif

/***********************************************************************************
 * Sensor Driver Implementation
 */

/**
 * @brief Initialize sensors
 */
static void sensorsInit(void) {
  // Configure ALL possible pins as input to test vibration sensor

  // Configure P0.0, P0.1 as input (potential vibration sensor pins)
  P0SEL &= ~0x03; // Clear special function for P0.0, P0.1
  P0DIR &= ~0x03; // Set as input
  P0INP &= ~0x03; // Enable pull-up/pull-down

  // Configure P1 pins carefully - avoid LED pins
  // P1.0 = LED1, P1.1 = LED2, P1.4 = LED4 - keep as output for LEDs
  // P1.2 = vibration sensor, P1.3, P1.5 = test pins - set as input

  P1SEL &= ~0x2C; // Clear special function for P1.2, P1.3, P1.5 only
  P1DIR &= ~0x2C; // Set P1.2, P1.3, P1.5 as input (keep LED pins as output)
  P1INP &= ~0x2C; // Enable pull-up/pull-down on P1.2, P1.3, P1.5

  // Configure P2.0, P2.1 as input (potential vibration sensor pins)
  P2SEL &= ~0x03; // Clear special function for P2.0, P2.1
  P2DIR &= ~0x03; // Set as input
  P2INP &= ~0x03; // Enable pull-up/pull-down

  // Ensure LED pins are configured as output
  P1DIR |= 0x13;  // Set P1.0 (LED1), P1.1 (LED2), P1.4 (LED4) as output
  P1SEL &= ~0x13; // Clear special function for LED pins

  // Wait for pins to stabilize
  halMcuWaitMs(100);

  // Send debug info about pin configuration
  halUartWrite((uint8 *)"Pin config: LEDs as output, sensors as input\r\n", 47);
}

/**
 * @brief Read temperature sensor value
 * @return Temperature value (simulated)
 */
static uint16 readTemperature(void) {
  halUartWrite((uint8 *)"DEBUG: Simulating temperature\r\n", 32);

  // Simulate temperature reading
  // In real implementation, read from DTH11 sensor
  static uint16 temp = 250;         // 25.0°C
  temp += (sequenceNumber % 5) - 2; // Add some variation

  halUartWrite((uint8 *)"DEBUG: Temperature simulation complete\r\n", 41);
  return temp;
}

/**
 * @brief Read vibration sensor value
 * @return Vibration value (0=no vibration, 1=vibration detected)
 */
static uint16 readVibration(void) {
  // Simple GPIO read from P1.2 (SW-520D sensor)
  // SW-520D outputs LOW when vibration detected, HIGH when stable
  halUartWrite((uint8 *)"DEBUG: Reading P1.2 pin\r\n", 26);

  uint8 sensorValue = (P1 & 0x04) ? 1 : 0; // Read P1.2 (bit 2)

  halUartWrite((uint8 *)"DEBUG: P1.2 read complete\r\n", 28);

  // Return inverted value: 0=stable, 1=vibration
  return sensorValue ? 0 : 1;
}

/***********************************************************************************
 * Debug Functions Implementation
 */

/**
 * @brief Initialize debug UART
 */
static void debugInit(void) {
  // UART already initialized in main(), just send test message
  halUartWrite((uint8 *)"Debug system ready\r\n", 20);

  // Add immediate pin test
  halUartWrite((uint8 *)"=== PIN TEST START ===\r\n", 25);

  // Test if we can get here
  halUartWrite((uint8 *)"About to call pin test...\r\n", 28);

  debugTestVibrationPins();

  halUartWrite((uint8 *)"=== PIN TEST END ===\r\n", 23);
}

/**
 * @brief Print string to UART
 * @param str String to print
 */
static void debugPrintString(char *str) {
  uint8 len = 0;
  char *p = str;

  // Calculate string length
  while (*p++)
    len++;

  // Send to UART
  halUartWrite((uint8 *)str, len);
}

/**
 * @brief Print sensor data to UART
 * @param temp Temperature value
 * @param vib Vibration value
 */
static void debugPrintSensorData(uint16 temp, uint16 vib) {
  // Use safe, segmented output to avoid sprintf memory corruption
  halUartWrite((uint8 *)"Node", 4);

  // Output node ID safely
  if (myNodeId == 1)
    halUartWrite((uint8 *)"1", 1);
  else if (myNodeId == 2)
    halUartWrite((uint8 *)"2", 1);
  else if (myNodeId == 3)
    halUartWrite((uint8 *)"3", 1);
  else
    halUartWrite((uint8 *)"?", 1);

  halUartWrite((uint8 *)": Temp=", 7);

  // Simple temperature output (avoid sprintf)
  if (temp < 1000) {
    char tempStr[4];
    tempStr[0] = '0' + (temp / 100);
    tempStr[1] = '0' + ((temp / 10) % 10);
    tempStr[2] = '0' + (temp % 10);
    halUartWrite((uint8 *)tempStr, 3);
  } else {
    halUartWrite((uint8 *)"999", 3); // Cap at 999
  }

  halUartWrite((uint8 *)", Vib=", 6);
  halUartWrite((uint8 *)(vib ? "1" : "0"), 1);

  halUartWrite((uint8 *)", Sync=", 7);
  halUartWrite((uint8 *)(networkSynced ? "1" : "0"), 1);

  halUartWrite((uint8 *)", Send=", 7);
  halUartWrite((uint8 *)((lastSendStatus == SUCCESS) ? "OK" : "FAIL"),
               (lastSendStatus == SUCCESS) ? 2 : 4);

  halUartWrite((uint8 *)"\r\n", 2);
}

/**
 * @brief Print send statistics to UART
 */
static void debugPrintSendStats(void) {
  // 修复：避免sprintf导致栈溢出，使用简单输出
  halUartWrite((uint8 *)"Send Stats: Success=", 20);

  // 简单的成功计数输出
  if (sendSuccessCount > 99) {
    halUartWrite((uint8 *)"99+", 3);
  } else if (sendSuccessCount > 9) {
    halUartWrite((uint8 *)"10+", 3);
  } else {
    halUartWrite((uint8 *)(sendSuccessCount ? "OK" : "0"),
                 sendSuccessCount ? 2 : 1);
  }

  halUartWrite((uint8 *)", Failed=", 9);

  // 简单的失败计数输出
  if (sendFailedCount > 99) {
    halUartWrite((uint8 *)"99+", 3);
  } else if (sendFailedCount > 9) {
    halUartWrite((uint8 *)"10+", 3);
  } else {
    halUartWrite((uint8 *)(sendFailedCount ? "ERR" : "0"),
                 sendFailedCount ? 3 : 1);
  }

  halUartWrite((uint8 *)"\r\n", 2);
}

/**
 * @brief Initialize Timer3 test (based on working example)
 */
static void timerTestInit(void) {
  halUartWrite((uint8 *)"Timer Test: Initializing T3...\r\n", 33);

  // Disable watchdog first
  WDCTL = 0x00; // Disable watchdog timer

  // Use Timer3 configuration from working example
  T3CTL = 0x00;   // Stop timer first
  T3CTL |= 0x08;  // Enable interrupt
  T3IE = 1;       // Enable T3 interrupt
  T3CTL |= 0xE0;  // 128 prescaler
  T3CTL &= ~0x03; // Auto-reload mode
  T3CTL |= 0x10;  // Start timer
  EA = 1;         // Enable global interrupts

  timerTestCount = 0;
  timerWorking = FALSE;

  halUartWrite(
      (uint8 *)"Timer Test: T3 configured, watching for interrupts...\r\n", 57);
  halUartWrite((uint8 *)"LED2 should flash when timer works\r\n", 37);
}

/**
 * @brief Check timer test status
 */
static void timerTestCheck(void) {
  static uint32 lastCount = 0;

  if (timerTestCount != lastCount) {
    timerWorking = TRUE;
    // 修复：避免sprintf导致栈溢出，使用简单输出
    halUartWrite((uint8 *)"Timer Test: Count=", 18);
    // 简单的数字输出，避免sprintf
    if (timerTestCount > 9999) {
      halUartWrite((uint8 *)"9999+", 5);
    } else if (timerTestCount > 999) {
      halUartWrite((uint8 *)"1000+", 5);
    } else {
      halUartWrite((uint8 *)"OK", 2);
    }
    halUartWrite((uint8 *)" (WORKING!)\r\n", 13);
    lastCount = timerTestCount;
  } else {
    if (!timerWorking) {
      halUartWrite(
          (uint8 *)"Timer Test: No interrupts detected (NOT WORKING)\r\n", 52);
    }
  }
}

/**
 * @brief Test vibration sensor on different pins
 */
static void debugTestVibrationPins(void) {
  // Read all port registers
  uint8 p0_val = P0;
  uint8 p1_val = P1;
  uint8 p2_val = P2;

  // Test multiple pins to find the vibration sensor
  uint8 p0_0 = (p0_val & 0x01) ? 1 : 0; // P0.0
  uint8 p0_1 = (p0_val & 0x02) ? 1 : 0; // P0.1
  uint8 p1_0 = (p1_val & 0x01) ? 1 : 0; // P1.0
  uint8 p1_1 = (p1_val & 0x02) ? 1 : 0; // P1.1
  uint8 p1_2 = (p1_val & 0x04) ? 1 : 0; // P1.2
  uint8 p1_3 = (p1_val & 0x08) ? 1 : 0; // P1.3
  uint8 p1_4 = (p1_val & 0x10) ? 1 : 0; // P1.4
  uint8 p1_5 = (p1_val & 0x20) ? 1 : 0; // P1.5
  uint8 p2_0 = (p2_val & 0x01) ? 1 : 0; // P2.0
  uint8 p2_1 = (p2_val & 0x02) ? 1 : 0; // P2.1

  // Output all pin states in compact format
  halUartWrite((uint8 *)"P0:", 3);
  halUartWrite((uint8 *)(p0_0 ? "1" : "0"), 1);
  halUartWrite((uint8 *)(p0_1 ? "1" : "0"), 1);
  halUartWrite((uint8 *)" P1:", 4);
  halUartWrite((uint8 *)(p1_0 ? "1" : "0"), 1);
  halUartWrite((uint8 *)(p1_1 ? "1" : "0"), 1);
  halUartWrite((uint8 *)(p1_2 ? "1" : "0"), 1);
  halUartWrite((uint8 *)(p1_3 ? "1" : "0"), 1);
  halUartWrite((uint8 *)(p1_4 ? "1" : "0"), 1);
  halUartWrite((uint8 *)(p1_5 ? "1" : "0"), 1);
  halUartWrite((uint8 *)" P2:", 4);
  halUartWrite((uint8 *)(p2_0 ? "1" : "0"), 1);
  halUartWrite((uint8 *)(p2_1 ? "1" : "0"), 1);
  halUartWrite((uint8 *)" ", 1);
}

/**
 * @brief Manual pin test - instructions for user
 */
static void debugManualPinTest(void) {
  halUartWrite((uint8 *)"=== MANUAL PIN TEST ===\r\n", 26);
  halUartWrite(
      (uint8 *)"Connect vibration sensor DO pin to different pins:\r\n", 53);
  halUartWrite(
      (uint8 *)"Try P0.0, P0.1, P1.0, P1.1, P1.2, P1.3, P1.4, P1.5\r\n", 55);
  halUartWrite((uint8 *)"Watch for pin value changes when shaking sensor\r\n",
               50);
  halUartWrite((uint8 *)"========================\r\n", 26);
}

/***********************************************************************************
 * TDMA Protocol Implementation
 */

/**
 * @brief Initialize TDMA protocol using Timer3 (based on working example)
 */
static void tdmaInit(void) {
  halUartWrite((uint8 *)"TDMA: Init start\r\n", 18);

  // Disable watchdog first
  WDCTL = 0x00;

  // Initialize TDMA variables
  currentSlot = 0;
  currentSuperframe = 0;
  timerTestCount = 0;
  slotFlags = 0;
  networkSynced = FALSE;
  timerWorking = FALSE;

#ifdef MASTER_NODE
  // Master node: Configure Timer3 immediately (it's the time reference)
  halUartWrite((uint8 *)"TDMA: Master node - configuring Timer3\r\n", 41);
  tdmaConfigureTimer3();
#else
  // Slave node: Wait for beacon before configuring Timer3
  halUartWrite((uint8 *)"TDMA: Slave node - waiting for master beacon\r\n", 47);
  // Timer3 will be configured in tdmaProcessBeacon() after receiving sync
  // signal
#endif

  halUartWrite((uint8 *)"TDMA: Init complete\r\n", 21);
}

/**
 * @brief Configure Timer3 for TDMA slot timing
 */
static void tdmaConfigureTimer3(void) {
  halUartWrite((uint8 *)"TDMA: Configuring Timer3...\r\n", 30);

  // Use Timer3 configuration (based on working example)
  T3CTL = 0x00;  // Stop timer first
  T3CTL |= 0x08; // Enable interrupt
  T3IE = 1;      // Enable T3 interrupt

  // Configure for 20ms timing
  // System clock 16MHz, prescaler 128: 128/16000000 * N = 0.02s
  // N = 0.02 * 16000000 / 128 = 2500
  // 2500/255 = 9.8, so we need about 10 interrupts per slot
  T3CTL |= 0xE0;  // 128 prescaler
  T3CTL &= ~0x03; // Auto-reload mode
  T3CTL |= 0x10;  // Start timer

  EA = 1; // Enable global interrupts

  halUartWrite((uint8 *)"TDMA: Timer3 configured and started\r\n", 38);
}

/**
 * @brief Get current slot
 * @return Current slot number
 */
static uint8 tdmaGetCurrentSlot(void) { return currentSlot; }

/**
 * @brief Wait for specific slot
 * @param slot Target slot number
 */
static void tdmaWaitForSlot(uint8 slot) {
  uint16 timeout = 0;
  uint8 targetFlag = 0;

  // Check if we should use hardware or software timing
  // Different logic for master and slave nodes
#ifdef MASTER_NODE
  // Master node: only needs timer working (it's the time reference)
  if (!timerWorking) {
    halUartWrite((uint8 *)"TDMA: Master using software timing\r\n", 37);
    halMcuWaitMs(TDMA_SLOT_LENGTH);
    return;
  }
#else
  // Slave node: needs both timer working AND network synchronized
  if (!timerWorking || !networkSynced) {
    halUartWrite((uint8 *)"TDMA: Slave using software timing\r\n", 36);
    halMcuWaitMs(TDMA_SLOT_LENGTH);
    return;
  }
#endif

  // Determine which flag to wait for
  switch (slot) {
  case SLOT_MASTER_BEACON:
    targetFlag = SLOT_FLAG_BEACON;
    break;
  case SLOT_SLAVE_1:
    targetFlag = SLOT_FLAG_SLAVE_1;
    break;
  case SLOT_SLAVE_2:
    targetFlag = SLOT_FLAG_SLAVE_2;
    break;
  case SLOT_SLAVE_3:
    targetFlag = SLOT_FLAG_SLAVE_3;
    break;
  default:
    halUartWrite((uint8 *)"ERROR: Invalid slot!\r\n", 22);
    return;
  }

  // Wait for interrupt to set the flag (synchronized mode)
  while (!(slotFlags & targetFlag)) {
    halMcuWaitMs(1); // Wait 1ms
    timeout++;

    // Timeout protection (max 200ms wait)
    if (timeout > 200) {
      halUartWrite((uint8 *)"TIMEOUT: Lost sync, using software timing\r\n",
                   44);
      // 修复：不要在这里重置networkSynced
      // 只使用软件定时，让tdmaProcessBeacon来管理同步状态
      halMcuWaitMs(TDMA_SLOT_LENGTH);
      break;
    }
  }

  // Clear the flag after processing (only if we didn't timeout)
  if (timeout <= 200) {
    slotFlags &= ~targetFlag;
  }
}

/**
 * @brief Process received beacon frame (slave node)
 * @param data Received data
 * @param length Data length
 */
static void tdmaProcessBeacon(uint8 *data, uint8 length) {
  halUartWrite((uint8 *)"Processing beacon frame\r\n", 25);

  // 修复：避免sprintf导致栈溢出，使用简单的调试输出
  halUartWrite((uint8 *)"Beacon received, length OK\r\n", 29);

  if (length >= sizeof(beacon_frame_t)) {
    beacon_frame_t *beacon = (beacon_frame_t *)data;

    // 修复：简化调试输出，避免sprintf栈溢出
    halUartWrite((uint8 *)"Frame type and node ID OK\r\n", 28);

    if (beacon->header.frameType == FRAME_TYPE_BEACON &&
        beacon->header.nodeId == 0) { // Verify it's from the master
      halUartWrite((uint8 *)"Valid beacon frame received!\r\n", 31);

#ifdef SLAVE_NODE
      // First-time synchronization: Configure Timer3 if not already done
      if (!timerWorking) {
        halUartWrite((uint8 *)"First sync - configuring Timer3\r\n", 34);
        tdmaConfigureTimer3();
      }
#endif

      // Get master timestamp from beacon
      uint32 masterTimestamp = beacon->masterTimestamp;
      uint16 slotLength = beacon->slotLength;

      // 修复：避免sprintf %lu格式导致栈溢出
      halUartWrite((uint8 *)"Sync info received from master\r\n", 33);

      // Calculate network delay compensation (simplified)
      uint32 estimatedDelay = 50; // Assume 50 timer ticks delay (~5ms)

      // Synchronize Timer3 counter with master
      // Disable interrupts during sync to avoid race conditions
      EA = 0;
      timerTestCount = masterTimestamp + estimatedDelay;
      currentSlot = 0; // Reset to beacon slot
      currentSuperframe = beacon->superframeId;
      slotTimer = 0;
      slotFlags = 0; // Clear all slot flags
      EA = 1;        // Re-enable interrupts

      // 修复：确保同步状态正确设置且不会被意外重置
      networkSynced = TRUE;
      syncTimeout = 0; // 重置超时计数器

      halUartWrite((uint8 *)"Timer3 synchronized! Network ready\r\n", 37);
      halUartWrite((uint8 *)"Successfully synchronized to network.\r\n", 40);

      // 修复：简化同步状态输出，避免sprintf
      halUartWrite((uint8 *)"Sync Status: Both flags set\r\n", 30);

      // 修复：更新beacon接收时间，用于超时检查
      lastBeaconTime = timerTestCount;
    } else {
      halUartWrite((uint8 *)"Invalid frame type or node ID\r\n", 32);

      // 修复：简化错误帧调试输出
      halUartWrite((uint8 *)"Invalid frame details logged\r\n", 31);
    }
  } else {
    halUartWrite((uint8 *)"Beacon length too short\r\n", 26);
  }
}

/**
 * @brief Calculate checksum
 * @param data Data pointer
 * @param length Data length
 * @return Checksum
 */
static uint8 calculateChecksum(uint8 *data, uint8 length) {
  uint8 checksum = 0;
  uint8 i;

  for (i = 0; i < length; i++) {
    checksum ^= data[i];
  }

  return checksum;
}

/**
 * @brief Send sensor data (slave node)
 */
static void sendSensorData(void) {
  // Use static packet to avoid stack overflow
  static sensor_data_packet_t packet;
  uint8 sendResult;

  // Read sensor values
  temperatureValue = readTemperature();
  vibrationValue = readVibration();

  // Prepare data packet (compatible with master node)
  packet.header.frameType = FRAME_TYPE_DATA;
  packet.header.nodeId = myNodeId;
  packet.header.timeSlot = SLOT_SLAVE_1 + (myNodeId - 1);
  packet.header.sequenceNum = sequenceNumber++;
  packet.header.timestamp = currentSuperframe;
  packet.temperature = temperatureValue;
  packet.vibration = vibrationValue;
  packet.ledStatus = ledState;
  packet.batteryLevel = 85; // Simulated battery level

  // Calculate checksum
  packet.checksum = calculateChecksum((uint8 *)&packet, sizeof(packet) - 1);

  // Send to master node and check result
  sendResult = basicRfSendPacket(MASTER_NODE_ADDR, (uint8 *)&packet,
                                 sizeof(sensor_data_packet_t));

  // Update send statistics
  lastSendStatus = sendResult;
  if (sendResult == SUCCESS) {
    sendSuccessCount++;
    // Flash LED2 briefly to indicate successful send
    halLedSet(2);
    halMcuWaitMs(5);
    halLedClear(2);
  } else {
    sendFailedCount++;
    // Flash LED2 longer to indicate failed send
    halLedSet(2);
    halMcuWaitMs(20);
    halLedClear(2);
    halMcuWaitMs(20);
    halLedSet(2);
    halMcuWaitMs(20);
    halLedClear(2);
  }

  // Debug output sensor data with send status
  debugPrintSensorData(temperatureValue, vibrationValue);
}

/**
 * @brief Process control command (slave node)
 * @param data Received data
 * @param length Data length
 */
static void processControlCommand(uint8 *data, uint8 length) {
  if (length >= sizeof(led_control_packet_t)) {
    led_control_packet_t *cmd = (led_control_packet_t *)data;

    if (cmd->header.frameType == FRAME_TYPE_CONTROL &&
        cmd->targetNode == myNodeId) {

      switch (cmd->ledCommand) {
      case LED_CMD_OFF:
        halLedClear(1);
        ledState = 0;
        break;
      case LED_CMD_ON:
        halLedSet(1);
        ledState = 1;
        break;
      case LED_CMD_TOGGLE:
        halLedToggle(1);
        ledState ^= 1;
        break;
      }
    }
  }
}

#ifdef MASTER_NODE
/**
 * @brief Send beacon frame (master node)
 */
static void sendBeaconFrame(void) {
  beacon_frame_t beacon;

  // Prepare beacon frame with timestamp for TDMA synchronization
  beacon.header.frameType = FRAME_TYPE_BEACON;
  beacon.header.nodeId = 0; // Master node ID
  beacon.header.timeSlot = SLOT_MASTER_BEACON;
  beacon.header.sequenceNum = sequenceNumber++;
  beacon.header.timestamp = currentSuperframe;

  beacon.superframeId = currentSuperframe;

  // Critical: Include current Timer3 timestamp for slave synchronization
  beacon.masterTimestamp = timerTestCount; // Current Timer3 counter value
  beacon.slotLength = TDMA_SLOT_LENGTH;    // Slot length for slaves

  beacon.slotSchedule[0] = SLOT_MASTER_BEACON;
  beacon.slotSchedule[1] = SLOT_SLAVE_1;
  beacon.slotSchedule[2] = SLOT_SLAVE_2;
  beacon.slotSchedule[3] = SLOT_SLAVE_3;
  beacon.slotSchedule[4] = SLOT_MAINTENANCE;
  beacon.networkStatus = 0x01; // Network active

  // Broadcast beacon frame with timestamp
  basicRfSendPacket(0xFFFF, (uint8 *)&beacon, sizeof(beacon_frame_t));

  // 修复：简化beacon调试信息，避免sprintf %lu
  halUartWrite((uint8 *)"Master: Beacon sent with timestamp\r\n", 37);
}

/**
 * @brief Master node application
 */
static void masterNodeApp(void) {
  uint8 rxLength;
  int16 rssi;
  uint16 loopCounter = 0;

  // Indicate master node started - 5 quick flashes
  uint8 i;
  for (i = 0; i < 5; i++) {
    halLedSet(1);
    halMcuWaitMs(80);
    halLedClear(1);
    halMcuWaitMs(80);
  }

  // Master node main loop
  while (TRUE) {
    loopCounter++;

    // 修复：简化主节点调试输出，避免sprintf
    if (loopCounter % 10 == 1) {
      halUartWrite((uint8 *)"Master: Loop active, Timer=", 28);
      if (timerWorking) {
        halUartWrite((uint8 *)"OK", 2);
      } else {
        halUartWrite((uint8 *)"FAIL", 4);
      }
      halUartWrite((uint8 *)", Slot=OK\r\n", 11);
    }

    // Heartbeat LED every 10 loops (about 1 second)
    if (loopCounter % 10 == 0) {
      halLedToggle(4); // LED4 heartbeat
    }

    // Master node: Don't wait for slot flags, use fixed timing
    // Master is the time reference, not a follower
    halUartWrite((uint8 *)"Master: Beacon slot (fixed timing)\r\n", 37);

    // Send beacon frame immediately
    sendBeaconFrame();

    // Flash LED1 to indicate beacon sent
    halLedSet(1);
    halMcuWaitMs(20);
    halLedClear(1);

    // Listen for slave node data using fixed timing (master controls timing)
    for (uint8 slot = SLOT_SLAVE_1; slot <= SLOT_SLAVE_3; slot++) {
      halUartWrite(
          (uint8 *)"Master: Listening for slave data (fixed timing)\r\n", 51);

      // Master uses fixed timing, not slot flags
      halMcuWaitMs(TDMA_SLOT_LENGTH); // Wait one slot duration

      // Check for incoming data
      if (basicRfPacketIsReady()) {
        halUartWrite((uint8 *)"Master: Data received from slave!\r\n", 36);
        rxLength = basicRfReceive(rxBuffer, MAX_PAYLOAD_LENGTH, &rssi);
        if (rxLength > 0) {
          // Flash LED2 to indicate data received
          halLedSet(2);
          halMcuWaitMs(30);
          halLedClear(2);

          // Process received sensor data
          if (rxLength >= sizeof(sensor_data_packet_t)) {
            sensor_data_packet_t *sensorData = (sensor_data_packet_t *)rxBuffer;
            if (sensorData->header.frameType == FRAME_TYPE_DATA) {
              halUartWrite((uint8 *)"Master: Valid sensor data processed\r\n",
                           38);
              // Data received successfully
              // Could send LED control command back to slave
            }
          }
        }
      }
    }

    // Wait for next superframe
    halMcuWaitMs(TDMA_SUPERFRAME_LENGTH);
  }
}
#endif

#ifdef SLAVE_NODE
/**
 * @brief Slave node application
 */
static void slaveNodeApp(void) {
  uint8 rxLength;
  int16 rssi;
  uint8 mySlot = SLOT_SLAVE_1 + (myNodeId - 1); // Calculate my slot
  uint16 loopCounter = 0;                       // Loop counter for testing

  halUartWrite((uint8 *)"=== TDMA SLAVE NODE MODE ===\r\n", 31);

  // Indicate slave node started - 2 long flashes
  halLedSet(2); // LED2 on to indicate slave node mode
  halMcuWaitMs(300);
  halLedClear(2);
  halMcuWaitMs(200);
  halLedSet(2);
  halMcuWaitMs(300);
  halLedClear(2);

  // Slave node main loop
  halUartWrite((uint8 *)"Entering main loop...\r\n", 24);
  while (TRUE) {
    // Add watchdog reset to prevent system restart
    WDCTL = 0x00; // Keep watchdog disabled

    loopCounter++;

    // Heartbeat LED every 10 loops (about 1 second)
    if (loopCounter % 10 == 0) {
      halLedToggle(4); // LED4 heartbeat
    }

    // Safety check - if loop counter gets too high, something is wrong
    if (loopCounter > 1000) {
      loopCounter = 0;
    }

    // Show sync status every 20 loops (about 2 seconds)
    if (loopCounter % 20 == 0) {
      // Debug: Print current sync status (as suggested in 问题.md)
      halUartWrite((uint8 *)"Current Sync Status: ", 21);
      if (networkSynced) {
        halUartWrite((uint8 *)"Synchronized\r\n", 14);
      } else {
        halUartWrite((uint8 *)"Not Synchronized\r\n", 18);
      }
    }

    // Show send statistics every 50 loops (about 5 seconds)
    if (loopCounter % 50 == 0 && networkSynced) {
      debugPrintSendStats();
    }

    // PHASE 1: Wait for beacon (different behavior based on sync status)
    if (!networkSynced) {
      // Not synchronized - use software timing to listen for beacon
      halMcuWaitMs(20); // Wait 20ms (one slot duration)
    } else {
      // Synchronized - use hardware timer to wait for beacon slot
      tdmaWaitForSlot(SLOT_MASTER_BEACON);
    }

    // Receive beacon frame
    if (basicRfPacketIsReady()) {
      rxLength = basicRfReceive(rxBuffer, MAX_PAYLOAD_LENGTH, &rssi);
      if (rxLength > 0) {
        // Flash LED2 to indicate packet received
        halLedSet(2);
        halMcuWaitMs(10);
        halLedClear(2);

        tdmaProcessBeacon(rxBuffer, rxLength);
      }
    }

    // 修复：添加基于时间的同步超时检查（而不是循环计数）
    // 只有在长时间没有收到beacon时才失去同步
    uint16 currentTime = timerTestCount;

    if (networkSynced) {
      // 如果超过5秒（约250,000个timer ticks）没收到beacon，则失去同步
      if (currentTime - lastBeaconTime > 250000) {
        halUartWrite(
            (uint8 *)"Long-term sync timeout, losing synchronization\r\n", 49);
        networkSynced = FALSE;
        lastBeaconTime = currentTime;
      }
    }

    // If network is synchronized, send data in designated slot
    if (networkSynced) {
      // Flash LED1 to indicate synchronized
      halLedSet(1);

      // Wait for own slot
      tdmaWaitForSlot(mySlot);

      // Send sensor data
      sendSensorData();

      halLedClear(1);

      // Listen for control commands
      if (basicRfPacketIsReady()) {
        rxLength = basicRfReceive(rxBuffer, MAX_PAYLOAD_LENGTH, &rssi);
        if (rxLength > 0) {
          // Flash LED2 to indicate control command received
          halLedSet(2);
          halMcuWaitMs(50);
          halLedClear(2);

          processControlCommand(rxBuffer, rxLength);
        }
      }
    } else {
      // Not synchronized - double flash pattern
      halLedSet(1);
      halMcuWaitMs(100);
      halLedClear(1);
      halMcuWaitMs(100);
      halLedSet(1);
      halMcuWaitMs(100);
      halLedClear(1);
      halMcuWaitMs(400); // Longer pause between double flashes
    }

    // Wait for next superframe
    halMcuWaitMs(TDMA_SUPERFRAME_LENGTH);
  }
}
#endif

/**
 * @brief Timer3 interrupt service routine - TDMA slot timer (based on working
 * example)
 */
#pragma vector = T3_VECTOR
__interrupt void timer3Isr(void) {
  // Clear interrupt flag (based on working example)
  IRCON = 0x00;

  // Timer test counter (for debugging)
  timerTestCount++;

  // LED2 flash every 100 interrupts to show timer is working
  if (timerTestCount % 100 == 0) {
    halLedToggle(2);
    timerWorking = TRUE; // Mark timer as working
  }

  // Update slot counter every 1000 interrupts (approximately 20ms)
  // 修复：设置当前时隙的标志，而不是下一个时隙
  if (timerTestCount % 1000 == 0) {
    // 先更新时隙
    currentSlot = (currentSlot + 1) % TDMA_TOTAL_SLOTS;
    if (currentSlot == 0) {
      currentSuperframe++;
    }
    slotTimer++;

    // 修复：设置当前时隙的标志，供tdmaWaitForSlot使用
    switch (currentSlot) {
    case SLOT_MASTER_BEACON:
      slotFlags |= SLOT_FLAG_BEACON;
      break;
    case SLOT_SLAVE_1:
      slotFlags |= SLOT_FLAG_SLAVE_1;
      break;
    case SLOT_SLAVE_2:
      slotFlags |= SLOT_FLAG_SLAVE_2;
      break;
    case SLOT_SLAVE_3:
      slotFlags |= SLOT_FLAG_SLAVE_3;
      break;
    }

    // 修复：移除中断中的所有复杂操作
    // 在中断中不应该调用halUartWrite等复杂函数
  }
}

/**
 * @brief Main function
 */
void main(void) {
  // Disable watchdog timer first to prevent resets
  WDCTL = 0x00; // Disable watchdog timer

  // Initialize hardware
  halBoardInit();

  // Initialize UART immediately for debugging
  halUartInit(115200);

  // Send immediate startup message
  halUartWrite((uint8 *)"=== CC2530 UART Test ===\r\n", 27);
  halUartWrite((uint8 *)"System Starting...\r\n", 20);
  halUartWrite((uint8 *)"Send Status: OK=Success, FAIL=Failed\r\n", 38);
  halUartWrite((uint8 *)"LED2: Short flash=Success, Double flash=Failed\r\n",
               49);

  halRfInit();
  halRfSetTxPower(2); // Set transmit power to 4dBm

#ifdef SLAVE_NODE
  // Send debug message before sensor init
  halUartWrite((uint8 *)"Initializing sensors...\r\n", 25);

  // Initialize sensors and debug
  sensorsInit();
  debugInit();

  // Send multiple test messages
  halUartWrite((uint8 *)"=== TDMA Slave Node Started ===\r\n", 34);
  halUartWrite((uint8 *)"UART Debug Active\r\n", 19);
  halUartWrite((uint8 *)"Node ID: ", 9);

  // 修复：使用动态节点ID而不是硬编码
  if (NODE_ID == 1) {
    halUartWrite((uint8 *)"1\r\n", 3);
  } else if (NODE_ID == 2) {
    halUartWrite((uint8 *)"2\r\n", 3);
  } else if (NODE_ID == 3) {
    halUartWrite((uint8 *)"3\r\n", 3);
  } else {
    halUartWrite((uint8 *)"?\r\n", 3);
  }

  // 修复：移除不必要的测试代码，简化启动流程
  halUartWrite((uint8 *)"Vibration sensor initialized on P1.2\r\n", 40);
#endif

  // Configure BasicRF
  basicRfConfig.panId = PAN_ID;
  basicRfConfig.channel = RF_CHANNEL;
  basicRfConfig.ackRequest = FALSE;
  basicRfConfig.myAddr = myNodeAddr;

  // Initialize BasicRF for TDMA communication
  halUartWrite((uint8 *)"Initializing BasicRF...\r\n", 25);
  basicRfInit(&basicRfConfig);
  basicRfReceiveOn();
  halUartWrite((uint8 *)"BasicRF initialized\r\n", 21);

  // 修复：简化main函数逻辑，移除混乱的测试代码
  // 直接初始化TDMA系统
  halUartWrite((uint8 *)"Initializing TDMA system...\r\n", 30);

  // 初始化TDMA变量
  currentSlot = 0;
  currentSuperframe = 0;
  timerTestCount = 0;
  slotFlags = 0;
  syncTimeout = 0;
  lastBeaconTime = 0;

#ifdef MASTER_NODE
  // 主节点：立即同步并配置Timer3
  networkSynced = TRUE;
  timerWorking = TRUE;
  tdmaConfigureTimer3();
  halUartWrite((uint8 *)"Master node: Timer3 configured\r\n", 33);
#else
  // 从节点：等待beacon同步
  networkSynced = FALSE;
  timerWorking = FALSE;
  halUartWrite((uint8 *)"Slave node: Waiting for beacon\r\n", 33);
#endif

  // Initialize send statistics
  sendSuccessCount = 0;
  sendFailedCount = 0;
  lastSendStatus = SUCCESS;
  halUartWrite((uint8 *)"Send statistics initialized\r\n", 29);

  // 修复：简化LED测试
  halUartWrite((uint8 *)"System ready\r\n", 14);

#ifdef MASTER_NODE
  // 修复：简化主节点启动流程
  halUartWrite((uint8 *)"Starting Master Node App...\r\n", 29);

  // 简单的启动指示
  halLedSet(1);
  halMcuWaitMs(100);
  halLedClear(1);

  masterNodeApp();
#endif

#ifdef SLAVE_NODE
  // 修复：简化从节点启动流程
  halUartWrite((uint8 *)"Starting Slave Node App...\r\n", 28);

  // 简单的启动指示
  halLedSet(2);
  halMcuWaitMs(100);
  halLedClear(2);

  slaveNodeApp();
#endif
}
