#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速测试数据解析功能
"""

import re

def test_new_format():
    """测试新数据格式解析"""
    test_line = "[SERIAL] Node 2: Temp=25.0, Vib=0,Slot=1"
    
    print("测试数据:", test_line)
    
    # 新格式解析
    pattern = r'Node\s+(\d+):\s*Temp=([0-9.]+),\s*Vib=(\d+),\s*Slot=(\d+)'
    match = re.search(pattern, test_line, re.IGNORECASE)
    
    if match:
        node_id = int(match.group(1))
        temperature = float(match.group(2))
        vibration = int(match.group(3))
        slot_number = int(match.group(4))
        
        print("✓ 解析成功:")
        print(f"  节点ID: {node_id}")
        print(f"  温度: {temperature}°C")
        print(f"  振动: {vibration}")
        print(f"  时隙: {slot_number}")
        print(f"  格式: new_format (无Sync字段)")
        
        return True
    else:
        print("✗ 解析失败")
        return False

if __name__ == "__main__":
    print("=" * 50)
    print("快速数据解析测试")
    print("=" * 50)
    
    success = test_new_format()
    
    print("\n" + "=" * 50)
    if success:
        print("测试通过 - 新格式解析正常")
    else:
        print("测试失败 - 需要检查解析逻辑")
    print("=" * 50)
