#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
TDMA传感器网络简化监控系统
只使用基础库，避免复杂依赖
"""

import tkinter as tk
from tkinter import ttk, messagebox, scrolledtext
import threading
import time
import queue
import re
from datetime import datetime
import os

try:
    import serial
    import serial.tools.list_ports
    SERIAL_AVAILABLE = True
except ImportError:
    SERIAL_AVAILABLE = False
    print("警告: pyserial未安装，将使用模拟数据模式")

class SimpleTDMAMonitor:
    def __init__(self, root):
        self.root = root
        self.root.title("TDMA传感器网络监控系统 (简化版)")
        self.root.geometry("1000x700")
        
        # 数据存储
        self.data_queue = queue.Queue()
        self.sensor_data_list = []
        
        # 串口相关
        self.serial_port = None
        self.is_connected = False
        self.read_thread = None
        
        # 模拟数据（用于测试）
        self.demo_mode = not SERIAL_AVAILABLE
        self.demo_counter = 0
        
        # 创建界面
        self.create_widgets()
        
        # 启动数据处理
        self.start_data_processing()
        
        # 如果是演示模式，启动模拟数据
        if self.demo_mode:
            self.start_demo_data()
        
    def create_widgets(self):
        """创建主界面"""
        # 主框架
        main_frame = ttk.Frame(self.root)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # 先初始化日志组件（避免其他组件调用add_log时出错）
        self.log_text = None

        # 顶部控制面板
        self.create_control_panel(main_frame)

        # 中间数据显示区域
        self.create_data_display(main_frame)

        # 底部日志区域
        self.create_log_area(main_frame)
        
    def create_control_panel(self, parent):
        """创建控制面板"""
        control_frame = ttk.LabelFrame(parent, text="连接控制", padding=10)
        control_frame.pack(fill=tk.X, pady=(0, 10))
        
        if SERIAL_AVAILABLE:
            # 串口选择
            ttk.Label(control_frame, text="串口:").grid(row=0, column=0, padx=5)
            self.port_var = tk.StringVar()
            self.port_combo = ttk.Combobox(control_frame, textvariable=self.port_var, width=15)
            self.port_combo.grid(row=0, column=1, padx=5)
            
            # 波特率选择
            ttk.Label(control_frame, text="波特率:").grid(row=0, column=2, padx=5)
            self.baud_var = tk.StringVar(value="115200")
            baud_combo = ttk.Combobox(control_frame, textvariable=self.baud_var, 
                                     values=["9600", "115200", "230400"], width=10)
            baud_combo.grid(row=0, column=3, padx=5)
            
            # 控制按钮
            self.refresh_btn = ttk.Button(control_frame, text="刷新串口", command=self.refresh_ports)
            self.refresh_btn.grid(row=0, column=4, padx=5)
            
            self.connect_btn = ttk.Button(control_frame, text="连接", command=self.toggle_connection)
            self.connect_btn.grid(row=0, column=5, padx=5)
            
            # 状态显示
            self.status_var = tk.StringVar(value="未连接")
            status_label = ttk.Label(control_frame, textvariable=self.status_var, foreground="red")
            status_label.grid(row=0, column=6, padx=20)
            
            # 初始化串口列表
            self.refresh_ports()
        else:
            # 演示模式提示
            demo_label = ttk.Label(control_frame, text="演示模式 - 显示模拟数据", foreground="blue")
            demo_label.grid(row=0, column=0, padx=5)
            
            self.demo_btn = ttk.Button(control_frame, text="开始/停止演示", command=self.toggle_demo)
            self.demo_btn.grid(row=0, column=1, padx=5)
            
            self.status_var = tk.StringVar(value="演示模式")
            status_label = ttk.Label(control_frame, textvariable=self.status_var, foreground="blue")
            status_label.grid(row=0, column=2, padx=20)
        
    def create_data_display(self, parent):
        """创建数据显示区域"""
        data_frame = ttk.LabelFrame(parent, text="实时数据", padding=10)
        data_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 10))
        
        # 传感器数据表格
        columns = ('时间', '节点ID', '温度(°C)', '振动', '时隙')
        self.sensor_tree = ttk.Treeview(data_frame, columns=columns, show='headings', height=15)

        for col in columns:
            self.sensor_tree.heading(col, text=col)
            if col == '时间':
                self.sensor_tree.column(col, width=120)
            else:
                self.sensor_tree.column(col, width=100)
                
        # 添加滚动条
        tree_scroll = ttk.Scrollbar(data_frame, orient=tk.VERTICAL, command=self.sensor_tree.yview)
        self.sensor_tree.configure(yscrollcommand=tree_scroll.set)
        
        self.sensor_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        tree_scroll.pack(side=tk.RIGHT, fill=tk.Y)
        
        # 右键菜单
        self.create_context_menu()
        
    def create_log_area(self, parent):
        """创建日志区域"""
        log_frame = ttk.LabelFrame(parent, text="系统日志", padding=10)
        log_frame.pack(fill=tk.X, pady=(0, 10))  # 先创建但不展开

        # 创建日志控制按钮框架
        log_control_frame = ttk.Frame(log_frame)
        log_control_frame.pack(fill=tk.X, pady=(0, 5))

        # 添加清除日志按钮
        self.clear_log_btn = ttk.Button(log_control_frame, text="清除日志", command=self.clear_log)
        self.clear_log_btn.pack(side=tk.LEFT, padx=(0, 5))

        # 添加保存日志按钮
        self.save_log_btn = ttk.Button(log_control_frame, text="保存日志", command=self.save_log)
        self.save_log_btn.pack(side=tk.LEFT, padx=(0, 5))

        # 添加日志级别过滤
        ttk.Label(log_control_frame, text="过滤:").pack(side=tk.LEFT, padx=(20, 5))
        self.log_filter_var = tk.StringVar(value="全部")
        log_filter_combo = ttk.Combobox(log_control_frame, textvariable=self.log_filter_var,
                                       values=["全部", "SERIAL", "DEBUG", "ERROR"], width=8)
        log_filter_combo.pack(side=tk.LEFT)
        log_filter_combo.bind('<<ComboboxSelected>>', self.on_log_filter_changed)

        self.log_text = scrolledtext.ScrolledText(log_frame, height=6, wrap=tk.WORD)
        self.log_text.pack(fill=tk.BOTH, expand=True)

        # 绑定日志区域右键菜单和快捷键
        self.log_text.bind("<Button-3>", self.show_log_context_menu)

        # 绑定快捷键
        self.root.bind("<Control-l>", lambda e: self.clear_log())  # Ctrl+L 清除日志
        self.root.bind("<Control-s>", lambda e: self.save_log())   # Ctrl+S 保存日志
        self.root.bind("<Control-d>", lambda e: self.clear_data()) # Ctrl+D 清除数据

        # 存储所有日志消息（用于过滤）
        self.all_log_messages = []

        # 添加初始日志
        self.add_log("系统启动完成")
        self.add_log("快捷键: Ctrl+L=清除日志, Ctrl+S=保存日志, Ctrl+D=清除数据")
        if self.demo_mode:
            self.add_log("演示模式：将显示模拟的TDMA传感器数据")
        
    def create_context_menu(self):
        """创建右键菜单"""
        # 数据表格右键菜单
        self.context_menu = tk.Menu(self.root, tearoff=0)
        self.context_menu.add_command(label="清除数据", command=self.clear_data)
        self.context_menu.add_command(label="导出数据", command=self.export_data)

        # 日志区域右键菜单
        self.log_context_menu = tk.Menu(self.root, tearoff=0)
        self.log_context_menu.add_command(label="清除日志", command=self.clear_log)
        self.log_context_menu.add_command(label="保存日志", command=self.save_log)
        self.log_context_menu.add_separator()
        self.log_context_menu.add_command(label="复制选中", command=self.copy_selected_log)

        self.sensor_tree.bind("<Button-3>", self.show_context_menu)
        
    def show_context_menu(self, event):
        """显示数据表格右键菜单"""
        try:
            self.context_menu.tk_popup(event.x_root, event.y_root)
        finally:
            self.context_menu.grab_release()

    def show_log_context_menu(self, event):
        """显示日志区域右键菜单"""
        try:
            self.log_context_menu.tk_popup(event.x_root, event.y_root)
        finally:
            self.log_context_menu.grab_release()

    def copy_selected_log(self):
        """复制选中的日志内容"""
        try:
            selected_text = self.log_text.selection_get()
            self.root.clipboard_clear()
            self.root.clipboard_append(selected_text)
            self.add_log("已复制选中的日志内容到剪贴板")
        except tk.TclError:
            # 没有选中文本
            messagebox.showwarning("警告", "请先选中要复制的日志内容")
            
    def clear_data(self):
        """清除数据"""
        for item in self.sensor_tree.get_children():
            self.sensor_tree.delete(item)
        self.sensor_data_list.clear()
        self.add_log("数据已清除")

    def clear_log(self):
        """清除日志"""
        self.log_text.delete("1.0", tk.END)
        self.all_log_messages.clear()
        self.add_log("日志已清除")

    def save_log(self):
        """保存日志到文件"""
        try:
            filename = f"system_log_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt"
            with open(filename, 'w', encoding='utf-8') as f:
                log_content = self.log_text.get("1.0", tk.END)
                f.write(log_content)

            self.add_log(f"日志已保存到: {filename}")
            messagebox.showinfo("成功", f"日志已保存到: {filename}")
        except Exception as e:
            self.add_log(f"保存日志失败: {e}")
            messagebox.showerror("错误", f"保存日志失败: {e}")

    def on_log_filter_changed(self, event=None):
        """日志过滤器改变事件"""
        filter_type = self.log_filter_var.get()

        # 清除当前显示的日志
        self.log_text.delete("1.0", tk.END)

        # 根据过滤条件重新显示日志
        for log_msg in self.all_log_messages:
            if filter_type == "全部":
                self.log_text.insert(tk.END, log_msg)
            elif filter_type in log_msg:
                self.log_text.insert(tk.END, log_msg)

        # 滚动到最新位置
        self.log_text.see(tk.END)
        
    def export_data(self):
        """导出数据到CSV文件"""
        if not self.sensor_data_list:
            messagebox.showwarning("警告", "没有数据可导出")
            return
            
        try:
            filename = f"sensor_data_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv"
            with open(filename, 'w', encoding='utf-8') as f:
                f.write("时间,节点ID,温度,振动,时隙\n")
                for data in self.sensor_data_list:
                    f.write(f"{data['time']},{data['node_id']},{data['temperature']},{data['vibration']},{data['slot']}\n")
            
            self.add_log(f"数据已导出到: {filename}")
            messagebox.showinfo("成功", f"数据已导出到: {filename}")
        except Exception as e:
            self.add_log(f"导出失败: {e}")
            messagebox.showerror("错误", f"导出失败: {e}")
    
    def refresh_ports(self):
        """刷新可用串口列表"""
        if not SERIAL_AVAILABLE:
            return
            
        ports = [port.device for port in serial.tools.list_ports.comports()]
        self.port_combo['values'] = ports
        if ports:
            self.port_combo.set(ports[0])
            self.add_log(f"发现串口: {', '.join(ports)}")
        else:
            self.add_log("未发现可用串口")
            
    def toggle_connection(self):
        """切换串口连接状态"""
        if not self.is_connected:
            self.connect_serial()
        else:
            self.disconnect_serial()
            
    def connect_serial(self):
        """连接串口"""
        if not SERIAL_AVAILABLE:
            return
            
        try:
            port = self.port_var.get()
            baud = int(self.baud_var.get())
            
            if not port:
                messagebox.showerror("错误", "请选择串口")
                return
                
            self.serial_port = serial.Serial(port, baud, timeout=1)
            self.is_connected = True
            
            # 启动读取线程
            self.read_thread = threading.Thread(target=self.read_serial_data, daemon=True)
            self.read_thread.start()
            
            # 更新界面
            self.status_var.set(f"已连接 - {port}")
            self.connect_btn.config(text="断开")
            self.add_log(f"已连接到串口: {port}, 波特率: {baud}")
            
        except Exception as e:
            self.add_log(f"连接失败: {e}")
            messagebox.showerror("连接错误", f"无法连接串口: {str(e)}")
            
    def disconnect_serial(self):
        """断开串口连接"""
        self.is_connected = False
        if self.serial_port:
            self.serial_port.close()
            self.serial_port = None
            
        self.status_var.set("未连接")
        self.connect_btn.config(text="连接")
        self.add_log("串口连接已断开")
        
    def read_serial_data(self):
        """读取串口数据线程"""
        while self.is_connected and self.serial_port:
            try:
                if self.serial_port.in_waiting:
                    line = self.serial_port.readline().decode('utf-8', errors='ignore').strip()
                    if line:
                        self.data_queue.put(('serial', line))
            except Exception as e:
                self.add_log(f"串口读取错误: {e}")
                break
                
    def toggle_demo(self):
        """切换演示模式"""
        self.demo_mode = not self.demo_mode
        if self.demo_mode:
            self.start_demo_data()
            self.status_var.set("演示模式 - 运行中")
            self.add_log("演示模式已启动")
        else:
            self.status_var.set("演示模式 - 已停止")
            self.add_log("演示模式已停止")
            
    def start_demo_data(self):
        """启动演示数据生成"""
        if self.demo_mode:
            self.generate_demo_data()
            self.root.after(2000, self.start_demo_data)  # 每2秒生成一次数据
            
    def generate_demo_data(self):
        """生成演示数据"""
        if not self.demo_mode:
            return
            
        self.demo_counter += 1
        
        # 模拟不同节点的数据
        for node_id in [1, 2, 3]:
            temp = 25.0 + (self.demo_counter % 10) + node_id * 0.5
            vib = self.demo_counter % 2
            sync = 1 if self.demo_counter > 3 else 0
            slot = node_id
            
            demo_line = f"Node {node_id}: Temp={temp:.1f}, Vib={vib}, Sync={sync}, Slot={slot}"
            self.data_queue.put(('demo', demo_line))
            
    def start_data_processing(self):
        """启动数据处理"""
        self.process_data()
        
    def process_data(self):
        """处理接收到的数据"""
        try:
            while not self.data_queue.empty():
                data_type, line = self.data_queue.get_nowait()
                self.parse_and_display_data(line, data_type)
        except queue.Empty:
            pass
        finally:
            # 每100ms处理一次数据
            self.root.after(100, self.process_data)
            
    def parse_and_display_data(self, line, data_type=""):
        """解析并显示数据"""
        # 解析传感器数据 - 支持多种格式

        # 格式1: 标准格式 "Node 1: Temp=25.6, Vib=0, Sync=1, Slot=1"
        sensor_pattern1 = r'Node\s+(\d+).*?Temp=([0-9.]+).*?Vib=(\d+).*?Sync=(\d+).*?Slot=(\d+)'
        match1 = re.search(sensor_pattern1, line, re.IGNORECASE)

        # 格式2: 标准格式 "Node 1: Temp=25.0, Vib=0, Sync=1, Slot=1" (新的TDMA输出格式)
        sensor_pattern2 = r'Node\s+(\d+):\s*Temp=([0-9.]+),\s*Vib=(\d+),\s*Sync=(\d+),\s*Slot=(\d+)'
        match2 = re.search(sensor_pattern2, line, re.IGNORECASE)

        # 格式3: 新格式 "Node 2: Temp=25.0, Vib=0,Slot=1" (缺少Sync字段)
        sensor_pattern3 = r'Node\s+(\d+):\s*Temp=([0-9.]+),\s*Vib=(\d+),\s*Slot=(\d+)'
        match3 = re.search(sensor_pattern3, line, re.IGNORECASE)

        # 格式4: 旧版MASTER接收格式 "MASTER: Received from Node 3 - Temp=298, Vib=0" (兼容性)
        sensor_pattern4 = r'MASTER:\s*Received from Node\s+(\d+).*?Temp=([0-9.]+).*?Vib=(\d+)'
        match4 = re.search(sensor_pattern4, line, re.IGNORECASE)

        match = None
        node_id = None
        temperature = None
        vibration = None
        sync_status = 1  # 默认同步状态
        slot_number = 0  # 默认时隙

        if match1:
            match = match1
            node_id = int(match.group(1))
            temperature = float(match.group(2))
            vibration = int(match.group(3))
            sync_status = int(match.group(4))
            slot_number = int(match.group(5))
        elif match2:
            match = match2
            node_id = int(match.group(1))
            temperature = float(match.group(2))  # 已经是正确的小数格式
            vibration = int(match.group(3))
            sync_status = int(match.group(4))  # 从数据中获取同步状态
            slot_number = int(match.group(5))  # 从数据中获取时隙号
        elif match3:
            match = match3
            node_id = int(match.group(1))
            temperature = float(match.group(2))  # 新格式已经是正确的小数格式
            vibration = int(match.group(3))
            sync_status = 1  # 新格式默认认为已同步（能收到数据说明已同步）
            slot_number = int(match.group(4))  # 从数据中获取时隙号
        elif match4:
            match = match4
            node_id = int(match.group(1))
            temperature = float(match.group(2)) / 10.0  # 298 -> 29.8°C (旧格式转换)
            vibration = int(match.group(3))
            sync_status = 1  # MASTER收到数据说明已同步
            slot_number = node_id  # 使用节点ID作为时隙号

        if match and node_id is not None:
            timestamp = datetime.now()

            # 添加调试信息
            debug_msg = f"解析成功: 节点{node_id}, 温度{temperature}°C, 振动{vibration}, 同步{sync_status}, 时隙{slot_number}"
            self.add_log(f"[DEBUG] {debug_msg}")

            # 更新表格显示
            self.update_sensor_table(timestamp, node_id, temperature, vibration, sync_status, slot_number)

            # 保存数据（移除sync字段）
            self.sensor_data_list.append({
                'time': timestamp.strftime('%H:%M:%S'),
                'node_id': node_id,
                'temperature': temperature,
                'vibration': vibration,
                'slot': slot_number
            })

            # 限制数据量
            if len(self.sensor_data_list) > 1000:
                self.sensor_data_list = self.sensor_data_list[-500:]
        else:
            # 添加解析失败的调试信息
            self.add_log(f"[DEBUG] 数据解析失败: {line}")

        # 添加到日志
        log_prefix = f"[{data_type.upper()}]" if data_type else ""
        self.add_log(f"{log_prefix} {line}")
        
    def update_sensor_table(self, timestamp, node_id, temp, vib, sync, slot):
        """更新传感器数据表格"""
        # 清除旧数据（保留最近50条）
        children = self.sensor_tree.get_children()
        if len(children) >= 50:
            for i in range(10):  # 删除最旧的10条
                if children:
                    self.sensor_tree.delete(children[i])

        # 添加新数据（移除同步状态列）
        time_str = timestamp.strftime("%H:%M:%S")

        item = self.sensor_tree.insert('', 'end', values=(
            time_str, f"节点{node_id}", f"{temp:.1f}", vib, slot
        ))

        # 滚动到最新数据
        self.sensor_tree.see(item)
        
    def add_log(self, message):
        """添加日志信息"""
        # 如果log_text还没创建，先打印到控制台
        if self.log_text is None:
            timestamp = datetime.now().strftime("%H:%M:%S")
            print(f"[{timestamp}] {message}")
            return

        timestamp = datetime.now().strftime("%H:%M:%S")
        log_message = f"[{timestamp}] {message}\n"

        # 存储到所有日志消息列表（用于过滤）
        if hasattr(self, 'all_log_messages'):
            self.all_log_messages.append(log_message)

            # 限制存储的日志数量
            if len(self.all_log_messages) > 500:
                self.all_log_messages = self.all_log_messages[-250:]

        # 检查当前过滤设置
        should_display = True
        if hasattr(self, 'log_filter_var'):
            filter_type = self.log_filter_var.get()
            if filter_type != "全部" and filter_type not in message:
                should_display = False

        # 只有符合过滤条件的消息才显示
        if should_display:
            self.log_text.insert(tk.END, log_message)
            self.log_text.see(tk.END)

            # 限制显示的日志行数
            lines = self.log_text.get("1.0", tk.END).split('\n')
            if len(lines) > 200:
                # 删除前100行
                self.log_text.delete("1.0", "100.0")

def main():
    root = tk.Tk()
    app = SimpleTDMAMonitor(root)
    
    # 设置窗口关闭事件
    def on_closing():
        if app.is_connected:
            app.disconnect_serial()
        root.destroy()
    
    root.protocol("WM_DELETE_WINDOW", on_closing)
    root.mainloop()

if __name__ == "__main__":
    main()
