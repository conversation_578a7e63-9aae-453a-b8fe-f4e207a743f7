<?xml version="1.0" encoding="iso-8859-1"?>

<project>
  <fileVersion>2</fileVersion>
  <fileChecksum>3058640055</fileChecksum>
  <configuration>
    <name>srf05_cc2530</name>
    <outputs>
      <file>$PROJ_DIR$\..\..\..\source\Components\targets\interface\hal_digio.h</file>
      <file>$PROJ_DIR$\..\..\..\source\Components\targets\interface\hal_assert.h</file>
      <file>$PROJ_DIR$\..\..\..\source\Components\targets\interface\hal_button.h</file>
      <file>$PROJ_DIR$\..\..\..\source\Components\common\hal_types.h</file>
      <file>$PROJ_DIR$\..\..\..\source\apps\spectrum_analyzer\spectrum_analyzer.c</file>
      <file>$PROJ_DIR$\..\..\..\source\Components\common\cc8051\hal_cc8051.h</file>
      <file>$PROJ_DIR$\..\..\..\source\Components\common\hal_defs.h</file>
      <file>$PROJ_DIR$\..\..\..\source\Components\common\hal_int.c</file>
      <file>$PROJ_DIR$\..\..\..\source\Components\targets\common\hal_lcd_srf05.c</file>
      <file>$PROJ_DIR$\..\..\..\source\Components\targets\interface\hal_lcd.h</file>
      <file>$PROJ_DIR$\..\..\..\source\Components\targets\interface\hal_rf.h</file>
      <file>$PROJ_DIR$\..\..\..\source\Components\targets\srf05_soc\hal_assert.c</file>
      <file>$PROJ_DIR$\..\..\..\source\Components\utils\util_lcd.h</file>
      <file>$PROJ_DIR$\temp\spectrum_analyzer\hal_button.pbi</file>
      <file>$PROJ_DIR$\..\..\..\source\Components\targets\srf05_soc\hal_button.c</file>
      <file>$PROJ_DIR$\..\..\..\source\Components\radios\cc2530\adc.h</file>
      <file>$PROJ_DIR$\..\..\..\source\Components\radios\cc2530\clock.h</file>
      <file>$PROJ_DIR$\..\..\..\source\Components\radios\cc2530\hal_rf.c</file>
      <file>$PROJ_DIR$\..\..\..\source\Components\targets\interface\hal_rf_util.h</file>
      <file>$PROJ_DIR$\..\..\..\source\Components\targets\interface\hal_int.h</file>
      <file>$PROJ_DIR$\..\..\..\source\Components\targets\srf05_soc\hal_led.c</file>
      <file>$PROJ_DIR$\temp\spectrum_analyzer\util_lcd.pbi</file>
      <file>$PROJ_DIR$\..\..\..\source\Components\utils\util.c</file>
      <file>$PROJ_DIR$\..\..\..\source\Components\targets\interface\hal_joystick.h</file>
      <file>$PROJ_DIR$\..\..\..\source\Components\radios\cc2530\hal_mcu.c</file>
      <file>$PROJ_DIR$\..\..\..\source\Components\targets\interface\hal_mcu.h</file>
      <file>$PROJ_DIR$\..\..\..\source\Components\targets\srf05_soc\hal_board.h</file>
      <file>$PROJ_DIR$\..\..\..\source\Components\targets\srf05_soc\hal_board.c</file>
      <file>$PROJ_DIR$\..\..\..\source\Components\targets\srf05_soc\hal_joystick.c</file>
      <file>$PROJ_DIR$\..\..\..\source\Components\radios\cc2530\hal_rf_util.c</file>
      <file>$PROJ_DIR$\..\..\..\source\Components\utils\util.h</file>
      <file>$PROJ_DIR$\..\..\..\source\Components\utils\util_lcd.c</file>
      <file>$PROJ_DIR$\..\..\..\source\Components\targets\interface\hal_led.h</file>
      <file>$PROJ_DIR$\..\..\..\source\Components\targets\srf05_soc\hal_digio.c</file>
      <file>$PROJ_DIR$\..\..\..\source\Components\radios\cc2530\adc.c</file>
      <file>$PROJ_DIR$\..\..\..\source\Components\radios\cc2530\clock.c</file>
      <file>$PROJ_DIR$\temp\spectrum_analyzer\hal_rf_util.r51</file>
      <file>$PROJ_DIR$\temp\spectrum_analyzer\hal_lcd_srf05.pbi</file>
      <file>$PROJ_DIR$\temp\spectrum_analyzer\hal_int.pbi</file>
      <file>$PROJ_DIR$\temp\spectrum_analyzer\clock.r51</file>
      <file>$PROJ_DIR$\temp\spectrum_analyzer\hal_assert.pbi</file>
      <file>$PROJ_DIR$\temp\spectrum_analyzer\hal_led.pbi</file>
      <file>$PROJ_DIR$\temp\spectrum_analyzer\hal_rf.pbi</file>
      <file>$PROJ_DIR$\temp\spectrum_analyzer\hal_joystick.pbi</file>
      <file>$TOOLKIT_DIR$\inc\clib\string.h</file>
      <file>$PROJ_DIR$\temp\spectrum_analyzer\hal_rf_util.pbi</file>
      <file>$PROJ_DIR$\temp\spectrum_analyzer\hal_assert.r51</file>
      <file>$PROJ_DIR$\temp\spectrum_analyzer\hal_int.r51</file>
      <file>$PROJ_DIR$\temp\spectrum_analyzer\util.pbi</file>
      <file>$PROJ_DIR$\temp\spectrum_analyzer\hal_button.r51</file>
      <file>$PROJ_DIR$\temp\spectrum_analyzer\adc.r51</file>
      <file>$PROJ_DIR$\temp\spectrum_analyzer\adc.pbi</file>
      <file>$PROJ_DIR$\temp\spectrum_analyzer\spectrum_analyzer.pbi</file>
      <file>$PROJ_DIR$\temp\spectrum_analyzer\hal_joystick.r51</file>
      <file>$PROJ_DIR$\temp\spectrum_analyzer\util_lcd.r51</file>
      <file>$PROJ_DIR$\temp\spectrum_analyzer\hal_led.r51</file>
      <file>$PROJ_DIR$\temp\spectrum_analyzer\hal_board.r51</file>
      <file>$PROJ_DIR$\temp\spectrum_analyzer\spectrum_analyzer.r51</file>
      <file>$PROJ_DIR$\temp\spectrum_analyzer\hal_mcu.r51</file>
      <file>$PROJ_DIR$\temp\spectrum_analyzer\spectrum_analyzer.pbd</file>
      <file>$PROJ_DIR$\temp\spectrum_analyzer\clock.pbi</file>
      <file>$PROJ_DIR$\temp\spectrum_analyzer\hal_digio.r51</file>
      <file>$PROJ_DIR$\temp\spectrum_analyzer\util.r51</file>
      <file>$TOOLKIT_DIR$\inc\clib\sysmac.h</file>
      <file>$TOOLKIT_DIR$\inc\ioCC2530.h</file>
      <file>$PROJ_DIR$\temp\spectrum_analyzer\hal_board.pbi</file>
      <file>$PROJ_DIR$\temp\spectrum_analyzer\hal_lcd_srf05.r51</file>
      <file>$PROJ_DIR$\temp\spectrum_analyzer\hal_rf.r51</file>
      <file>$PROJ_DIR$\temp\spectrum_analyzer\hal_mcu.pbi</file>
      <file>$PROJ_DIR$\..\..\..\source\Components\common\hal_uart.h</file>
      <file>$PROJ_DIR$\temp\spectrum_analyzer\hal_digio.pbi</file>
      <file>$PROJ_DIR$\spectrum_analyzer.d51</file>
    </outputs>
    <file>
      <name>[ROOT_NODE]</name>
      <outputs>
        <tool>
          <name>XLINK</name>
          <file> 71</file>
        </tool>
      </outputs>
    </file>
    <file>
      <name>$PROJ_DIR$\..\..\..\source\apps\spectrum_analyzer\spectrum_analyzer.c</name>
      <outputs>
        <tool>
          <name>BICOMP</name>
          <file> 52</file>
        </tool>
        <tool>
          <name>ICC8051</name>
          <file> 57</file>
        </tool>
      </outputs>
      <inputs>
        <tool>
          <name>BICOMP</name>
          <file> 9 3 32 23 26 64 5 6 25 19 1 10 30 18 12</file>
        </tool>
      </inputs>
    </file>
    <file>
      <name>$PROJ_DIR$\..\..\..\source\Components\common\hal_int.c</name>
      <outputs>
        <tool>
          <name>BICOMP</name>
          <file> 38</file>
        </tool>
        <tool>
          <name>ICC8051</name>
          <file> 47 50 39 58 67 36 66 46 56 49 61 53 55 62 54 57</file>
        </tool>
      </outputs>
      <inputs>
        <tool>
          <name>BICOMP</name>
          <file> 3 6 19 26 64 5 25</file>
        </tool>
        <tool>
          <name>ICC8051</name>
          <file> 3 6 19 26 64 5 25 34 15 35 16 24 1 17 10 30 29 18 8 9 44 63 11 32 27 0 23 14 2 33 28 20 22 31 12 4</file>
        </tool>
      </inputs>
    </file>
    <file>
      <name>$PROJ_DIR$\..\..\..\source\Components\targets\common\hal_lcd_srf05.c</name>
      <outputs>
        <tool>
          <name>BICOMP</name>
          <file> 37</file>
        </tool>
        <tool>
          <name>ICC8051</name>
          <file> 66</file>
        </tool>
      </outputs>
      <inputs>
        <tool>
          <name>BICOMP</name>
          <file> 3 9 25 26 64 5 6 44 63 30</file>
        </tool>
      </inputs>
    </file>
    <file>
      <name>$PROJ_DIR$\..\..\..\source\Components\targets\srf05_soc\hal_assert.c</name>
      <outputs>
        <tool>
          <name>BICOMP</name>
          <file> 40</file>
        </tool>
        <tool>
          <name>ICC8051</name>
          <file> 46</file>
        </tool>
      </outputs>
      <inputs>
        <tool>
          <name>BICOMP</name>
          <file> 1 6 32 3 25</file>
        </tool>
      </inputs>
    </file>
    <file>
      <name>$PROJ_DIR$\..\..\..\source\Components\targets\srf05_soc\hal_button.c</name>
      <outputs>
        <tool>
          <name>BICOMP</name>
          <file> 13</file>
        </tool>
        <tool>
          <name>ICC8051</name>
          <file> 49</file>
        </tool>
      </outputs>
      <inputs>
        <tool>
          <name>BICOMP</name>
          <file> 3 26 64 5 6 25 2</file>
        </tool>
      </inputs>
    </file>
    <file>
      <name>$PROJ_DIR$\..\..\..\source\Components\radios\cc2530\hal_rf.c</name>
      <outputs>
        <tool>
          <name>BICOMP</name>
          <file> 42</file>
        </tool>
        <tool>
          <name>ICC8051</name>
          <file> 67</file>
        </tool>
      </outputs>
      <inputs>
        <tool>
          <name>BICOMP</name>
          <file> 26 64 5 3 6 25 19 1 10 30</file>
        </tool>
      </inputs>
    </file>
    <file>
      <name>$PROJ_DIR$\..\..\..\source\Components\targets\srf05_soc\hal_led.c</name>
      <outputs>
        <tool>
          <name>BICOMP</name>
          <file> 41</file>
        </tool>
        <tool>
          <name>ICC8051</name>
          <file> 55</file>
        </tool>
      </outputs>
      <inputs>
        <tool>
          <name>BICOMP</name>
          <file> 3 32 26 64 5 6 25</file>
        </tool>
      </inputs>
    </file>
    <file>
      <name>$PROJ_DIR$\..\..\..\source\Components\utils\util.c</name>
      <outputs>
        <tool>
          <name>BICOMP</name>
          <file> 48</file>
        </tool>
        <tool>
          <name>ICC8051</name>
          <file> 62</file>
        </tool>
      </outputs>
      <inputs>
        <tool>
          <name>BICOMP</name>
          <file> 30 3 6 10</file>
        </tool>
      </inputs>
    </file>
    <file>
      <name>$PROJ_DIR$\..\..\..\source\Components\radios\cc2530\hal_mcu.c</name>
      <outputs>
        <tool>
          <name>BICOMP</name>
          <file> 68</file>
        </tool>
        <tool>
          <name>ICC8051</name>
          <file> 58</file>
        </tool>
      </outputs>
      <inputs>
        <tool>
          <name>BICOMP</name>
          <file> 3 25 16 6 64 1</file>
        </tool>
      </inputs>
    </file>
    <file>
      <name>$PROJ_DIR$\..\..\..\source\Components\targets\srf05_soc\hal_board.c</name>
      <outputs>
        <tool>
          <name>BICOMP</name>
          <file> 65</file>
        </tool>
        <tool>
          <name>ICC8051</name>
          <file> 56</file>
        </tool>
      </outputs>
      <inputs>
        <tool>
          <name>BICOMP</name>
          <file> 3 6 0 19 25 26 64 5 9 23 16 69</file>
        </tool>
      </inputs>
    </file>
    <file>
      <name>$PROJ_DIR$\..\..\..\source\Components\targets\srf05_soc\hal_joystick.c</name>
      <outputs>
        <tool>
          <name>BICOMP</name>
          <file> 43</file>
        </tool>
        <tool>
          <name>ICC8051</name>
          <file> 53</file>
        </tool>
      </outputs>
      <inputs>
        <tool>
          <name>BICOMP</name>
          <file> 23 3 26 64 5 6 25 15 0 19</file>
        </tool>
      </inputs>
    </file>
    <file>
      <name>$PROJ_DIR$\..\..\..\source\Components\radios\cc2530\hal_rf_util.c</name>
      <outputs>
        <tool>
          <name>BICOMP</name>
          <file> 45</file>
        </tool>
        <tool>
          <name>ICC8051</name>
          <file> 36</file>
        </tool>
      </outputs>
      <inputs>
        <tool>
          <name>BICOMP</name>
          <file> 26 64 5 3 6 25 1 18 10</file>
        </tool>
      </inputs>
    </file>
    <file>
      <name>$PROJ_DIR$\..\..\..\source\Components\utils\util_lcd.c</name>
      <outputs>
        <tool>
          <name>BICOMP</name>
          <file> 21</file>
        </tool>
        <tool>
          <name>ICC8051</name>
          <file> 54</file>
        </tool>
      </outputs>
      <inputs>
        <tool>
          <name>BICOMP</name>
          <file> 26 64 5 3 6 25 9 23 2 1 12 30 10 44 63</file>
        </tool>
      </inputs>
    </file>
    <file>
      <name>$PROJ_DIR$\..\..\..\source\Components\targets\srf05_soc\hal_digio.c</name>
      <outputs>
        <tool>
          <name>BICOMP</name>
          <file> 70</file>
        </tool>
        <tool>
          <name>ICC8051</name>
          <file> 61</file>
        </tool>
      </outputs>
      <inputs>
        <tool>
          <name>BICOMP</name>
          <file> 3 6 26 64 5 25 19 0</file>
        </tool>
      </inputs>
    </file>
    <file>
      <name>$PROJ_DIR$\..\..\..\source\Components\radios\cc2530\adc.c</name>
      <outputs>
        <tool>
          <name>BICOMP</name>
          <file> 51</file>
        </tool>
        <tool>
          <name>ICC8051</name>
          <file> 50</file>
        </tool>
      </outputs>
      <inputs>
        <tool>
          <name>BICOMP</name>
          <file> 15 3 6 64</file>
        </tool>
      </inputs>
    </file>
    <file>
      <name>$PROJ_DIR$\..\..\..\source\Components\radios\cc2530\clock.c</name>
      <outputs>
        <tool>
          <name>BICOMP</name>
          <file> 60</file>
        </tool>
        <tool>
          <name>ICC8051</name>
          <file> 39</file>
        </tool>
      </outputs>
      <inputs>
        <tool>
          <name>BICOMP</name>
          <file> 16 3 6 64 25</file>
        </tool>
      </inputs>
    </file>
    <file>
      <name>$PROJ_DIR$\temp\spectrum_analyzer\spectrum_analyzer.pbd</name>
      <inputs>
        <tool>
          <name>BILINK</name>
          <file> 51 60 40 65 13 70 38 43 37 41 68 42 45 52 48 21</file>
        </tool>
      </inputs>
    </file>
  </configuration>
</project>


