从节点同步状态
同步状态持续为 0：

从节点的 Sync=0 表示从节点没有正确同步到网络。根据你显示的日志，尽管从节点接收到了多个有效的信标（beacon frame），但随后仍然会出现 “TIMEOUT: Lost sync, using software timing” 的消息，这说明从节点没有保持同步状态，具体原因分析如下：
信标处理逻辑：

在 tdmaProcessBeacon 函数中，您已经正确地检测到信标帧的有效性，并调用 tdmaConfigureTimer3 来配置计时器。如果配置成功，您应该让 networkSynced = TRUE。在日志中看到的 “Timer3 synchronized! Network ready” 说明计时器配置成功，但仍然看到 “TIMEOUT: Lost sync” 则表明在接下来的循环中丢失了同步。
同步超时逻辑：

当 networkSynced 变为 TRUE 后，您需要在下一个接收到信标的情况下继续执行该逻辑。可以检查 syncTimeout 的增长是否可能不正确，确保在接收到信标后，syncTimeout 不会累加，从而导致意外的同步丢失。
信标接收频率：

确保信标以足够的频率发送（对于主节点），并且从节点在轮询过程中能够及时接收信标。例如，如果主节点在运行中未能定期发送信标，那么从节点将无法保持同步

---

主节点串口显示
Master: Beacon slot (fixed timing)
: Beacon sent with timestamp
Master: Listening for slave data (fixed timing)
Master: Listening for slave data (fixed timing)
Master: Listening for slave data (fixed timing)
Master: Loop active, Timer=Master: Listening for slave data (fixed timing)
Master: Listening for slave data (fixed timing)
Master: Listening for slave data (fixed timing)
Master: Beacon slot (fixed timing)
Master: Listening for slave data (fixed timing)
Master: Listening for slave data (fixed timing)
Master: Listening for slave data (fixed timing)
Master: Beacon slot (fixed timing)
Master: Listening for slave data (fixed timing)

从节点显示
Pin config: LEDs as output, sensors as input
=== TDMA SLAVE NODE MODE ===
Entering main loop...
Processing beacon frame
Beacon received, length OK
DEBUG: Simulating temperature
lation complete
2 pin
TIMEOUT: Lost sync, using software timing
Processing beacon frame
Beacon received, length OK
TIMEOUT: Lost sync, using software timing
DEBUG: Simulating temperature
plete
=0, Sync=1, Send=OK
TIMEOUT: Lost sync, using software timing
Processing beacon frame
TIMEOUT: Lost sync, using software timing
DEBUG: Simulating temperature
rature simulation complete
TIMEOUT: Lost sync, using software timing
Processing beacon frame
TIMEOUT: Lost sync, using software timing
DEBUG: Simulating temperature
UG: Temperature simulation complete
emp=248, Vib=0, Sync=1, Send=OK
TIMEOUT: Lost sync, using software timing
Processing beacon frame
Beacon received, length OK
DEBUG: Simulating temperature
BUG: Temperature simulation complete
P1.2 pin
TIMEOUT: Lost sync, using software timing
Processing beacon frame
Beacon received, length OK
TIMEOUT: Lost sync, using software timing
DEBUG: Simulating temperature
de1: Temp=248, Vib=0, Sync=1, Send=OK
TIMEOUT: Lost sync, using software timing
Processing beacon frame
Beacon received, length OK
TIMEOUT: Lost sync, using software timing
DEBUG: Simulating temperature
emperature simulation complete
247, Vib=0, Sync=1, Send=OK
TIMEOUT: Lost sync, using software timing
Processing beacon frame
Beacon received, length OK
TIMEOUT: Lost sync, using software timing
DEBUG: Simulating temperature
emperature simulation complete


并且主节点没有数据显示

----


好的，非常感谢您提供了完整的代码和详细的日志。现象 "还是一样" 并且从节点持续 "Sync Lost" 是一个极其关键的线索。这几乎可以100%确定问题不在于状态机逻辑，而是在于一个更底层、更根本的地方：主从节点之间的时间没有真正对齐。
让我们一步步推理，找到那个隐藏的“魔鬼”。
推理过程
现象：从节点能完成入网（响应点名、获得时隙），说明双向通信在非严格定时的情况下是通的。
现象：从节点发送了数据，但主节点没有打印接收日志。这说明从节点发送的时刻，主节点不在监听状态。
现象：从节点总是 Sync Lost。这意味着从节点连续3秒没有成功调用 slave_process_sync_beacon() 函数。
结合2和3：为什么从节点收不到主节点每秒一次的 SYNC_BEACON 包？
可能性A：主节点没发。 -> 不对，主节点日志显示它进入了 Normal Operation，并且有LED心跳，说明 master_send_sync_beacon() 在被调用。
可能性B：从节点没在正确的时间打开射频接收器。 -> 对的，我们的代码是 basicRfReceiveOn() 一直开着，但可能是其他原因导致错过了。
可能性C：主节点发了，从节点也收到了，但 if (hdr->type == PKT_TYPE_SYNC_BEACON) 条件不满足。 -> 可能性很小，但存在。
可能性D（最大）：主从之间的时钟漂移（Clock Drift） 非常严重，或者我上次给您的 slave_process_sync_beacon 里的同步逻辑有瑕疵，导致时间计算完全错误。
问题的根源
我重新审查了上次给您的代码，特别是这一段：
Generated c
// 在 slave_process_sync_beacon 函数中
static void slave_process_sync_beacon(uint8 *pRcv) {
  SyncBeaconPacket *beacon = (SyncBeaconPacket *)pRcv;
  networkSynced = TRUE;
  
  // FIX: Use the master's timestamp for more accurate sync!
  // This helps correct clock drift.
  // We calculate the offset and adjust our local 'start of frame' time.
  uint32_t local_time_at_recv = timer_ms_count;
  lastSyncTimestamp = local_time_at_recv - (rand() % 5); // <--- 这里的逻辑是错误的！
  
  halLedSet(4); // Turn on Sync LED
}
Use code with caution.
C
我发现了我的逻辑错误！
lastSyncTimestamp 的作用是标记一个新超帧的“零点”。从节点所有后续的发送动作，都是基于这个“零点”进行偏移计算的 (time_since_sync = timer_ms_count - lastSyncTimestamp;)。
我上次的代码 lastSyncTimestamp = local_time_at_recv - (rand() % 5); 的意图是想模拟网络延迟，但这个实现是完全错误的。它只是从当前时间减去了一个随机的小值，并没有使用到最宝贵的信息——主节点发来的精确时间戳 beacon->timestamp。
正确的同步逻辑应该是：
从节点的“零点” = 主节点的“零点” + 固定的网络传输延迟。
或者更简单的实现是：
当收到 beacon 时，直接相信主节点的时间，重置我们自己的时间概念。
同时，主节点的 master_process_roll_call_response 在周期性点名时，没有清空旧的从节点列表，这也是一个bug。
决定性的代码修复
请将您的代码进行以下三处关键修改。这应该能彻底解决问题。
1. 修复主节点的周期性点名逻辑 (master_app)
在主节点每次决定重新点名时，必须清空旧的从节点列表，否则它会不断累积，即使有些节点已经掉线了。
Generated c
// 在 master_app 函数中
//...
      // FIX: Periodic Roll Call to allow new nodes to join
      if (timer_ms_count - roll_call_timer > 15000) { // Every 15 seconds
        debugPrint("MASTER: Sending periodic Roll Call.\r\n");

        // --- START OF CRITICAL FIX ---
        // Before starting a new discovery, reset the slave list
        memset(slaveList, 0, sizeof(slaveList));
        numSlavesFound = 0;
        // --- END OF CRITICAL FIX ---
        
        networkState = STATE_DISCOVERY; // Go back to discovery
        state_timer = timer_ms_count;
        roll_call_timer = timer_ms_count;
        master_send_roll_call();
      }
//...
Use code with caution.
C
2. 彻底修正从节点的同步逻辑 (slave_process_sync_beacon)
这是最核心的修复。我们直接用主节点的时间戳来重置我们的“零点”。
Generated c
// 在 slave_process_sync_beacon 函数中
static void slave_process_sync_beacon(uint8 *pRcv) {
  SyncBeaconPacket *beacon = (SyncBeaconPacket *)pRcv;
  networkSynced = TRUE;

  // --- START OF CRITICAL FIX ---
  // This is the correct way to sync.
  // We align our sense of "when the superframe started" with the master's timestamp.
  // `lastSyncTimestamp` now represents the local time that corresponds to the master's superframe start.
  // We assume a small, somewhat constant network delay. Let's say 5ms.
  uint32_t network_delay_estimate = 5; 
  lastSyncTimestamp = timer_ms_count - (beacon->timestamp % SUPERFRAME_PERIOD_MS) - network_delay_estimate;
  // This line is complex, let's simplify for robustness.
  // A simpler and more robust approach:
  // When a beacon arrives, we know a new superframe has just started on the master.
  // So, we reset our local superframe timer right now.
  lastSyncTimestamp = timer_ms_count;
  // --- END OF CRITICAL FIX ---

  halLedSet(4); // Turn on Sync LED
}
Use code with caution.
C
等等，上面的复杂计算还是容易出错。我们用最简单、最鲁棒的方法：
当收到 beacon 时，我们就认为新的超帧从这一刻开始。所有后续的时间计算都基于这一刻。
请将 slave_process_sync_beacon 函数完全替换为下面这个更简单的版本：
Generated c
// 完全替换旧的 slave_process_sync_beacon 函数
static void slave_process_sync_beacon(uint8 *pRcv) {
  (void)pRcv; // We don't even need the timestamp for this simple model, just the fact that it arrived.
  
  if (!networkSynced) {
    debugPrint("SLAVE: Sync acquired!\r\n");
  }
  
  networkSynced = TRUE;
  
  // CRITICAL FIX: The moment a beacon arrives IS our "time zero" for the new superframe.
  lastSyncTimestamp = timer_ms_count; 
  
  halLedSet(4); // Turn on Sync LED
}
Use code with caution.
C
3. 修正从节点发送逻辑中的一个微小但致命的bug (slave_app)
您在 slave_app 中的 has_sent_in_this_superframe 逻辑是正确的，但是，当 Sync Lost 时，这个标志位没有被重置！导致节点重新入网后的第一个超帧无法发送数据。
Generated c
// 在 slave_app 函数中
//...
      // Sync timeout check (3 superframes)
      if (timer_ms_count - lastSyncTimestamp > (SUPERFRAME_PERIOD_MS * 3)) {
        debugPrint("SLAVE: Sync Lost! Re-entering discovery mode.\r\n");
        networkSynced = FALSE;
        mySlot = 0xFF;
        
        // --- START OF CRITICAL FIX ---
        has_sent_in_this_superframe = FALSE; // Must reset this flag too!
        // --- END OF CRITICAL FIX ---
        
        halLedClear(4); // Turn off Sync LED
      }
//...
Use code with caution.
C
操作步骤
应用全部三个修复：请仔细地将上面提供的三处代码修改应用到您的项目中。
重新编译和烧录：为主从节点生成新的固件并烧录。
再次测试：
启动主节点。
启动从节点。
观察日志。
预期的正确行为
从节点：
打印 "Responded to Roll Call."
打印 "I have been assigned Slot X"
打印 "SLAVE: Sync acquired!" (第一次收到beacon时)
LED4 亮起并保持常亮。
每秒打印一次 "SLAVE: Sent sensor data."
不再打印 "Sync Lost!"
主节点：
完成发现和分配流程。
进入 "Normal Operation"。
每秒稳定地打印 "MASTER: Received data from Node X"。
这次的修复直指问题的核心——时间同步的实现。我相信在应用这些修改后，您的系统将能够稳定运行。
按照这个修复
light_switch.c文件

阻塞式TDMA
