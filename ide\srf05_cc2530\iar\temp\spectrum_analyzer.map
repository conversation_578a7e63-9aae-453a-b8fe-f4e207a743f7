################################################################################
#                                                                              #
#      IAR Universal Linker V4.61L/W32                                         #
#                                                                              #
#           Link time     =  10/Nov/2010  18:05:43                             #
#           Target CPU    =  x51                                               #
#           List file     =  "C:\Texas Instruments\CC2530 BasicRF\ide\srf05_cc #
#                            2530\iar\temp\spectrum_analyzer.map"              #
#           Output file 1 =  "C:\Texas Instruments\CC2530 BasicRF\ide\srf05_cc #
#                            2530\iar\spectrum_analyzer.hex"                   #
#                            Format: intel-extended                            #
#           Output file 2 =  "C:\Texas Instruments\CC2530 BasicRF\ide\srf05_cc #
#                            2530\iar\spectrum_analyzer.d51"                   #
#                            Format: debug                                     #
#                            UBROF version 10.0.2                              #
#                            Using library modules for C-SPY (-rt)             #
#           Command line  =  "-IC:\Program Files\IAR Systems\Embedded Workbenc #
#                            h 5.3\8051\CONFIG\"                               #
#                            -D_NR_OF_BANKS=0 -D_CODEBANK_END=0                #
#                            -D_CODEBANK_START=0                               #
#                            "C:\Texas Instruments\CC2530 BasicRF\ide\srf05_cc #
#                            2530\iar\temp\spectrum_analyzer\adc.r51"          #
#                            "C:\Texas Instruments\CC2530 BasicRF\ide\srf05_cc #
#                            2530\iar\temp\spectrum_analyzer\clock.r51"        #
#                            "C:\Texas Instruments\CC2530 BasicRF\ide\srf05_cc #
#                            2530\iar\temp\spectrum_analyzer\hal_assert.r51"   #
#                            "C:\Texas Instruments\CC2530 BasicRF\ide\srf05_cc #
#                            2530\iar\temp\spectrum_analyzer\hal_board.r51"    #
#                            "C:\Texas Instruments\CC2530 BasicRF\ide\srf05_cc #
#                            2530\iar\temp\spectrum_analyzer\hal_button.r51"   #
#                            "C:\Texas Instruments\CC2530 BasicRF\ide\srf05_cc #
#                            2530\iar\temp\spectrum_analyzer\hal_digio.r51"    #
#                            "C:\Texas Instruments\CC2530 BasicRF\ide\srf05_cc #
#                            2530\iar\temp\spectrum_analyzer\hal_int.r51"      #
#                            "C:\Texas Instruments\CC2530 BasicRF\ide\srf05_cc #
#                            2530\iar\temp\spectrum_analyzer\hal_joystick.r51" #
#                            "C:\Texas Instruments\CC2530 BasicRF\ide\srf05_cc #
#                            2530\iar\temp\spectrum_analyzer\hal_lcd_srf05.r51 #
#                            "                                                 #
#                            "C:\Texas Instruments\CC2530 BasicRF\ide\srf05_cc #
#                            2530\iar\temp\spectrum_analyzer\hal_led.r51"      #
#                            "C:\Texas Instruments\CC2530 BasicRF\ide\srf05_cc #
#                            2530\iar\temp\spectrum_analyzer\hal_mcu.r51"      #
#                            "C:\Texas Instruments\CC2530 BasicRF\ide\srf05_cc #
#                            2530\iar\temp\spectrum_analyzer\hal_rf.r51"       #
#                            "C:\Texas Instruments\CC2530 BasicRF\ide\srf05_cc #
#                            2530\iar\temp\spectrum_analyzer\hal_rf_util.r51"  #
#                            "C:\Texas Instruments\CC2530 BasicRF\ide\srf05_cc #
#                            2530\iar\temp\spectrum_analyzer\spectrum_analyzer #
#                            .r51"                                             #
#                            "C:\Texas Instruments\CC2530 BasicRF\ide\srf05_cc #
#                            2530\iar\temp\spectrum_analyzer\util.r51"         #
#                            "C:\Texas Instruments\CC2530 BasicRF\ide\srf05_cc #
#                            2530\iar\temp\spectrum_analyzer\util_lcd.r51"     #
#                            -o                                                #
#                            "C:\Texas Instruments\CC2530 BasicRF\ide\srf05_cc #
#                            2530\iar\spectrum_analyzer.d51"                   #
#                            -l                                                #
#                            "C:\Texas Instruments\CC2530 BasicRF\ide\srf05_cc #
#                            2530\iar\temp\spectrum_analyzer.map"              #
#                            -xmsn                                             #
#                            "-IC:\Program Files\IAR Systems\Embedded Workbenc #
#                            h 5.3\8051\LIB\"                                  #
#                            -f lnk51ew_cc2530b.xcl (-D_IDATA_END=0xFF         #
#                            -D_PDATA_START=0x1E00 -D_PDATA_END=0x1EFF         #
#                            -D_IXDATA_START=0x0001 -D_IXDATA_END=0x1EFF       #
#                            -D_XDATA_START=_IXDATA_START                      #
#                            -D_XDATA_END=_IXDATA_END -D_CODE_START=0x0000     #
#                            -D_CODE_END=0x7FFF -D_FIRST_BANK_ADDR=0x10000     #
#                            -M(CODE)[(_CODEBANK_START+_FIRST_BANK_ADDR)-(_COD #
#                            EBANK_END+_FIRST_BANK_ADDR)]*_NR_OF_BANKS+0x10000 #
#                            =0x8000                                           #
#                            -ww69=i -D_NEAR_CODE_END=_CODE_END                #
#                            -D?REGISTER_BANK=0 -D_REGISTER_BANK_START=0       #
#                            -D?PBANK_NUMBER=0x1E -D?PBANK=0x93                #
#                            -D_BREG_START=0x00 -D?VB=0x20                     #
#                            -D_FAR_DATA_NR_OF_BANKS=0x0E                      #
#                            -D_FAR_DATA_START=0x010001                        #
#                            -D_FAR_DATA_END=0xFFFFFF                          #
#                            -D_FAR_CODE_START=_CODE_START                     #
#                            -D_FAR_CODE_END=_CODE_END -f lnk_base.xcl         #
#                            (-Z(BIT)BREG=_BREG_START -Z(BIT)BIT_N=0-7F        #
#                            -Z(DATA)REGISTERS+8=_REGISTER_BANK_START          #
#                            -Z(DATA)BDATA_Z,BDATA_N,BDATA_I=20-2F             #
#                            -Z(DATA)VREG+_NR_OF_VIRTUAL_REGISTERS=08-7F       #
#                            -Z(DATA)PSP,XSP=08-7F -Z(DATA)DOVERLAY=08-7F      #
#                            -Z(DATA)DATA_I,DATA_Z,DATA_N=08-7F                #
#                            -U(IDATA)0-7F=(DATA)0-7F                          #
#                            -Z(IDATA)IDATA_I,IDATA_Z,IDATA_N=08-_IDATA_END    #
#                            -Z(IDATA)ISTACK+_IDATA_STACK_SIZE#08-_IDATA_END   #
#                            -Z(IDATA)IOVERLAY=08-FF -Z(CODE)INTVEC=0          #
#                            -Z(CODE)CSTART=_CODE_START-_CODE_END              #
#                            -Z(CODE)BIT_ID,BDATA_ID,DATA_ID,IDATA_ID,IXDATA_I #
#                            D,PDATA_ID,XDATA_ID=_CODE_START-_CODE_END         #
#                            -Z(CODE)HUGE_ID=_FAR_CODE_START-_FAR_CODE_END     #
#                            -Z(CODE)BANK_RELAYS,RCODE,DIFUNCT,CODE_C,CODE_N,N #
#                            EAR_CODE=_CODE_START-_CODE_END                    #
#                            -P(CODE)BANKED_CODE=_CODE_START-_CODE_END,[(_CODE #
#                            BANK_START+_FIRST_BANK_ADDR)-(_CODEBANK_END+_FIRS #
#                            T_BANK_ADDR)]*_NR_OF_BANKS+10000                  #
#                            -P(CODE)FAR_CODE_C,FAR_CODE_N,FAR_CODE=[_FAR_CODE #
#                            _START-_FAR_CODE_END]/10000                       #
#                            -P(CODE)HUGE_CODE_C=_FAR_CODE_START-_FAR_CODE_END #
#                            -Z(CODE)CHECKSUM#_CODE_END                        #
#                            -Z(XDATA)EXT_STACK+_EXTENDED_STACK_SIZE=_EXTENDED #
#                            _STACK_START                                      #
#                            -Z(XDATA)PSTACK+_PDATA_STACK_SIZE=_PDATA_START-_P #
#                            DATA_END                                          #
#                            -Z(XDATA)XSTACK+_XDATA_STACK_SIZE=_XDATA_START-_X #
#                            DATA_END                                          #
#                            -Z(XDATA)PDATA_Z,PDATA_I=_PDATA_START-_PDATA_END  #
#                            -P(XDATA)PDATA_N=_PDATA_START-_PDATA_END          #
#                            -Z(XDATA)IXDATA_Z,IXDATA_I=_IXDATA_START-_IXDATA_ #
#                            END                                               #
#                            -P(XDATA)IXDATA_N=_IXDATA_START-_IXDATA_END       #
#                            -Z(XDATA)XDATA_Z,XDATA_I=_XDATA_START-_XDATA_END  #
#                            -P(XDATA)XDATA_N=_XDATA_START-_XDATA_END          #
#                            -Z(XDATA)XDATA_HEAP+_XDATA_HEAP_SIZE=_XDATA_START #
#                            -_XDATA_END                                       #
#                            -Z(CONST)XDATA_ROM_C=_XDATA_START-_XDATA_END      #
#                            -Z(XDATA)FAR_Z=[_FAR_DATA_START-_FAR_DATA_END]/10 #
#                            000                                               #
#                            -Z(XDATA)FAR_I=[_FAR_DATA_START-_FAR_DATA_END]/10 #
#                            000                                               #
#                            -Z(CODE)FAR_ID=[_FAR_CODE_START-_FAR_CODE_END]/10 #
#                            000                                               #
#                            -Z(XDATA)FAR_HEAP+_FAR_HEAP_SIZE=[_FAR_DATA_START #
#                            -_FAR_DATA_END]/10000                             #
#                            -P(XDATA)FAR_N=[_FAR_DATA_START-_FAR_DATA_END]*_F #
#                            AR_DATA_NR_OF_BANKS+10000                         #
#                            -P(CONST)FAR_ROM_C=[_FAR_DATA_START-_FAR_DATA_END #
#                            ]*_FAR_DATA_NR_OF_BANKS+10000                     #
#                            -Z(XDATA)HUGE_Z,HUGE_I=_FAR_DATA_START-_FAR_DATA_ #
#                            END                                               #
#                            -P(XDATA)HUGE_N=_FAR_DATA_START-_FAR_DATA_END     #
#                            -Z(XDATA)HUGE_HEAP+_HUGE_HEAP_SIZE=_FAR_DATA_STAR #
#                            T-_FAR_DATA_END                                   #
#                            -Z(CONST)HUGE_ROM_C=_FAR_DATA_START-_FAR_DATA_END #
#                            -cx51) -D_BANK0_START=0x00000                     #
#                            -D_BANK0_END=0x07FFF -D_BANK1_START=0x18000       #
#                            -D_BANK1_END=0x1FFFF -D_BANK2_START=0x28000       #
#                            -D_BANK2_END=0x2FFFF -D_BANK3_START=0x38000       #
#                            -D_BANK3_END=0x3FFFF -D_BANK4_START=0x48000       #
#                            -D_BANK4_END=0x4FFFF -D_BANK5_START=0x58000       #
#                            -D_BANK5_END=0x5FFFF -D_BANK6_START=0x68000       #
#                            -D_BANK6_END=0x6FFFF -D_BANK7_START=0x78000       #
#                            -D_BANK7_END=0x7FFFF                              #
#                            -P(CODE)BANK0=_BANK0_START-_BANK0_END             #
#                            -P(CODE)BANK1=_BANK1_START-_BANK1_END             #
#                            -P(CODE)BANK2=_BANK2_START-_BANK2_END             #
#                            -P(CODE)BANK3=_BANK3_START-_BANK3_END             #
#                            -P(CODE)BANK4=_BANK4_START-_BANK4_END             #
#                            -P(CODE)BANK5=_BANK5_START-_BANK5_END             #
#                            -P(CODE)BANK6=_BANK6_START-_BANK6_END             #
#                            -P(CODE)BANK7=_BANK7_START-_BANK7_END             #
#                            -D_FLASH_LOCK_BITS_START=((_NR_OF_BANKS*_FIRST_BA #
#                            NK_ADDR)+0xFFF0)                                  #
#                            -D_FLASH_LOCK_BITS_END=((_NR_OF_BANKS*_FIRST_BANK #
#                            _ADDR)+0xFFFF)                                    #
#                            -Z(CODE)FLASH_LOCK_BITS=_FLASH_LOCK_BITS_START-_F #
#                            LASH_LOCK_BITS_END                                #
#                            -U(CODE)0x0000=(CODE)_FLASH_LOCK_BITS_START-_FLAS #
#                            H_LOCK_BITS_END)                                  #
#                            -D_NR_OF_VIRTUAL_REGISTERS=8                      #
#                            -e_medium_write=_formatted_write                  #
#                            -e_medium_read=_formatted_read -rt                #
#                            "-Ointel-extended=C:\Texas Instruments\CC2530 Bas #
#                            icRF\ide\srf05_cc2530\iar\spectrum_analyzer.hex"  #
#                            -s __program_start                                #
#                            "C:\Program Files\IAR Systems\Embedded Workbench  #
#                            5.3\8051\LIB\CLIB\cl-pli-nlxd-1e16x01.r51"        #
#                            -D_IDATA_STACK_SIZE=0x40                          #
#                            -D_EXTENDED_STACK_START=0x00                      #
#                            -D_EXTENDED_STACK_SIZE=0x00                       #
#                            -D_PDATA_STACK_SIZE=0x80                          #
#                            -D_XDATA_STACK_SIZE=0x100                         #
#                            -D_XDATA_HEAP_SIZE=0xFF -D_FAR_HEAP_SIZE=0xFFF    #
#                            -D_HUGE_HEAP_SIZE=0xFFF                           #
#                                                                              #
#                           Copyright (C) 1987-2009 IAR Systems AB.            #
################################################################################





                ****************************************
                *                                      *
                *           CROSS REFERENCE            *
                *                                      *
                ****************************************

       Program entry at : CODE      000000D3  Relocatable, from module : CSTARTUP




                ****************************************
                *                                      *
                *            RUNTIME MODEL             *
                *                                      *
                ****************************************

  __calling_convention     = xdata_reentrant
  __code_model             = near
  __core                   = plain
  __data_model             = large
  __dptr_size              = 16
  __extended_stack         = disabled
  __location_for_constants = data
  __number_of_dptrs        = 1
  __rt_version             = 1



                ****************************************
                *                                      *
                *              MODULE MAP              *
                *                                      *
                ****************************************


  DEFINED ABSOLUTE ENTRIES
    *************************************************************************

  DEFINED ABSOLUTE ENTRIES
  PROGRAM MODULE, NAME : ?ABS_ENTRY_MOD

Absolute parts
           ENTRY                   ADDRESS         REF BY
           =====                   =======         ======
           _HUGE_HEAP_SIZE         00000FFF 
           _FAR_HEAP_SIZE          00000FFF 
           _XDATA_HEAP_SIZE        000000FF 
           _XDATA_STACK_SIZE       ******** 
           _PDATA_STACK_SIZE       ******** 
           _EXTENDED_STACK_SIZE    ******** 
           _EXTENDED_STACK_START   ******** 
           _IDATA_STACK_SIZE       ******** 
           _NR_OF_VIRTUAL_REGISTERS
                                   ******** 
           _FLASH_LOCK_BITS_END    0000FFFF 
           _FLASH_LOCK_BITS_START
                                   0000FFF0 
           _BANK7_END              0007FFFF 
           _BANK7_START            ******** 
           _BANK6_END              0006FFFF 
           _BANK6_START            ******** 
           _BANK5_END              0005FFFF 
           _BANK5_START            ******** 
           _BANK4_END              0004FFFF 
           _BANK4_START            ******** 
           _BANK3_END              0003FFFF 
           _BANK3_START            ******** 
           _BANK2_END              0002FFFF 
           _BANK2_START            ******** 
           _BANK1_END              0001FFFF 
           _BANK1_START            ******** 
           _BANK0_END              00007FFF 
           _BANK0_START            ******** 
           _FAR_CODE_END           00007FFF 
           _FAR_CODE_START         ******** 
           _FAR_DATA_END           00FFFFFF 
           _FAR_DATA_START         ******** 
           _FAR_DATA_NR_OF_BANKS   0000000E 
           ?VB                     ********        ?FUNC_ENTER_XDATA (?FUNC_ENTER_XDATA)
                                                   ?FUNC_LEAVE_XDATA (?FUNC_LEAVE_XDATA)
                                                   ?INTERRUPT_ENTER_XSP (?INTERRUPT_ENTER_XSP)
                                                   ?INTERRUPT_LEAVE_XSP (?INTERRUPT_LEAVE_XSP)
           _BREG_START             ******** 
           ?PBANK                  ******** 
           ?PBANK_NUMBER           0000001E 
           _REGISTER_BANK_START    ******** 
           ?REGISTER_BANK          ********        Segment part 6 (CSTARTUP)
           _NEAR_CODE_END          00007FFF 
           _FIRST_BANK_ADDR        ******** 
           _CODE_END               00007FFF 
           _CODE_START             ******** 
           _XDATA_END              00001EFF 
           _XDATA_START            ******** 
           _IXDATA_END             00001EFF 
           _IXDATA_START           ******** 
           _PDATA_END              00001EFF 
           _PDATA_START            00001E00 
           _IDATA_END              000000FF 
           _CODEBANK_START         ******** 
           _CODEBANK_END           ******** 
           _NR_OF_BANKS            ******** 
    *************************************************************************

  FILE NAME : C:\Texas Instruments\CC2530 BasicRF\ide\srf05_cc2530\iar\temp\spectrum_analyzer\adc.r51
    *************************************************************************

  FILE NAME : C:\Texas Instruments\CC2530 BasicRF\ide\srf05_cc2530\iar\temp\spectrum_analyzer\clock.r51
    *************************************************************************

  FILE NAME : C:\Texas Instruments\CC2530 BasicRF\ide\srf05_cc2530\iar\temp\spectrum_analyzer\hal_assert.r51
    *************************************************************************

  FILE NAME : C:\Texas Instruments\CC2530 BasicRF\ide\srf05_cc2530\iar\temp\spectrum_analyzer\hal_board.r51
    *************************************************************************

  FILE NAME : C:\Texas Instruments\CC2530 BasicRF\ide\srf05_cc2530\iar\temp\spectrum_analyzer\hal_button.r51
    *************************************************************************

  FILE NAME : C:\Texas Instruments\CC2530 BasicRF\ide\srf05_cc2530\iar\temp\spectrum_analyzer\hal_digio.r51
    *************************************************************************

  FILE NAME : C:\Texas Instruments\CC2530 BasicRF\ide\srf05_cc2530\iar\temp\spectrum_analyzer\hal_int.r51
  PROGRAM MODULE, NAME : hal_int

  SEGMENTS IN THE MODULE
  ======================
SFR_AN
  Relative segment, address: DATA ******** - ******** (0x1 bytes), align: 0
  Segment part 1. ROOT.       Intra module refs:   HalLcd_HW_Control
                                                   HalLcd_HW_Write
                                                   halBoardInit
                                                   halDigioConfig
                                                   halLcdInit
                                                   halLedToggle
                                                   main
           ENTRY                   ADDRESS         REF BY
           =====                   =======         ======
           _A_P0                   ******** 
    -------------------------------------------------------------------------
SFR_AN
  Relative segment, address: DATA ******** - ******** (0x1 bytes), align: 0
  Segment part 2. ROOT.       Intra module refs:   adcSampleSingle
           ENTRY                   ADDRESS         REF BY
           =====                   =======         ======
           _A_TCON                 ******** 
    -------------------------------------------------------------------------
SFR_AN
  Relative segment, address: DATA 00000089 - 00000089 (0x1 bytes), align: 0
  Segment part 3. ROOT.       Intra module refs:   halDigioIntClear
                                                   port0_ISR
           ENTRY                   ADDRESS         REF BY
           =====                   =======         ======
           P0IFG                   00000089 
    -------------------------------------------------------------------------
SFR_AN
  Relative segment, address: DATA 0000008A - 0000008A (0x1 bytes), align: 0
  Segment part 4. ROOT.       Intra module refs:   halDigioIntClear
                                                   port1_ISR
           ENTRY                   ADDRESS         REF BY
           =====                   =======         ======
           P1IFG                   0000008A 
    -------------------------------------------------------------------------
SFR_AN
  Relative segment, address: DATA 0000008B - 0000008B (0x1 bytes), align: 0
  Segment part 5. ROOT.       Intra module refs:   halDigioIntClear
                                                   port2_ISR
           ENTRY                   ADDRESS         REF BY
           =====                   =======         ======
           P2IFG                   0000008B 
    -------------------------------------------------------------------------
SFR_AN
  Relative segment, address: DATA 0000008C - 0000008C (0x1 bytes), align: 0
  Segment part 6. ROOT.       Intra module refs:   appConfigIO
           ENTRY                   ADDRESS         REF BY
           =====                   =======         ======
           PICTL                   0000008C 
    -------------------------------------------------------------------------
SFR_AN
  Relative segment, address: DATA 0000008D - 0000008D (0x1 bytes), align: 0
  Segment part 7. ROOT.       Intra module refs:   halJoystickIntEnable
           ENTRY                   ADDRESS         REF BY
           =====                   =======         ======
           P1IEN                   0000008D 
    -------------------------------------------------------------------------
SFR_AN
  Relative segment, address: DATA 0000008F - 0000008F (0x1 bytes), align: 0
  Segment part 8. ROOT.       Intra module refs:   halBoardInit
           ENTRY                   ADDRESS         REF BY
           =====                   =======         ======
           P0INP                   0000008F 
    -------------------------------------------------------------------------
SFR_AN
  Relative segment, address: DATA 00000090 - 00000090 (0x1 bytes), align: 0
  Segment part 9. ROOT.       Intra module refs:   HalLcd_HW_Control
                                                   HalLcd_HW_Write
                                                   halBoardInit
                                                   halDigioConfig
                                                   halLcdInit
                                                   halLedToggle
                                                   main
           ENTRY                   ADDRESS         REF BY
           =====                   =======         ======
           _A_P1                   00000090 
    -------------------------------------------------------------------------
SFR_AN
  Relative segment, address: DATA 0000009A - 0000009A (0x1 bytes), align: 0
  Segment part 10. ROOT.      Intra module refs:   halJoystickIntEnable
                                                   main
           ENTRY                   ADDRESS         REF BY
           =====                   =======         ======
           IEN2                    0000009A 
    -------------------------------------------------------------------------
SFR_AN
  Relative segment, address: DATA 0000009B - 0000009B (0x1 bytes), align: 0
  Segment part 11. ROOT.      Intra module refs:   rfIsr
           ENTRY                   ADDRESS         REF BY
           =====                   =======         ======
           S1CON                   0000009B 
    -------------------------------------------------------------------------
SFR_AN
  Relative segment, address: DATA 0000009D - 0000009D (0x1 bytes), align: 0
  Segment part 12. ROOT.      Intra module refs:   clockSetMainSrc
           ENTRY                   ADDRESS         REF BY
           =====                   =======         ======
           SLEEPSTA                0000009D 
    -------------------------------------------------------------------------
SFR_AN
  Relative segment, address: DATA 0000009E - 0000009E (0x1 bytes), align: 0
  Segment part 13. ROOT.      Intra module refs:   clockSelect32k
                                                   clockSetMainSrc
                                                   halBoardInit
           ENTRY                   ADDRESS         REF BY
           =====                   =======         ======
           CLKCONSTA               0000009E 
    -------------------------------------------------------------------------
SFR_AN
  Relative segment, address: DATA 000000A0 - 000000A0 (0x1 bytes), align: 0
  Segment part 14. ROOT.      Intra module refs:   halDigioConfig
           ENTRY                   ADDRESS         REF BY
           =====                   =======         ======
           _A_P2                   000000A0 
    -------------------------------------------------------------------------
SFR_AN
  Relative segment, address: DATA 000000A8 - 000000A8 (0x1 bytes), align: 0
  Segment part 15. ROOT.      Intra module refs:   appConfigIO
                                                   halBoardInit
                                                   halJoystickIntConnect
                                                   rfIsr
           ENTRY                   ADDRESS         REF BY
           =====                   =======         ======
           _A_IEN0                 000000A8 
    -------------------------------------------------------------------------
SFR_AN
  Relative segment, address: DATA 000000AB - 000000AB (0x1 bytes), align: 0
  Segment part 16. ROOT.      Intra module refs:   halJoystickIntEnable
           ENTRY                   ADDRESS         REF BY
           =====                   =======         ======
           P0IEN                   000000AB 
    -------------------------------------------------------------------------
SFR_AN
  Relative segment, address: DATA 000000AC - 000000AC (0x1 bytes), align: 0
  Segment part 17. ROOT.      Intra module refs:   halJoystickIntEnable
           ENTRY                   ADDRESS         REF BY
           =====                   =======         ======
           P2IEN                   000000AC 
    -------------------------------------------------------------------------
SFR_AN
  Relative segment, address: DATA 000000B6 - 000000B6 (0x1 bytes), align: 0
  Segment part 18. ROOT.      Intra module refs:   adcSampleSingle
           ENTRY                   ADDRESS         REF BY
           =====                   =======         ======
           ADCCON3                 000000B6 
    -------------------------------------------------------------------------
SFR_AN
  Relative segment, address: DATA 000000B8 - 000000B8 (0x1 bytes), align: 0
  Segment part 19. ROOT.      Intra module refs:   halJoystickIntEnable
           ENTRY                   ADDRESS         REF BY
           =====                   =======         ======
           _A_IEN1                 000000B8 
    -------------------------------------------------------------------------
SFR_AN
  Relative segment, address: DATA 000000BA - 000000BA (0x1 bytes), align: 0
  Segment part 20. ROOT.      Intra module refs:   adcSampleSingle
           ENTRY                   ADDRESS         REF BY
           =====                   =======         ======
           ADCL                    000000BA 
    -------------------------------------------------------------------------
SFR_AN
  Relative segment, address: DATA 000000BB - 000000BB (0x1 bytes), align: 0
  Segment part 21. ROOT.      Intra module refs:   adcSampleSingle
           ENTRY                   ADDRESS         REF BY
           =====                   =======         ======
           ADCH                    000000BB 
    -------------------------------------------------------------------------
SFR_AN
  Relative segment, address: DATA 000000BE - 000000BE (0x1 bytes), align: 0
  Segment part 22. ROOT.      Intra module refs:   clockSetMainSrc
           ENTRY                   ADDRESS         REF BY
           =====                   =======         ======
           SLEEPCMD                000000BE 
    -------------------------------------------------------------------------
SFR_AN
  Relative segment, address: DATA 000000C0 - 000000C0 (0x1 bytes), align: 0
  Segment part 23. ROOT.      Intra module refs:   port0_ISR
           ENTRY                   ADDRESS         REF BY
           =====                   =======         ======
           _A_IRCON                000000C0 
    -------------------------------------------------------------------------
SFR_AN
  Relative segment, address: DATA 000000C6 - 000000C6 (0x1 bytes), align: 0
  Segment part 24. ROOT.      Intra module refs:   clockSelect32k
                                                   clockSetMainSrc
           ENTRY                   ADDRESS         REF BY
           =====                   =======         ======
           CLKCONCMD               000000C6 
    -------------------------------------------------------------------------
SFR_AN
  Relative segment, address: DATA 000000E1 - 000000E1 (0x1 bytes), align: 0
  Segment part 25. ROOT.      Intra module refs:   main
           ENTRY                   ADDRESS         REF BY
           =====                   =======         ======
           RFST                    000000E1 
    -------------------------------------------------------------------------
SFR_AN
  Relative segment, address: DATA 000000E8 - 000000E8 (0x1 bytes), align: 0
  Segment part 26. ROOT.      Intra module refs:   port1_ISR
                                                   port2_ISR
           ENTRY                   ADDRESS         REF BY
           =====                   =======         ======
           _A_IRCON2               000000E8 
    -------------------------------------------------------------------------
SFR_AN
  Relative segment, address: DATA 000000E9 - 000000E9 (0x1 bytes), align: 0
  Segment part 27. ROOT.      Intra module refs:   rfIsr
           ENTRY                   ADDRESS         REF BY
           =====                   =======         ======
           RFIRQF0                 000000E9 
    -------------------------------------------------------------------------
SFR_AN
  Relative segment, address: DATA 000000F1 - 000000F1 (0x1 bytes), align: 0
  Segment part 28. ROOT.      Intra module refs:   halBoardInit
           ENTRY                   ADDRESS         REF BY
           =====                   =======         ======
           PERCFG                  000000F1 
    -------------------------------------------------------------------------
SFR_AN
  Relative segment, address: DATA 000000F2 - 000000F2 (0x1 bytes), align: 0
  Segment part 29. ROOT.      Intra module refs:   adcSampleSingle
           ENTRY                   ADDRESS         REF BY
           =====                   =======         ======
           ADCCFG                  000000F2 
    -------------------------------------------------------------------------
SFR_AN
  Relative segment, address: DATA 000000F3 - 000000F3 (0x1 bytes), align: 0
  Segment part 30. ROOT.      Intra module refs:   appConfigIO
                                                   halBoardInit
                                                   halDigioConfig
                                                   halLcdInit
           ENTRY                   ADDRESS         REF BY
           =====                   =======         ======
           P0SEL                   000000F3 
    -------------------------------------------------------------------------
SFR_AN
  Relative segment, address: DATA 000000F4 - 000000F4 (0x1 bytes), align: 0
  Segment part 31. ROOT.      Intra module refs:   halBoardInit
                                                   halDigioConfig
                                                   halLcdInit
           ENTRY                   ADDRESS         REF BY
           =====                   =======         ======
           P1SEL                   000000F4 
    -------------------------------------------------------------------------
SFR_AN
  Relative segment, address: DATA 000000F5 - 000000F5 (0x1 bytes), align: 0
  Segment part 32. ROOT.      Intra module refs:   appConfigIO
                                                   halBoardInit
                                                   halDigioConfig
           ENTRY                   ADDRESS         REF BY
           =====                   =======         ======
           P2SEL                   000000F5 
    -------------------------------------------------------------------------
SFR_AN
  Relative segment, address: DATA 000000F7 - 000000F7 (0x1 bytes), align: 0
  Segment part 33. ROOT.      Intra module refs:   appConfigIO
                                                   halBoardInit
           ENTRY                   ADDRESS         REF BY
           =====                   =======         ======
           P2INP                   000000F7 
    -------------------------------------------------------------------------
SFR_AN
  Relative segment, address: DATA 000000F8 - 000000F8 (0x1 bytes), align: 0
  Segment part 34. ROOT.      Intra module refs:   HalLcd_HW_Control
                                                   HalLcd_HW_Write
                                                   halBoardInit
           ENTRY                   ADDRESS         REF BY
           =====                   =======         ======
           _A_U1CSR                000000F8 
    -------------------------------------------------------------------------
SFR_AN
  Relative segment, address: DATA 000000F9 - 000000F9 (0x1 bytes), align: 0
  Segment part 35. ROOT.      Intra module refs:   HalLcd_HW_Control
                                                   HalLcd_HW_Write
           ENTRY                   ADDRESS         REF BY
           =====                   =======         ======
           U1DBUF                  000000F9 
    -------------------------------------------------------------------------
SFR_AN
  Relative segment, address: DATA 000000FA - 000000FA (0x1 bytes), align: 0
  Segment part 36. ROOT.      Intra module refs:   halBoardInit
           ENTRY                   ADDRESS         REF BY
           =====                   =======         ======
           U1BAUD                  000000FA 
    -------------------------------------------------------------------------
SFR_AN
  Relative segment, address: DATA 000000FB - 000000FB (0x1 bytes), align: 0
  Segment part 37. ROOT.      Intra module refs:   halBoardInit
           ENTRY                   ADDRESS         REF BY
           =====                   =======         ======
           U1UCR                   000000FB 
    -------------------------------------------------------------------------
SFR_AN
  Relative segment, address: DATA 000000FC - 000000FC (0x1 bytes), align: 0
  Segment part 38. ROOT.      Intra module refs:   halBoardInit
           ENTRY                   ADDRESS         REF BY
           =====                   =======         ======
           U1GCR                   000000FC 
    -------------------------------------------------------------------------
SFR_AN
  Relative segment, address: DATA 000000FD - 000000FD (0x1 bytes), align: 0
  Segment part 39. ROOT.      Intra module refs:   halBoardInit
                                                   halDigioConfig
                                                   halLcdInit
           ENTRY                   ADDRESS         REF BY
           =====                   =======         ======
           P0DIR                   000000FD 
    -------------------------------------------------------------------------
SFR_AN
  Relative segment, address: DATA 000000FE - 000000FE (0x1 bytes), align: 0
  Segment part 40. ROOT.      Intra module refs:   halBoardInit
                                                   halDigioConfig
                                                   halLcdInit
           ENTRY                   ADDRESS         REF BY
           =====                   =======         ======
           P1DIR                   000000FE 
    -------------------------------------------------------------------------
SFR_AN
  Relative segment, address: DATA 000000FF - 000000FF (0x1 bytes), align: 0
  Segment part 41. ROOT.      Intra module refs:   appConfigIO
                                                   halBoardInit
                                                   halDigioConfig
           ENTRY                   ADDRESS         REF BY
           =====                   =======         ======
           P2DIR                   000000FF 
    -------------------------------------------------------------------------
NEAR_CODE
  Relative segment, address: CODE 00000C46 - 00000C88 (0x43 bytes), align: 0
  Segment part 138.           Intra module refs:   halJoystickGetDir
           ENTRY                   ADDRESS         REF BY
           =====                   =======         ======
           adcSampleSingle         00000C46 
               XSTACK = 00000009 ( ******** )
               ISTACK = ******** ( 00000002 )
    -------------------------------------------------------------------------
NEAR_CODE
  Relative segment, address: CODE 00000C89 - 00000CBC (0x34 bytes), align: 0
  Segment part 139.           Intra module refs:   halBoardInit
           ENTRY                   ADDRESS         REF BY
           =====                   =======         ======
           clockSetMainSrc         00000C89 
    -------------------------------------------------------------------------
NEAR_CODE
  Relative segment, address: CODE 00000CBD - 00000CE2 (0x26 bytes), align: 0
  Segment part 140.           Intra module refs:   halBoardInit
           ENTRY                   ADDRESS         REF BY
           =====                   =======         ======
           clockSelect32k          00000CBD 
    -------------------------------------------------------------------------
NEAR_CODE
  Relative segment, address: CODE 00000CE3 - 00000D13 (0x31 bytes), align: 0
  Segment part 141.           Intra module refs:   halMcuWaitMs
                                                   main
           ENTRY                   ADDRESS         REF BY
           =====                   =======         ======
           halMcuWaitUs            00000CE3 
               XSTACK = ******** ( ******** )
    -------------------------------------------------------------------------
NEAR_CODE
  Relative segment, address: CODE 00000D14 - 00000D40 (0x2d bytes), align: 0
  Segment part 142.           Intra module refs:   halAssertHandler
                                                   main
           ENTRY                   ADDRESS         REF BY
           =====                   =======         ======
           halMcuWaitMs            00000D14 
               calls direct
               XSTACK = ******** ( ******** )
    -------------------------------------------------------------------------
XDATA_Z
  Relative segment, address: XDATA 00000101 - 00000102 (0x2 bytes), align: 0
  Segment part 115.           Intra module refs:   rfIsr
           LOCAL                   ADDRESS         
           =====                   =======         
           pfISR                   00000101 
    -------------------------------------------------------------------------
XDATA_I
  Relative segment, address: XDATA 000001E8 - 000001ED (0x6 bytes), align: 0
  Segment part 51.            Intra module refs:   Segment part 133
    -------------------------------------------------------------------------
XDATA_I
  Relative segment, address: XDATA 000001EE - 000001F3 (0x6 bytes), align: 0
  Segment part 53.            Intra module refs:   Segment part 133
    -------------------------------------------------------------------------
XDATA_I
  Relative segment, address: XDATA 000001F4 - 000001F9 (0x6 bytes), align: 0
  Segment part 55.            Intra module refs:   Segment part 133
    -------------------------------------------------------------------------
XDATA_I
  Relative segment, address: XDATA 000001FA - 000001FF (0x6 bytes), align: 0
  Segment part 57.            Intra module refs:   Segment part 133
    -------------------------------------------------------------------------
XDATA_I
  Relative segment, address: XDATA 00000200 - 00000205 (0x6 bytes), align: 0
  Segment part 59.            Intra module refs:   Segment part 133
    -------------------------------------------------------------------------
XDATA_I
  Relative segment, address: XDATA 00000206 - 0000020B (0x6 bytes), align: 0
  Segment part 61.            Intra module refs:   Segment part 133
    -------------------------------------------------------------------------
XDATA_I
  Relative segment, address: XDATA 0000020C - 00000211 (0x6 bytes), align: 0
  Segment part 63.            Intra module refs:   Segment part 133
    -------------------------------------------------------------------------
XDATA_I
  Relative segment, address: XDATA 00000212 - 00000217 (0x6 bytes), align: 0
  Segment part 65.            Intra module refs:   Segment part 133
    -------------------------------------------------------------------------
XDATA_I
  Relative segment, address: XDATA 00000218 - 0000021D (0x6 bytes), align: 0
  Segment part 67.            Intra module refs:   Segment part 133
    -------------------------------------------------------------------------
XDATA_I
  Relative segment, address: XDATA 0000021E - 00000223 (0x6 bytes), align: 0
  Segment part 69.            Intra module refs:   Segment part 133
    -------------------------------------------------------------------------
XDATA_I
  Relative segment, address: XDATA 00000224 - 00000229 (0x6 bytes), align: 0
  Segment part 71.            Intra module refs:   Segment part 133
    -------------------------------------------------------------------------
XDATA_I
  Relative segment, address: XDATA 0000022A - 0000022F (0x6 bytes), align: 0
  Segment part 73.            Intra module refs:   Segment part 133
    -------------------------------------------------------------------------
XDATA_I
  Relative segment, address: XDATA 00000230 - 00000235 (0x6 bytes), align: 0
  Segment part 75.            Intra module refs:   Segment part 133
    -------------------------------------------------------------------------
XDATA_I
  Relative segment, address: XDATA 00000236 - 0000023B (0x6 bytes), align: 0
  Segment part 77.            Intra module refs:   Segment part 133
    -------------------------------------------------------------------------
XDATA_I
  Relative segment, address: XDATA 0000023C - 00000241 (0x6 bytes), align: 0
  Segment part 79.            Intra module refs:   Segment part 133
    -------------------------------------------------------------------------
XDATA_I
  Relative segment, address: XDATA 00000242 - 00000247 (0x6 bytes), align: 0
  Segment part 81.            Intra module refs:   Segment part 133
    -------------------------------------------------------------------------
XDATA_I
  Relative segment, address: XDATA 00000248 - 0000024C (0x5 bytes), align: 0
  Segment part 83.            Intra module refs:   utilPrintLogo
    -------------------------------------------------------------------------
XDATA_I
  Relative segment, address: XDATA 0000024D - 00000251 (0x5 bytes), align: 0
  Segment part 85.            Intra module refs:   utilPrintLogo
    -------------------------------------------------------------------------
XDATA_I
  Relative segment, address: XDATA 00000252 - 00000256 (0x5 bytes), align: 0
  Segment part 87.            Intra module refs:   utilPrintLogo
    -------------------------------------------------------------------------
XDATA_I
  Relative segment, address: XDATA 00000257 - 0000025B (0x5 bytes), align: 0
  Segment part 89.            Intra module refs:   utilPrintLogo
    -------------------------------------------------------------------------
XDATA_I
  Relative segment, address: XDATA 0000025C - 00000260 (0x5 bytes), align: 0
  Segment part 91.            Intra module refs:   utilPrintLogo
    -------------------------------------------------------------------------
XDATA_I
  Relative segment, address: XDATA 00000261 - 00000265 (0x5 bytes), align: 0
  Segment part 93.            Intra module refs:   utilPrintLogo
    -------------------------------------------------------------------------
XDATA_I
  Relative segment, address: XDATA 00000266 - 0000026A (0x5 bytes), align: 0
  Segment part 95.            Intra module refs:   utilPrintLogo
    -------------------------------------------------------------------------
XDATA_I
  Relative segment, address: XDATA 0000026B - 0000026F (0x5 bytes), align: 0
  Segment part 97.            Intra module refs:   utilPrintLogo
    -------------------------------------------------------------------------
XDATA_I
  Relative segment, address: XDATA 00000270 - 00000274 (0x5 bytes), align: 0
  Segment part 99.            Intra module refs:   utilPrintLogo
    -------------------------------------------------------------------------
XDATA_I
  Relative segment, address: XDATA 00000275 - 00000279 (0x5 bytes), align: 0
  Segment part 101.           Intra module refs:   utilPrintLogo
    -------------------------------------------------------------------------
XDATA_I
  Relative segment, address: XDATA 0000027A - 0000027E (0x5 bytes), align: 0
  Segment part 103.           Intra module refs:   utilPrintLogo
    -------------------------------------------------------------------------
XDATA_I
  Relative segment, address: XDATA 0000027F - 0000028C (0xe bytes), align: 0
  Segment part 105.           Intra module refs:   utilPrintLogo
    -------------------------------------------------------------------------
XDATA_I
  Relative segment, address: XDATA 0000028D - 000002AA (0x1e bytes), align: 0
  Segment part 107.           Intra module refs:   utilPrintLogo
    -------------------------------------------------------------------------
XDATA_I
  Relative segment, address: XDATA 000002AB - 000002B4 (0xa bytes), align: 0
  Segment part 109.           Intra module refs:   utilPrintLogo
    -------------------------------------------------------------------------
XDATA_I
  Relative segment, address: XDATA 000002B5 - 000002C1 (0xd bytes), align: 0
  Segment part 111.           Intra module refs:   main
    -------------------------------------------------------------------------
XDATA_I
  Relative segment, address: XDATA 000002C2 - 000002C6 (0x5 bytes), align: 0
  Segment part 113.           Intra module refs:   main
    -------------------------------------------------------------------------
XDATA_I
  Relative segment, address: XDATA 000002C7 - 000002C7 (0x1 bytes), align: 0
  Segment part 116.           Intra module refs:   main
           LOCAL                   ADDRESS         
           =====                   =======         
           rssiOffset              000002C7 
    -------------------------------------------------------------------------
NEAR_CODE
  Relative segment, address: CODE 00000D41 - 00000D78 (0x38 bytes), align: 0
  Segment part 143.           Intra module refs:   rfIsr::??INTVEC 131
           ENTRY                   ADDRESS         REF BY
           =====                   =======         ======
           rfIsr                   00000D41 
               interrupt function
               ISTACK = ******** ( 0000000F )
    -------------------------------------------------------------------------
CODE_C
  Relative segment, address: CODE ******** - 00000C45 (0x600 bytes), align: 0
  Segment part 118.           Intra module refs:   ?Subroutine10
           ENTRY                   ADDRESS         REF BY
           =====                   =======         ======
           ASCIITAB                ******** 
    -------------------------------------------------------------------------
XDATA_Z
  Relative segment, address: XDATA 00000103 - 00000112 (0x10 bytes), align: 0
  Segment part 119.           Intra module refs:   ?Subroutine7
                                                   halLcdWriteLine
           LOCAL                   ADDRESS         
           =====                   =======         
           pLcdLineBuffer          00000103 
    -------------------------------------------------------------------------
NEAR_CODE
  Relative segment, address: CODE 00000D79 - 00000D84 (0xc bytes), align: 0
  Segment part 144.           Intra module refs:   SET_DDRAM_ADDR
                                                   halLcdInit
           LOCAL                   ADDRESS         
           =====                   =======         
           HalLcd_HW_Control       00000D79 
               XSTACK = ******** ( ******** )
    -------------------------------------------------------------------------
NEAR_CODE
  Relative segment, address: CODE 00000D85 - 00000D8B (0x7 bytes), align: 0
  Segment part 145.           Intra module refs:   HalLcd_HW_Control
                                                   HalLcd_HW_Write
           LOCAL                   ADDRESS         
           =====                   =======         
           ?Subroutine12           00000D85 
    -------------------------------------------------------------------------
NEAR_CODE
  Relative segment, address: CODE 00000D8C - 00000D99 (0xe bytes), align: 0
  Segment part 146.           Intra module refs:   ?Subroutine10
                                                   halLcdInit
           LOCAL                   ADDRESS         
           =====                   =======         
           HalLcd_HW_Write         00000D8C 
               XSTACK = ******** ( ******** )
    -------------------------------------------------------------------------
NEAR_CODE
  Relative segment, address: CODE 00000D9A - 00000E4F (0xb6 bytes), align: 0
  Segment part 147.           Intra module refs:   halLcdWriteLine
           LOCAL                   ADDRESS         
           =====                   =======         
           lcdWriteLine            00000D9A 
               calls direct
               XSTACK = 00000009 ( ******** )
    -------------------------------------------------------------------------
NEAR_CODE
  Relative segment, address: CODE 00000E50 - 00000E57 (0x8 bytes), align: 0
  Segment part 148.           Intra module refs:   convInt32ToText
                                                   lcdWriteLine
           LOCAL                   ADDRESS         
           =====                   =======         
           ?Subroutine14           00000E50 
    -------------------------------------------------------------------------
NEAR_CODE
  Relative segment, address: CODE 00000E58 - 00000E62 (0xb bytes), align: 0
  Segment part 149.           Intra module refs:   lcdWriteLine
           LOCAL                   ADDRESS         
           =====                   =======         
           ?Subroutine2            00000E58 
    -------------------------------------------------------------------------
NEAR_CODE
  Relative segment, address: CODE 00000E63 - 00000E6D (0xb bytes), align: 0
  Segment part 150.           Intra module refs:   ?Subroutine2
                                                   halLcdWriteChar
    -------------------------------------------------------------------------
NEAR_CODE
  Relative segment, address: CODE 00000E6E - 00000E8E (0x21 bytes), align: 0
  Segment part 151.           Intra module refs:   halLcdInit
                                                   halLcdWriteChar
                                                   lcdWriteLine
           LOCAL                   ADDRESS         
           =====                   =======         
           SET_DDRAM_ADDR          00000E6E 
               calls direct
               XSTACK = ******** ( ******** )
               ISTACK = ******** ( ******** )
    -------------------------------------------------------------------------
NEAR_CODE
  Relative segment, address: CODE 00000E8F - 00000F05 (0x77 bytes), align: 0
  Segment part 152.           Intra module refs:   halBoardInit
           ENTRY                   ADDRESS         REF BY
           =====                   =======         ======
           halLcdInit              00000E8F 
               calls direct
               XSTACK = ******** ( ******** )
    -------------------------------------------------------------------------
NEAR_CODE
  Relative segment, address: CODE 00000F06 - 00000F0A (0x5 bytes), align: 0
  Segment part 153.           Intra module refs:   appConfigIO
                                                   halLcdInit
           LOCAL                   ADDRESS         
           =====                   =======         
           ?Subroutine15           00000F06 
    -------------------------------------------------------------------------
NEAR_CODE
  Relative segment, address: CODE 00000F0B - 00000F93 (0x89 bytes), align: 0
  Segment part 154.           Intra module refs:   main
           ENTRY                   ADDRESS         REF BY
           =====                   =======         ======
           halLcdWriteChar         00000F0B 
               calls direct
               XSTACK = ******** ( 0000000D )
    -------------------------------------------------------------------------
NEAR_CODE
  Relative segment, address: CODE 00000F94 - 00000FA5 (0x12 bytes), align: 0
  Segment part 155.           Intra module refs:   Segment part 150
                                                   halLcdWriteChar
           LOCAL                   ADDRESS         
           =====                   =======         
           ?Subroutine10           00000F94 
    -------------------------------------------------------------------------
NEAR_CODE
  Relative segment, address: CODE 00000FA6 - 00000FA9 (0x4 bytes), align: 0
  Segment part 156.           Intra module refs:   halLcdWriteChar
                                                   lcdWriteLine
           LOCAL                   ADDRESS         
           =====                   =======         
           ?Subroutine3            00000FA6 
    -------------------------------------------------------------------------
NEAR_CODE
  Relative segment, address: CODE 00000FAA - 00000FB2 (0x9 bytes), align: 0
  Segment part 157.           Intra module refs:   ?Subroutine3
                                                   halLcdWriteChar
                                                   lcdWriteLine
    -------------------------------------------------------------------------
NEAR_CODE
  Relative segment, address: CODE 00000FB3 - 00000FED (0x3b bytes), align: 0
  Segment part 158.           Intra module refs:   utilLcdDisplayValue
                                                   utilPrintLogo
           ENTRY                   ADDRESS         REF BY
           =====                   =======         ======
           halLcdWriteLine         00000FB3 
               calls direct
               XSTACK = 00000037 ( 00000009 )
               ISTACK = ******** ( ******** )
    -------------------------------------------------------------------------
NEAR_CODE
  Relative segment, address: CODE 00000FEE - 00000FF2 (0x5 bytes), align: 0
  Segment part 159.           Intra module refs:   halDigioConfig
                                                   halJoystickGetDir
                                                   halLcdWriteLine
           LOCAL                   ADDRESS         
           =====                   =======         
           ?Subroutine16           00000FEE 
    -------------------------------------------------------------------------
NEAR_CODE
  Relative segment, address: CODE 00000FF3 - 00001000 (0xe bytes), align: 0
  Segment part 160.           Intra module refs:   halLcdWriteLine
           LOCAL                   ADDRESS         
           =====                   =======         
           ?Subroutine7            00000FF3 
    -------------------------------------------------------------------------
NEAR_CODE
  Relative segment, address: CODE 00001001 - 0000101D (0x1d bytes), align: 0
  Segment part 161.           Intra module refs:   halBoardInit
           ENTRY                   ADDRESS         REF BY
           =====                   =======         ======
           halAssertHandler        00001001 
               calls direct
    -------------------------------------------------------------------------
NEAR_CODE
  Relative segment, address: CODE 0000101E - 00001084 (0x67 bytes), align: 0
  Segment part 162.           Intra module refs:   main
           ENTRY                   ADDRESS         REF BY
           =====                   =======         ======
           halBoardInit            0000101E 
               calls direct
    -------------------------------------------------------------------------
XDATA_Z
  Relative segment, address: XDATA 00000113 - 00000122 (0x10 bytes), align: 0
  Segment part 120.           Intra module refs:   port0_ISR
           LOCAL                   ADDRESS         
           =====                   =======         
           port0_isr_tbl           00000113 
    -------------------------------------------------------------------------
XDATA_Z
  Relative segment, address: XDATA 00000123 - 00000132 (0x10 bytes), align: 0
  Segment part 121.           Intra module refs:   port1_ISR
           LOCAL                   ADDRESS         
           =====                   =======         
           port1_isr_tbl           00000123 
    -------------------------------------------------------------------------
XDATA_Z
  Relative segment, address: XDATA 00000133 - 0000013C (0xa bytes), align: 0
  Segment part 122.           Intra module refs:   appConfigIO
                                                   port2_ISR
           LOCAL                   ADDRESS         
           =====                   =======         
           port2_isr_tbl           00000133 
    -------------------------------------------------------------------------
NEAR_CODE
  Relative segment, address: CODE 00001085 - 00001168 (0xe4 bytes), align: 0
  Segment part 163.           Intra module refs:   appConfigIO
           ENTRY                   ADDRESS         REF BY
           =====                   =======         ======
           halDigioConfig          00001085 
               XSTACK = ******** ( 00000009 )
    -------------------------------------------------------------------------
NEAR_CODE
  Relative segment, address: CODE 00001169 - 000011AF (0x47 bytes), align: 0
  Segment part 164.           Intra module refs:   appConfigIO
           ENTRY                   ADDRESS         REF BY
           =====                   =======         ======
           halDigioIntClear        00001169 
               XSTACK = ******** ( ******** )
               ISTACK = ******** ( 00000002 )
    -------------------------------------------------------------------------
NEAR_CODE
  Relative segment, address: CODE 000011B0 - 00001204 (0x55 bytes), align: 0
  Segment part 165.           Intra module refs:   port0_ISR::??INTVEC 107
           ENTRY                   ADDRESS         REF BY
           =====                   =======         ======
           port0_ISR               000011B0 
               interrupt function
               ISTACK = ******** ( ******** )
    -------------------------------------------------------------------------
NEAR_CODE
  Relative segment, address: CODE 00001205 - 00001209 (0x5 bytes), align: 0
  Segment part 166.           Intra module refs:   port0_ISR
                                                   port1_ISR
                                                   port2_ISR
           LOCAL                   ADDRESS         
           =====                   =======         
           ?Subroutine17           00001205 
    -------------------------------------------------------------------------
NEAR_CODE
  Relative segment, address: CODE 0000120A - 0000120B (0x2 bytes), align: 0
  Segment part 167.           Intra module refs:   port0_ISR
                                                   port1_ISR
                                                   port2_ISR
           LOCAL                   ADDRESS         
           =====                   =======         
           ?Subroutine0            0000120A 
    -------------------------------------------------------------------------
NEAR_CODE
  Relative segment, address: CODE 0000120C - 00001214 (0x9 bytes), align: 0
  Segment part 168.           Intra module refs:   ?Subroutine0
                                                   halJoystickMoveISR
                                                   lcdWriteLine
                                                   rfIsr
    -------------------------------------------------------------------------
NEAR_CODE
  Relative segment, address: CODE 00001215 - 0000126B (0x57 bytes), align: 0
  Segment part 169.           Intra module refs:   port1_ISR::??INTVEC 123
           ENTRY                   ADDRESS         REF BY
           =====                   =======         ======
           port1_ISR               00001215 
               interrupt function
               ISTACK = ******** ( ******** )
    -------------------------------------------------------------------------
NEAR_CODE
  Relative segment, address: CODE 0000126C - 000012C3 (0x58 bytes), align: 0
  Segment part 170.           Intra module refs:   port2_ISR::??INTVEC 51
           ENTRY                   ADDRESS         REF BY
           =====                   =======         ======
           port2_ISR               0000126C 
               interrupt function
               ISTACK = ******** ( ******** )
    -------------------------------------------------------------------------
XDATA_I
  Relative segment, address: XDATA 000002C8 - 000002CC (0x5 bytes), align: 0
  Segment part 123.           Intra module refs:   appConfigIO
           LOCAL                   ADDRESS         
           =====                   =======         
           pinJoystickMove         000002C8 
    -------------------------------------------------------------------------
XDATA_Z
  Relative segment, address: XDATA 0000013D - 00000148 (0xc bytes), align: 0
  Segment part 125.           Intra module refs:   halJoystickIntConnect
                                                   halJoystickMoveISR
           LOCAL                   ADDRESS         
           =====                   =======         
           joystick_isr_tbl        0000013D 
    -------------------------------------------------------------------------
NEAR_CODE
  Relative segment, address: CODE 000012C4 - 00001312 (0x4f bytes), align: 0
  Segment part 171.           Intra module refs:   appConfigIO
           ENTRY                   ADDRESS         REF BY
           =====                   =======         ======
           halJoystickIntConnect   000012C4 
               XSTACK = ******** ( ******** )
               ISTACK = ******** ( 00000002 )
    -------------------------------------------------------------------------
NEAR_CODE
  Relative segment, address: CODE 00001313 - 0000131B (0x9 bytes), align: 0
  Segment part 172.           Intra module refs:   appConfigIO
           ENTRY                   ADDRESS         REF BY
           =====                   =======         ======
           halJoystickIntEnable    00001313 
               XSTACK = ******** ( ******** )
    -------------------------------------------------------------------------
NEAR_CODE
  Relative segment, address: CODE 0000131C - 0000136D (0x52 bytes), align: 0
  Segment part 173.           Intra module refs:   halJoystickMoveISR
           ENTRY                   ADDRESS         REF BY
           =====                   =======         ======
           halJoystickGetDir       0000131C 
               calls direct
               XSTACK = ******** ( 00000009 )
    -------------------------------------------------------------------------
NEAR_CODE
  Relative segment, address: CODE 0000136E - 000013D8 (0x6b bytes), align: 0
  Segment part 174.           Intra module refs:   appConfigIO
           LOCAL                   ADDRESS         
           =====                   =======         
           halJoystickMoveISR      0000136E 
               calls direct, is indirectly called
               ISTACK = ******** ( 00000002 )
    -------------------------------------------------------------------------
XDATA_Z
  Relative segment, address: XDATA 00000149 - 00000149 (0x1 bytes), align: 0
  Segment part 126.           Intra module refs:   halLedToggle
           ENTRY                   ADDRESS         REF BY
           =====                   =======         ======
           led4State               00000149 
    -------------------------------------------------------------------------
NEAR_CODE
  Relative segment, address: CODE 000013D9 - 0000140E (0x36 bytes), align: 0
  Segment part 175.           Intra module refs:   halAssertHandler
           ENTRY                   ADDRESS         REF BY
           =====                   =======         ======
           halLedToggle            000013D9 
               ISTACK = ******** ( 00000002 )
    -------------------------------------------------------------------------
NEAR_CODE
  Relative segment, address: CODE 0000140F - 0000151F (0x111 bytes), align: 0
  Segment part 176.           Intra module refs:   utilLcdDisplayValue
           ENTRY                   ADDRESS         REF BY
           =====                   =======         ======
           convInt32ToText         0000140F 
               XSTACK = 0000000E ( 00000014 )
               ISTACK = ******** ( 00000002 )
    -------------------------------------------------------------------------
NEAR_CODE
  Relative segment, address: CODE 00001520 - 0000152D (0xe bytes), align: 0
  Segment part 177.           Intra module refs:   convInt32ToText
           LOCAL                   ADDRESS         
           =====                   =======         
           ?Subroutine4            00001520 
    -------------------------------------------------------------------------
XDATA_Z
  Relative segment, address: XDATA 0000014A - 00000155 (0xc bytes), align: 0
  Segment part 127.           Intra module refs:   convInt32ToText
           LOCAL                   ADDRESS         
           =====                   =======         
           convInt32ToText::pValueToTextBuffer
                                   0000014A 
    -------------------------------------------------------------------------
XDATA_Z
  Relative segment, address: XDATA 00000156 - 00000165 (0x10 bytes), align: 0
  Segment part 128.           Intra module refs:   ?Subroutine8
                                                   utilLcdDisplayValue
           LOCAL                   ADDRESS         
           =====                   =======         
           pLcdLineBuffer          00000156 
    -------------------------------------------------------------------------
NEAR_CODE
  Relative segment, address: CODE 0000152E - 00001661 (0x134 bytes), align: 0
  Segment part 178.           Intra module refs:   main
           ENTRY                   ADDRESS         REF BY
           =====                   =======         ======
           utilPrintLogo           0000152E 
               calls direct
               XSTACK = ******** ( 00000039 )
               ISTACK = ******** ( 00000002 )
    -------------------------------------------------------------------------
NEAR_CODE
  Relative segment, address: CODE 00001662 - 0000170C (0xab bytes), align: 0
  Segment part 179.           Intra module refs:   main
           ENTRY                   ADDRESS         REF BY
           =====                   =======         ======
           utilLcdDisplayValue     00001662 
               calls direct
               XSTACK = 00000004 ( 0000000E )
               ISTACK = ******** ( ******** )
    -------------------------------------------------------------------------
NEAR_CODE
  Relative segment, address: CODE 0000170D - 0000171A (0xe bytes), align: 0
  Segment part 180.           Intra module refs:   utilLcdDisplayValue
           LOCAL                   ADDRESS         
           =====                   =======         
           ?Subroutine8            0000170D 
    -------------------------------------------------------------------------
XDATA_Z
  Relative segment, address: XDATA 00000166 - 00000166 (0x1 bytes), align: 0
  Segment part 129.           Intra module refs:   appDecChannel
                                                   appIncChannel
                                                   appSetShowText
                                                   main
           LOCAL                   ADDRESS         
           =====                   =======         
           appShowText             00000166 
    -------------------------------------------------------------------------
XDATA_Z
  Relative segment, address: XDATA 00000167 - 00000167 (0x1 bytes), align: 0
  Segment part 130.           Intra module refs:   ?Subroutine1
                                                   appDecChannel
                                                   appIncChannel
                                                   main
           LOCAL                   ADDRESS         
           =====                   =======         
           txtChannel              00000167 
    -------------------------------------------------------------------------
XDATA_Z
  Relative segment, address: XDATA 00000168 - 000001E7 (0x80 bytes), align: 0
  Segment part 131.           Intra module refs:   ?Subroutine11
                                                   main
           LOCAL                   ADDRESS         
           =====                   =======         
           ppRssi                  00000168 
    -------------------------------------------------------------------------
XDATA_I
  Relative segment, address: XDATA 000002CD - 000002EC (0x20 bytes), align: 0
  Segment part 132.           Intra module refs:   main
           LOCAL                   ADDRESS         
           =====                   =======         
           channelText             000002CD 
    -------------------------------------------------------------------------
XDATA_I
  Relative segment, address: XDATA 000002ED - 000002F0 (0x4 bytes), align: 0
  Segment part 134.           Intra module refs:   convInt32ToText
           ENTRY                   ADDRESS         REF BY
           =====                   =======         ======
           __Constant_0            000002ED 
    -------------------------------------------------------------------------
XDATA_I
  Relative segment, address: XDATA 000002F1 - 000002F4 (0x4 bytes), align: 0
  Segment part 136.           Intra module refs:   convInt32ToText
           ENTRY                   ADDRESS         REF BY
           =====                   =======         ======
           __Constant_a            000002F1 
    -------------------------------------------------------------------------
NEAR_CODE
  Relative segment, address: CODE 0000171B - 00001725 (0xb bytes), align: 0
  Segment part 181.           Intra module refs:   appConfigIO
           LOCAL                   ADDRESS         
           =====                   =======         
           appSetShowText          0000171B 
               is indirectly called
               ISTACK = ******** ( 00000002 )
    -------------------------------------------------------------------------
NEAR_CODE
  Relative segment, address: CODE 00001726 - 0000172A (0x5 bytes), align: 0
  Segment part 182.           Intra module refs:   appDecChannel
                                                   appIncChannel
                                                   appSetShowText
                                                   halDigioIntClear
                                                   halJoystickIntConnect
                                                   halJoystickMoveISR
                                                   halLedToggle
           LOCAL                   ADDRESS         
           =====                   =======         
           ?Subroutine13           00001726 
    -------------------------------------------------------------------------
NEAR_CODE
  Relative segment, address: CODE 0000172B - 00001744 (0x1a bytes), align: 0
  Segment part 183.           Intra module refs:   appConfigIO
           LOCAL                   ADDRESS         
           =====                   =======         
           appIncChannel           0000172B 
               is indirectly called
               ISTACK = ******** ( 00000002 )
    -------------------------------------------------------------------------
NEAR_CODE
  Relative segment, address: CODE 00001745 - 0000175D (0x19 bytes), align: 0
  Segment part 184.           Intra module refs:   appConfigIO
           LOCAL                   ADDRESS         
           =====                   =======         
           appDecChannel           00001745 
               is indirectly called
               ISTACK = ******** ( 00000002 )
    -------------------------------------------------------------------------
NEAR_CODE
  Relative segment, address: CODE 0000175E - 000017BA (0x5d bytes), align: 0
  Segment part 185.           Intra module refs:   main
           LOCAL                   ADDRESS         
           =====                   =======         
           appConfigIO             0000175E 
               calls direct
               XSTACK = ******** ( ******** )
    -------------------------------------------------------------------------
NEAR_CODE
  Relative segment, address: CODE 000017BB - 000017C7 (0xd bytes), align: 0
  Segment part 186.           Intra module refs:   appConfigIO
                                                   halBoardInit
           LOCAL                   ADDRESS         
           =====                   =======         
           ?Subroutine6            000017BB 
    -------------------------------------------------------------------------
NEAR_CODE
  Relative segment, address: CODE 000017C8 - 000019DD (0x216 bytes), align: 0
  Segment part 187.
           ENTRY                   ADDRESS         REF BY
           =====                   =======         ======
           main                    000017C8        ?call_main (?cmain)
               calls direct
               XSTACK = ******** ( 00000004 )
               ISTACK = ******** ( ******** )
    -------------------------------------------------------------------------
NEAR_CODE
  Relative segment, address: CODE 000019DE - 000019E1 (0x4 bytes), align: 0
  Segment part 188.           Intra module refs:   main
           LOCAL                   ADDRESS         
           =====                   =======         
           ?Subroutine9            000019DE 
    -------------------------------------------------------------------------
NEAR_CODE
  Relative segment, address: CODE 000019E2 - 000019F5 (0x14 bytes), align: 0
  Segment part 189.           Intra module refs:   ?Subroutine5
                                                   ?Subroutine9
           LOCAL                   ADDRESS         
           =====                   =======         
           ?Subroutine11           000019E2 
    -------------------------------------------------------------------------
NEAR_CODE
  Relative segment, address: CODE 000019F6 - 000019FB (0x6 bytes), align: 0
  Segment part 190.           Intra module refs:   main
           LOCAL                   ADDRESS         
           =====                   =======         
           ?Subroutine5            000019F6 
    -------------------------------------------------------------------------
NEAR_CODE
  Relative segment, address: CODE 000019FC - 00001A09 (0xe bytes), align: 0
  Segment part 191.           Intra module refs:   main
           LOCAL                   ADDRESS         
           =====                   =======         
           ?Subroutine1            000019FC 
    -------------------------------------------------------------------------
INTVEC
  Common segment, address: CODE ******** - 00000035 (0x36 bytes), align: 0
  Segment part 42. ROOT.
           ENTRY                   ADDRESS         REF BY
           =====                   =======         ======
           port2_ISR::??INTVEC 51
                                   00000033 
    -------------------------------------------------------------------------
INTVEC
  Common segment, address: CODE ******** - 0000006D (0x6e bytes), align: 0
  Segment part 43. ROOT.
           ENTRY                   ADDRESS         REF BY
           =====                   =======         ======
           port0_ISR::??INTVEC 107
                                   0000006B 
    -------------------------------------------------------------------------
INTVEC
  Common segment, address: CODE ******** - 0000007D (0x7e bytes), align: 0
  Segment part 44. ROOT.
           ENTRY                   ADDRESS         REF BY
           =====                   =======         ======
           port1_ISR::??INTVEC 123
                                   0000007B 
    -------------------------------------------------------------------------
INTVEC
  Common segment, address: CODE ******** - 00000085 (0x86 bytes), align: 0
  Segment part 45. ROOT.
           ENTRY                   ADDRESS         REF BY
           =====                   =======         ======
           rfIsr::??INTVEC 131     00000083 
    -------------------------------------------------------------------------
XDATA_ID
  Relative segment, address: CODE 000000E5 - 000000EA (0x6 bytes), align: 0
  Segment part 52.            Intra module refs:   Segment part 51
    -------------------------------------------------------------------------
XDATA_ID
  Relative segment, address: CODE 000000EB - 000000F0 (0x6 bytes), align: 0
  Segment part 54.            Intra module refs:   Segment part 53
    -------------------------------------------------------------------------
XDATA_ID
  Relative segment, address: CODE 000000F1 - 000000F6 (0x6 bytes), align: 0
  Segment part 56.            Intra module refs:   Segment part 55
    -------------------------------------------------------------------------
XDATA_ID
  Relative segment, address: CODE 000000F7 - 000000FC (0x6 bytes), align: 0
  Segment part 58.            Intra module refs:   Segment part 57
    -------------------------------------------------------------------------
XDATA_ID
  Relative segment, address: CODE 000000FD - 00000102 (0x6 bytes), align: 0
  Segment part 60.            Intra module refs:   Segment part 59
    -------------------------------------------------------------------------
XDATA_ID
  Relative segment, address: CODE 00000103 - 00000108 (0x6 bytes), align: 0
  Segment part 62.            Intra module refs:   Segment part 61
    -------------------------------------------------------------------------
XDATA_ID
  Relative segment, address: CODE 00000109 - 0000010E (0x6 bytes), align: 0
  Segment part 64.            Intra module refs:   Segment part 63
    -------------------------------------------------------------------------
XDATA_ID
  Relative segment, address: CODE 0000010F - 00000114 (0x6 bytes), align: 0
  Segment part 66.            Intra module refs:   Segment part 65
    -------------------------------------------------------------------------
XDATA_ID
  Relative segment, address: CODE 00000115 - 0000011A (0x6 bytes), align: 0
  Segment part 68.            Intra module refs:   Segment part 67
    -------------------------------------------------------------------------
XDATA_ID
  Relative segment, address: CODE 0000011B - 00000120 (0x6 bytes), align: 0
  Segment part 70.            Intra module refs:   Segment part 69
    -------------------------------------------------------------------------
XDATA_ID
  Relative segment, address: CODE 00000121 - 00000126 (0x6 bytes), align: 0
  Segment part 72.            Intra module refs:   Segment part 71
    -------------------------------------------------------------------------
XDATA_ID
  Relative segment, address: CODE 00000127 - 0000012C (0x6 bytes), align: 0
  Segment part 74.            Intra module refs:   Segment part 73
    -------------------------------------------------------------------------
XDATA_ID
  Relative segment, address: CODE 0000012D - 00000132 (0x6 bytes), align: 0
  Segment part 76.            Intra module refs:   Segment part 75
    -------------------------------------------------------------------------
XDATA_ID
  Relative segment, address: CODE 00000133 - 00000138 (0x6 bytes), align: 0
  Segment part 78.            Intra module refs:   Segment part 77
    -------------------------------------------------------------------------
XDATA_ID
  Relative segment, address: CODE 00000139 - 0000013E (0x6 bytes), align: 0
  Segment part 80.            Intra module refs:   Segment part 79
    -------------------------------------------------------------------------
XDATA_ID
  Relative segment, address: CODE 0000013F - 00000144 (0x6 bytes), align: 0
  Segment part 82.            Intra module refs:   Segment part 81
    -------------------------------------------------------------------------
XDATA_ID
  Relative segment, address: CODE 00000145 - 00000149 (0x5 bytes), align: 0
  Segment part 84.            Intra module refs:   Segment part 83
    -------------------------------------------------------------------------
XDATA_ID
  Relative segment, address: CODE 0000014A - 0000014E (0x5 bytes), align: 0
  Segment part 86.            Intra module refs:   Segment part 85
    -------------------------------------------------------------------------
XDATA_ID
  Relative segment, address: CODE 0000014F - 00000153 (0x5 bytes), align: 0
  Segment part 88.            Intra module refs:   Segment part 87
    -------------------------------------------------------------------------
XDATA_ID
  Relative segment, address: CODE 00000154 - 00000158 (0x5 bytes), align: 0
  Segment part 90.            Intra module refs:   Segment part 89
    -------------------------------------------------------------------------
XDATA_ID
  Relative segment, address: CODE 00000159 - 0000015D (0x5 bytes), align: 0
  Segment part 92.            Intra module refs:   Segment part 91
    -------------------------------------------------------------------------
XDATA_ID
  Relative segment, address: CODE 0000015E - 00000162 (0x5 bytes), align: 0
  Segment part 94.            Intra module refs:   Segment part 93
    -------------------------------------------------------------------------
XDATA_ID
  Relative segment, address: CODE 00000163 - 00000167 (0x5 bytes), align: 0
  Segment part 96.            Intra module refs:   Segment part 95
    -------------------------------------------------------------------------
XDATA_ID
  Relative segment, address: CODE 00000168 - 0000016C (0x5 bytes), align: 0
  Segment part 98.            Intra module refs:   Segment part 97
    -------------------------------------------------------------------------
XDATA_ID
  Relative segment, address: CODE 0000016D - 00000171 (0x5 bytes), align: 0
  Segment part 100.           Intra module refs:   Segment part 99
    -------------------------------------------------------------------------
XDATA_ID
  Relative segment, address: CODE 00000172 - 00000176 (0x5 bytes), align: 0
  Segment part 102.           Intra module refs:   Segment part 101
    -------------------------------------------------------------------------
XDATA_ID
  Relative segment, address: CODE 00000177 - 0000017B (0x5 bytes), align: 0
  Segment part 104.           Intra module refs:   Segment part 103
    -------------------------------------------------------------------------
XDATA_ID
  Relative segment, address: CODE 0000017C - 00000189 (0xe bytes), align: 0
  Segment part 106.           Intra module refs:   Segment part 105
    -------------------------------------------------------------------------
XDATA_ID
  Relative segment, address: CODE 0000018A - 000001A7 (0x1e bytes), align: 0
  Segment part 108.           Intra module refs:   Segment part 107
    -------------------------------------------------------------------------
XDATA_ID
  Relative segment, address: CODE 000001A8 - 000001B1 (0xa bytes), align: 0
  Segment part 110.           Intra module refs:   Segment part 109
    -------------------------------------------------------------------------
XDATA_ID
  Relative segment, address: CODE 000001B2 - 000001BE (0xd bytes), align: 0
  Segment part 112.           Intra module refs:   Segment part 111
    -------------------------------------------------------------------------
XDATA_ID
  Relative segment, address: CODE 000001BF - 000001C3 (0x5 bytes), align: 0
  Segment part 114.           Intra module refs:   Segment part 113
    -------------------------------------------------------------------------
XDATA_ID
  Relative segment, address: CODE 000001C4 - 000001C4 (0x1 bytes), align: 0
  Segment part 117.           Intra module refs:   rssiOffset
    -------------------------------------------------------------------------
XDATA_ID
  Relative segment, address: CODE 000001C5 - 000001C9 (0x5 bytes), align: 0
  Segment part 124.           Intra module refs:   pinJoystickMove
    -------------------------------------------------------------------------
XDATA_ID
  Relative segment, address: CODE 000001CA - 000001E9 (0x20 bytes), align: 0
  Segment part 133.           Intra module refs:   channelText
    -------------------------------------------------------------------------
XDATA_ID
  Relative segment, address: CODE 000001EA - 000001ED (0x4 bytes), align: 0
  Segment part 135.           Intra module refs:   __Constant_0
    -------------------------------------------------------------------------
XDATA_ID
  Relative segment, address: CODE 000001EE - 000001F1 (0x4 bytes), align: 0
  Segment part 137.           Intra module refs:   __Constant_a

    *************************************************************************

  FILE NAME : C:\Texas Instruments\CC2530 BasicRF\ide\srf05_cc2530\iar\temp\spectrum_analyzer\hal_joystick.r51
    *************************************************************************

  FILE NAME : C:\Texas Instruments\CC2530 BasicRF\ide\srf05_cc2530\iar\temp\spectrum_analyzer\hal_lcd_srf05.r51
    *************************************************************************

  FILE NAME : C:\Texas Instruments\CC2530 BasicRF\ide\srf05_cc2530\iar\temp\spectrum_analyzer\hal_led.r51
    *************************************************************************

  FILE NAME : C:\Texas Instruments\CC2530 BasicRF\ide\srf05_cc2530\iar\temp\spectrum_analyzer\hal_mcu.r51
    *************************************************************************

  FILE NAME : C:\Texas Instruments\CC2530 BasicRF\ide\srf05_cc2530\iar\temp\spectrum_analyzer\hal_rf.r51
    *************************************************************************

  FILE NAME : C:\Texas Instruments\CC2530 BasicRF\ide\srf05_cc2530\iar\temp\spectrum_analyzer\hal_rf_util.r51
    *************************************************************************

  FILE NAME : C:\Texas Instruments\CC2530 BasicRF\ide\srf05_cc2530\iar\temp\spectrum_analyzer\spectrum_analyzer.r51
    *************************************************************************

  FILE NAME : C:\Texas Instruments\CC2530 BasicRF\ide\srf05_cc2530\iar\temp\spectrum_analyzer\util.r51
    *************************************************************************

  FILE NAME : C:\Texas Instruments\CC2530 BasicRF\ide\srf05_cc2530\iar\temp\spectrum_analyzer\util_lcd.r51
    *************************************************************************

  FILE NAME : C:\Program Files\IAR Systems\Embedded Workbench 5.3\8051\LIB\CLIB\cl-pli-nlxd-1e16x01.r51
  LIBRARY MODULE, NAME : ?cexit

  SEGMENTS IN THE MODULE
  ======================
CSTART
  Relative segment, address: CODE 00000086 - 0000008A (0x5 bytes), align: 0
  Segment part 0.
           ENTRY                   ADDRESS         REF BY
           =====                   =======         ======
           exit                    00000086        ?call_main (?cmain)
           ?C_EXIT                 00000086 
           ?ROM_MONITOR_NOPS       00000086 

    -------------------------------------------------------------------------
  LIBRARY MODULE, NAME : ?cmain

  SEGMENTS IN THE MODULE
  ======================
CSTART
  Relative segment, address: CODE 0000008B, align: 0
  Segment part 0.
           ENTRY                   ADDRESS         REF BY
           =====                   =======         ======
           ?cmain                  0000008B        Segment part 12 (CSTARTUP)
    -------------------------------------------------------------------------
CSTART
  Relative segment, address: CODE 0000008B - ******** (0x9 bytes), align: 0
  Segment part 1.
           ENTRY                   ADDRESS         REF BY
           =====                   =======         ======
           __low_level_init_call   0000008B        __low_level_init (?low_level_init)
    -------------------------------------------------------------------------
CSTART
  Relative segment, address: CODE 00000094 - 000000A3 (0x10 bytes), align: 0
  Segment part 11.
           ENTRY                   ADDRESS         REF BY
           =====                   =======         ======
           __INIT_XDATA_Z          00000094        appShowText (hal_int)
                                                   convInt32ToText::pValueToTextBuffer (hal_int)
                                                   joystick_isr_tbl (hal_int)
                                                   led4State (hal_int)
                                                   pLcdLineBuffer (hal_int)
                                                   pfISR (hal_int)
                                                   port0_isr_tbl (hal_int)
                                                   port1_isr_tbl (hal_int)
                                                   port2_isr_tbl (hal_int)
                                                   ppRssi (hal_int)
                                                   txtChannel (hal_int)
    -------------------------------------------------------------------------
CSTART
  Relative segment, address: CODE 000000A4 - 000000CC (0x29 bytes), align: 0
  Segment part 28.
           ENTRY                   ADDRESS         REF BY
           =====                   =======         ======
           __INIT_XDATA_I          000000A4        Segment part 101 (hal_int)
                                                   Segment part 103 (hal_int)
                                                   Segment part 105 (hal_int)
                                                   Segment part 107 (hal_int)
                                                   Segment part 109 (hal_int)
                                                   Segment part 111 (hal_int)
                                                   Segment part 113 (hal_int)
                                                   Segment part 51 (hal_int)
                                                   Segment part 53 (hal_int)
                                                   Segment part 55 (hal_int)
                                                   Segment part 57 (hal_int)
                                                   Segment part 59 (hal_int)
                                                   Segment part 61 (hal_int)
                                                   Segment part 63 (hal_int)
                                                   Segment part 65 (hal_int)
                                                   Segment part 67 (hal_int)
                                                   Segment part 69 (hal_int)
                                                   Segment part 71 (hal_int)
                                                   Segment part 73 (hal_int)
                                                   Segment part 75 (hal_int)
                                                   Segment part 77 (hal_int)
                                                   Segment part 79 (hal_int)
                                                   Segment part 81 (hal_int)
                                                   Segment part 83 (hal_int)
                                                   Segment part 85 (hal_int)
                                                   Segment part 87 (hal_int)
                                                   Segment part 89 (hal_int)
                                                   Segment part 91 (hal_int)
                                                   Segment part 93 (hal_int)
                                                   Segment part 95 (hal_int)
                                                   Segment part 97 (hal_int)
                                                   Segment part 99 (hal_int)
                                                   __Constant_0 (hal_int)
                                                   __Constant_a (hal_int)
                                                   channelText (hal_int)
                                                   pinJoystickMove (hal_int)
                                                   rssiOffset (hal_int)
    -------------------------------------------------------------------------
CSTART
  Relative segment, address: CODE 000000CD, align: 0
  Segment part 32.            Intra module refs:   __low_level_init_call
    -------------------------------------------------------------------------
CSTART
  Relative segment, address: CODE 000000CD - 000000D2 (0x6 bytes), align: 0
  Segment part 33.            Intra module refs:   ?cmain
           ENTRY                   ADDRESS         REF BY
           =====                   =======         ======
           ?call_main              000000CD 

    -------------------------------------------------------------------------
  LIBRARY MODULE, NAME : CSTARTUP

  SEGMENTS IN THE MODULE
  ======================
REGISTERS
  Relative segment, address: DATA ********, align: 0
  Segment part 0.             Intra module refs:   Segment part 6
           ENTRY                   ADDRESS         REF BY
           =====                   =======         ======
           ?REGISTERS              ******** 
    -------------------------------------------------------------------------
ISTACK
  Relative segment, address: IDATA 000000C0, align: 0
  Segment part 1.             Intra module refs:   Segment part 6
           ENTRY                   ADDRESS         REF BY
           =====                   =======         ======
           ?ISTACK_START           000000C0 
    -------------------------------------------------------------------------
XSTACK
  Relative segment, address: XDATA ********, align: 0
  Segment part 3.             Intra module refs:   ?RESET_XSP
           ENTRY                   ADDRESS         REF BY
           =====                   =======         ======
           ?XSTACK_START           ******** 
    -------------------------------------------------------------------------
INTVEC
  Common segment, address: CODE ******** - 00000002 (0x3 bytes), align: 0
  Segment part 5. ROOT.       Intra module refs:   Segment part 6
    -------------------------------------------------------------------------
CSTART
  Relative segment, address: CODE 000000D3 - 000000D8 (0x6 bytes), align: 0
  Segment part 6. ROOT.       Intra module refs:   Segment part 5
           ENTRY                   ADDRESS         REF BY
           =====                   =======         ======
           __program_start         000000D3        Absolute parts (?ABS_ENTRY_MOD)
           ?RESET_SP               000000D6 
    -------------------------------------------------------------------------
CSTART
  Relative segment, address: CODE 000000D9 - 000000DE (0x6 bytes), align: 0
  Segment part 8.
           ENTRY                   ADDRESS         REF BY
           =====                   =======         ======
           ?RESET_XSP              000000D9        ?XSP (VIRTUAL_REGISTERS)
    -------------------------------------------------------------------------
CSTART
  Relative segment, address: CODE 000000DF - 000000E1 (0x3 bytes), align: 0
  Segment part 12.            Intra module refs:   Absolute parts

    -------------------------------------------------------------------------
  LIBRARY MODULE, NAME : VIRTUAL_REGISTERS

  SEGMENTS IN THE MODULE
  ======================
BREG
  Relative segment, address: BIT ********.0 - ********.7 (0x8 bits), align: 0
  Segment part 0.
           ENTRY                   ADDRESS         REF BY
           =====                   =======         ======
           ?B0                     ********.0      Absolute parts (CSTARTUP)
    -------------------------------------------------------------------------
VREG
  Relative segment, address: DATA ********, align: 0
  Segment part 1.
           ENTRY                   ADDRESS         REF BY
           =====                   =======         ======
           ?V0                     ********        ?FUNC_ENTER_XDATA (?FUNC_ENTER_XDATA)
                                                   ?FUNC_LEAVE_XDATA (?FUNC_LEAVE_XDATA)
                                                   ?INTERRUPT_ENTER_XSP (?INTERRUPT_ENTER_XSP)
                                                   ?INTERRUPT_LEAVE_XSP (?INTERRUPT_LEAVE_XSP)
                                                   ?Subroutine1 (hal_int)
                                                   ?Subroutine11 (hal_int)
                                                   ?Subroutine2 (hal_int)
                                                   ?Subroutine3 (hal_int)
                                                   ?Subroutine7 (hal_int)
                                                   ?Subroutine8 (hal_int)
                                                   Segment part 150 (hal_int)
                                                   Segment part 157 (hal_int)
                                                   adcSampleSingle (hal_int)
                                                   convInt32ToText (hal_int)
                                                   halDigioConfig (hal_int)
                                                   halJoystickGetDir (hal_int)
                                                   halLcdWriteChar (hal_int)
                                                   lcdWriteLine (hal_int)
                                                   main (hal_int)
                                                   port0_ISR (hal_int)
                                                   port1_ISR (hal_int)
                                                   port2_ISR (hal_int)
                                                   strncpy (?strncpy)
                                                   utilLcdDisplayValue (hal_int)
                                                   utilPrintLogo (hal_int)
    -------------------------------------------------------------------------
XSP
  Relative segment, address: DATA ******** - ******** (0x2 bytes), align: 0
  Segment part 3.
           ENTRY                   ADDRESS         REF BY
           =====                   =======         ======
           ?XSP                    ********        ?ADD_XSTACK_DISP0_8 (?ADD_XSTACK_DISP8)
                                                   ?ALLOC_XSTACK8 (?ALLOC_XSTACK8)
                                                   ?DEALLOC_XSTACK8 (?DEALLOC_XSTACK8)
                                                   ?FUNC_ENTER_XDATA (?FUNC_ENTER_XDATA)
                                                   ?FUNC_LEAVE_XDATA (?FUNC_LEAVE_XDATA)
                                                   ?INTERRUPT_ENTER_XSP (?INTERRUPT_ENTER_XSP)
                                                   ?INTERRUPT_LEAVE_XSP (?INTERRUPT_LEAVE_XSP)
                                                   ?RESET_XSP (CSTARTUP)
                                                   ?XSTACK_DISP0_8 (?XSTACK_DISP8)
                                                   convInt32ToText (hal_int)
                                                   lcdWriteLine (hal_int)
                                                   utilPrintLogo (hal_int)

    -------------------------------------------------------------------------
  LIBRARY MODULE, NAME : ?L_EQ_X

  SEGMENTS IN THE MODULE
  ======================
RCODE
  Relative segment, address: CODE 000001F2 - 00000206 (0x15 bytes), align: 0
  Segment part 1.
           ENTRY                   ADDRESS         REF BY
           =====                   =======         ======
           ?L_EQ_X                 000001F2        convInt32ToText (hal_int)

    -------------------------------------------------------------------------
  LIBRARY MODULE, NAME : ?SL_GT_X

  SEGMENTS IN THE MODULE
  ======================
RCODE
  Relative segment, address: CODE 00000207 - 0000021B (0x15 bytes), align: 0
  Segment part 1.
           ENTRY                   ADDRESS         REF BY
           =====                   =======         ======
           ?SL_GT_X                00000207        convInt32ToText (hal_int)

    -------------------------------------------------------------------------
  LIBRARY MODULE, NAME : ?UL_DIV_MOD

  SEGMENTS IN THE MODULE
  ======================
RCODE
  Relative segment, address: CODE 0000021C - 0000028B (0x70 bytes), align: 0
  Segment part 1.
           ENTRY                   ADDRESS         REF BY
           =====                   =======         ======
           ?UL_DIV_MOD             0000021C        ?L_DIV_MOD (?L_DIV_MOD)

    -------------------------------------------------------------------------
  LIBRARY MODULE, NAME : ?L_DIV_MOD

  SEGMENTS IN THE MODULE
  ======================
RCODE
  Relative segment, address: CODE 0000028C - 000002E2 (0x57 bytes), align: 0
  Segment part 0.
           ENTRY                   ADDRESS         REF BY
           =====                   =======         ======
           ?L_DIV_MOD              000002B6        convInt32ToText (hal_int)

    -------------------------------------------------------------------------
  LIBRARY MODULE, NAME : ?L_NEG_R1

  SEGMENTS IN THE MODULE
  ======================
RCODE
  Relative segment, address: CODE 000002E3 - 000002F3 (0x11 bytes), align: 0
  Segment part 1.
           ENTRY                   ADDRESS         REF BY
           =====                   =======         ======
           ?L_NEG_R1               000002E3        ?L_DIV_MOD (?L_DIV_MOD)

    -------------------------------------------------------------------------
  LIBRARY MODULE, NAME : ?L_NEG

  SEGMENTS IN THE MODULE
  ======================
RCODE
  Relative segment, address: CODE 000002F4 - 00000304 (0x11 bytes), align: 0
  Segment part 1.
           ENTRY                   ADDRESS         REF BY
           =====                   =======         ======
           ?L_NEG                  000002F4        ?L_DIV_MOD (?L_DIV_MOD)
                                                   convInt32ToText (hal_int)

    -------------------------------------------------------------------------
  LIBRARY MODULE, NAME : ?L_MOV_X

  SEGMENTS IN THE MODULE
  ======================
RCODE
  Relative segment, address: CODE 00000305 - 00000313 (0xf bytes), align: 0
  Segment part 0.
           ENTRY                   ADDRESS         REF BY
           =====                   =======         ======
           ?L_MOV_X                00000305        convInt32ToText (hal_int)
                                                   utilLcdDisplayValue (hal_int)

    -------------------------------------------------------------------------
  LIBRARY MODULE, NAME : ?L_MOV_TO_X

  SEGMENTS IN THE MODULE
  ======================
RCODE
  Relative segment, address: CODE 00000314 - 00000322 (0xf bytes), align: 0
  Segment part 2.
           ENTRY                   ADDRESS         REF BY
           =====                   =======         ======
           ?L_MOV_TO_X             00000314        convInt32ToText (hal_int)

    -------------------------------------------------------------------------
  LIBRARY MODULE, NAME : ?MOVE_LONG8_XDATA_IDATA

  SEGMENTS IN THE MODULE
  ======================
RCODE
  Relative segment, address: CODE 00000323 - 0000032F (0xd bytes), align: 0
  Segment part 0.
           ENTRY                   ADDRESS         REF BY
           =====                   =======         ======
           ?MOVE_LONG8_XDATA_IDATA
                                   00000323        ?PUSH_XSTACK_I (?PUSH_XSTACK_I)

    -------------------------------------------------------------------------
  LIBRARY MODULE, NAME : ?MOVE_LONG8_XDATA_XDATA

  SEGMENTS IN THE MODULE
  ======================
RCODE
  Relative segment, address: CODE 00000330 - 00000350 (0x21 bytes), align: 0
  Segment part 0.
           ENTRY                   ADDRESS         REF BY
           =====                   =======         ======
           ?MOVE_LONG8_XDATA_XDATA
                                   00000330        utilPrintLogo (hal_int)

    -------------------------------------------------------------------------
  LIBRARY MODULE, NAME : ?S_DIV_MOD

  SEGMENTS IN THE MODULE
  ======================
RCODE
  Relative segment, address: CODE 00000351 - 00000391 (0x41 bytes), align: 0
  Segment part 0.
           ENTRY                   ADDRESS         REF BY
           =====                   =======         ======
           ?S_DIV_MOD              0000038A        main (hal_int)
    -------------------------------------------------------------------------
RCODE
  Relative segment, address: CODE 00000392 - 000003EA (0x59 bytes), align: 0
  Segment part 1.             Intra module refs:   ?S_DIV_MOD
           ENTRY                   ADDRESS         REF BY
           =====                   =======         ======
           ?US_DIV_MOD             00000392 

    -------------------------------------------------------------------------
  LIBRARY MODULE, NAME : ?S_SHL

  SEGMENTS IN THE MODULE
  ======================
RCODE
  Relative segment, address: CODE 000003EB - 000003ED (0x3 bytes), align: 0
  Segment part 0.
           ENTRY                   ADDRESS         REF BY
           =====                   =======         ======
           ?S_SHL                  000003EB        adcSampleSingle (hal_int)
                                                   halDigioConfig (hal_int)
                                                   halLcdWriteChar (hal_int)
                                                   lcdWriteLine (hal_int)
                                                   main (hal_int)
                                                   port0_ISR (hal_int)
                                                   port1_ISR (hal_int)
                                                   port2_ISR (hal_int)
    -------------------------------------------------------------------------
RCODE
  Relative segment, address: CODE 000003EE - 000003F0 (0x3 bytes), align: 0
  Segment part 1.             Intra module refs:   Segment part 2
           ENTRY                   ADDRESS         REF BY
           =====                   =======         ======
           ?S_SHL_REW              000003EE 
    -------------------------------------------------------------------------
RCODE
  Relative segment, address: CODE 000003F1 - 000003FD (0xd bytes), align: 0
  Segment part 2.             Intra module refs:   ?S_SHL
                                                   ?S_SHL_REW

    -------------------------------------------------------------------------
  LIBRARY MODULE, NAME : ?ALLOC_XSTACK8

  SEGMENTS IN THE MODULE
  ======================
RCODE
  Relative segment, address: CODE 000003FE - 00000413 (0x16 bytes), align: 0
  Segment part 0.
           ENTRY                   ADDRESS         REF BY
           =====                   =======         ======
           ?ALLOC_XSTACK8          000003FE        convInt32ToText (hal_int)
                                                   lcdWriteLine (hal_int)
                                                   utilPrintLogo (hal_int)

    -------------------------------------------------------------------------
  LIBRARY MODULE, NAME : ?DEALLOC_XSTACK8

  SEGMENTS IN THE MODULE
  ======================
RCODE
  Relative segment, address: CODE 00000414 - 0000042D (0x1a bytes), align: 0
  Segment part 0.
           ENTRY                   ADDRESS         REF BY
           =====                   =======         ======
           ?DEALLOC_XSTACK8        00000414        ?Subroutine14 (hal_int)
                                                   main (hal_int)
                                                   utilPrintLogo (hal_int)

    -------------------------------------------------------------------------
  LIBRARY MODULE, NAME : ?ADD_XSTACK_DISP8

  SEGMENTS IN THE MODULE
  ======================
RCODE
  Relative segment, address: CODE 0000042E - 00000448 (0x1b bytes), align: 0
  Segment part 1.
           ENTRY                   ADDRESS         REF BY
           =====                   =======         ======
           ?ADD_XSTACK_DISP0_8     0000042E        ?PUSH_XSTACK_I (?PUSH_XSTACK_I)

    -------------------------------------------------------------------------
  LIBRARY MODULE, NAME : ?XSTACK_DISP8

  SEGMENTS IN THE MODULE
  ======================
RCODE
  Relative segment, address: CODE 00000449 - 00000452 (0xa bytes), align: 0
  Segment part 1.
           ENTRY                   ADDRESS         REF BY
           =====                   =======         ======
           ?XSTACK_DISP0_8         00000449        convInt32ToText (hal_int)
                                                   strncpy (?strncpy)
                                                   utilLcdDisplayValue (hal_int)
                                                   utilPrintLogo (hal_int)

    -------------------------------------------------------------------------
  LIBRARY MODULE, NAME : ?CALL_IND

  SEGMENTS IN THE MODULE
  ======================
RCODE
  Relative segment, address: CODE 00000453 - 00000454 (0x2 bytes), align: 0
  Segment part 0.
           ENTRY                   ADDRESS         REF BY
           =====                   =======         ======
           ?CALL_IND               00000453        halJoystickMoveISR (hal_int)
                                                   port0_ISR (hal_int)
                                                   port1_ISR (hal_int)
                                                   port2_ISR (hal_int)
                                                   rfIsr (hal_int)

    -------------------------------------------------------------------------
  LIBRARY MODULE, NAME : ?FUNC_ENTER_XDATA

  SEGMENTS IN THE MODULE
  ======================
RCODE
  Relative segment, address: CODE 00000455 - 000004B4 (0x60 bytes), align: 0
  Segment part 1.
           ENTRY                   ADDRESS         REF BY
           =====                   =======         ======
           ?FUNC_ENTER_XDATA       00000455        appConfigIO (hal_int)
                                                   convInt32ToText (hal_int)
                                                   halDigioConfig (hal_int)
                                                   halJoystickGetDir (hal_int)
                                                   halLcdInit (hal_int)
                                                   halLcdWriteChar (hal_int)
                                                   halLcdWriteLine (hal_int)
                                                   halMcuWaitMs (hal_int)
                                                   lcdWriteLine (hal_int)
                                                   strcat (?strcat)
                                                   strncpy (?strncpy)
                                                   utilLcdDisplayValue (hal_int)
                                                   utilPrintLogo (hal_int)

    -------------------------------------------------------------------------
  LIBRARY MODULE, NAME : ?FUNC_LEAVE_XDATA

  SEGMENTS IN THE MODULE
  ======================
RCODE
  Relative segment, address: CODE 000004B5 - 000004EC (0x38 bytes), align: 0
  Segment part 1.
           ENTRY                   ADDRESS         REF BY
           =====                   =======         ======
           ?FUNC_LEAVE_XDATA       000004B5        ?Subroutine14 (hal_int)
                                                   ?Subroutine15 (hal_int)
                                                   ?Subroutine16 (hal_int)
                                                   halLcdWriteChar (hal_int)
                                                   halMcuWaitMs (hal_int)
                                                   strcat (?strcat)
                                                   strncpy (?strncpy)
                                                   utilLcdDisplayValue (hal_int)
                                                   utilPrintLogo (hal_int)

    -------------------------------------------------------------------------
  LIBRARY MODULE, NAME : ?INTERRUPT_ENTER_XSP

  SEGMENTS IN THE MODULE
  ======================
RCODE
  Relative segment, address: CODE 000004ED - 0000054B (0x5f bytes), align: 0
  Segment part 1.
           ENTRY                   ADDRESS         REF BY
           =====                   =======         ======
           ?INTERRUPT_ENTER_XSP    000004ED        port0_ISR (hal_int)
                                                   port1_ISR (hal_int)
                                                   port2_ISR (hal_int)
                                                   rfIsr (hal_int)

    -------------------------------------------------------------------------
  LIBRARY MODULE, NAME : ?INTERRUPT_LEAVE_XSP

  SEGMENTS IN THE MODULE
  ======================
RCODE
  Relative segment, address: CODE 0000054C - 0000059B (0x50 bytes), align: 0
  Segment part 1.
           ENTRY                   ADDRESS         REF BY
           =====                   =======         ======
           ?INTERRUPT_LEAVE_XSP    0000054C        ?Subroutine17 (hal_int)
                                                   rfIsr (hal_int)

    -------------------------------------------------------------------------
  LIBRARY MODULE, NAME : ?PUSH_XSTACK_I

  SEGMENTS IN THE MODULE
  ======================
RCODE
  Relative segment, address: CODE 0000059C - 0000059F (0x4 bytes), align: 0
  Segment part 0.
           ENTRY                   ADDRESS         REF BY
           =====                   =======         ======
           ?PUSH_XSTACK_I_FOUR     0000059C        main (hal_int)
    -------------------------------------------------------------------------
RCODE
  Relative segment, address: CODE 000005A0 - 000005A3 (0x4 bytes), align: 0
  Segment part 2.
           ENTRY                   ADDRESS         REF BY
           =====                   =======         ======
           ?PUSH_XSTACK_I_TWO      000005A0        utilPrintLogo (hal_int)
    -------------------------------------------------------------------------
RCODE
  Relative segment, address: CODE 000005A4 - 000005B0 (0xd bytes), align: 0
  Segment part 4.             Intra module refs:   ?PUSH_XSTACK_I_FOUR
                                                   ?PUSH_XSTACK_I_TWO
           ENTRY                   ADDRESS         REF BY
           =====                   =======         ======
           ?PUSH_XSTACK_I          000005A4 

    -------------------------------------------------------------------------
  LIBRARY MODULE, NAME : ?UC_SWITCH_DENSE

  SEGMENTS IN THE MODULE
  ======================
RCODE
  Relative segment, address: CODE 000005B1 - 000005B6 (0x6 bytes), align: 0
  Segment part 0.
           ENTRY                   ADDRESS         REF BY
           =====                   =======         ======
           ?UC_SWITCH_DENSE        000005B1        halDigioConfig (hal_int)
                                                   halDigioIntClear (hal_int)
                                                   halJoystickIntConnect (hal_int)
                                                   halJoystickMoveISR (hal_int)
                                                   halLedToggle (hal_int)
    -------------------------------------------------------------------------
RCODE
  Relative segment, address: CODE 000005B7 - 000005E5 (0x2f bytes), align: 0
  Segment part 2.             Intra module refs:   ?UC_SWITCH_DENSE
    -------------------------------------------------------------------------
RCODE
  Relative segment, address: CODE 000005E6 - 000005E6 (0x1 bytes), align: 0
  Segment part 4.             Intra module refs:   ?UC_SWITCH_DENSE

    -------------------------------------------------------------------------
  LIBRARY MODULE, NAME : ?UC_SWITCH_SPARSE

  SEGMENTS IN THE MODULE
  ======================
RCODE
  Relative segment, address: CODE 000005E7 - 000005ED (0x7 bytes), align: 0
  Segment part 0.
           ENTRY                   ADDRESS         REF BY
           =====                   =======         ======
           ?UC_SWITCH_SPARSE       000005E7        utilPrintLogo (hal_int)
    -------------------------------------------------------------------------
RCODE
  Relative segment, address: CODE 000005EE - 0000060F (0x22 bytes), align: 0
  Segment part 2.             Intra module refs:   ?UC_SWITCH_SPARSE
           ENTRY                   ADDRESS         REF BY
           =====                   =======         ======
           ?UC_SWITCH_SPARSE_LEAVE
                                   00000604        ?UC_JMP_IF_IN_RANGE (?UC_JMP_IF_IN_RANGE)
                                                   ?UC_JMP_IF_VALUE (?UC_JMP_IF_VALUE)
    -------------------------------------------------------------------------
RCODE
  Relative segment, address: CODE 00000610 - 00000610 (0x1 bytes), align: 0
  Segment part 4.             Intra module refs:   ?UC_SWITCH_SPARSE

    -------------------------------------------------------------------------
  LIBRARY MODULE, NAME : ?UC_JMP_IF_IN_RANGE

  SEGMENTS IN THE MODULE
  ======================
RCODE
  Relative segment, address: CODE 00000611 - 0000062F (0x1f bytes), align: 0
  Segment part 0.
           ENTRY                   ADDRESS         REF BY
           =====                   =======         ======
           ?UC_JMP_IF_IN_RANGE     00000611        ?UC_SWITCH_SPARSE_LEAVE (?UC_SWITCH_SPARSE)

    -------------------------------------------------------------------------
  LIBRARY MODULE, NAME : ?UC_JMP_IF_VALUE

  SEGMENTS IN THE MODULE
  ======================
RCODE
  Relative segment, address: CODE 00000630 - ******** (0x16 bytes), align: 0
  Segment part 0.
           ENTRY                   ADDRESS         REF BY
           =====                   =======         ======
           ?UC_JMP_IF_VALUE        00000630        ?UC_SWITCH_SPARSE_LEAVE (?UC_SWITCH_SPARSE)

    -------------------------------------------------------------------------
  LIBRARY MODULE, NAME : ?strcat

  SEGMENTS IN THE MODULE
  ======================
NEAR_CODE
  Relative segment, address: CODE 00001A0A - 00001A3F (0x36 bytes), align: 0
  Segment part 6.
           ENTRY                   ADDRESS         REF BY
           =====                   =======         ======
           strcat                  00001A0A        utilPrintLogo (hal_int)
               XSTACK = ******** ( ******** )
               ISTACK = ******** ( ******** )

    -------------------------------------------------------------------------
  LIBRARY MODULE, NAME : ?strlen

  SEGMENTS IN THE MODULE
  ======================
NEAR_CODE
  Relative segment, address: CODE 00001A40 - 00001A5B (0x1c bytes), align: 0
  Segment part 6.
           ENTRY                   ADDRESS         REF BY
           =====                   =======         ======
           strlen                  00001A40        lcdWriteLine (hal_int)
                                                   utilPrintLogo (hal_int)
               ISTACK = ******** ( 00000002 )

    -------------------------------------------------------------------------
  LIBRARY MODULE, NAME : ?strncpy

  SEGMENTS IN THE MODULE
  ======================
NEAR_CODE
  Relative segment, address: CODE 00001A5C - 00001AC2 (0x67 bytes), align: 0
  Segment part 6.
           ENTRY                   ADDRESS         REF BY
           =====                   =======         ======
           strncpy                 00001A5C        utilPrintLogo (hal_int)
               XSTACK = 00000002 ( 00000009 )
               ISTACK = ******** ( ******** )

    -------------------------------------------------------------------------
  LIBRARY MODULE, NAME : ?low_level_init

  SEGMENTS IN THE MODULE
  ======================
CSTART
  Relative segment, address: CODE 000000E2 - 000000E4 (0x3 bytes), align: 0
  Segment part 6. ROOT.
           ENTRY                   ADDRESS         REF BY
           =====                   =======         ======
           __low_level_init        000000E2        __low_level_init_call (?cmain)




                ****************************************
                *                                      *
                *            MODULE SUMMARY            *
                *                                      *
                ****************************************

Module                    CODE      DATA      XDATA  IDATA    BIT
------                    ----      ----      -----  -----    ---
                         (Rel)  (Rel)  (Abs)  (Rel)  (Rel)  (Rel)
?ADD_XSTACK_DISP8           27
?ALLOC_XSTACK8              22
?CALL_IND                    2
?DEALLOC_XSTACK8            26
?FUNC_ENTER_XDATA           96
?FUNC_LEAVE_XDATA           56
?INTERRUPT_ENTER_XSP        95
?INTERRUPT_LEAVE_XSP        80
?L_DIV_MOD                  87
?L_EQ_X                     21
?L_MOV_TO_X                 15
?L_MOV_X                    15
?L_NEG                      17
?L_NEG_R1                   17
?MOVE_LONG8_XDATA_IDATA     13
?MOVE_LONG8_XDATA_XDATA     33
?PUSH_XSTACK_I              21
?SL_GT_X                    21
?S_DIV_MOD                 154
?S_SHL                      19
?UC_JMP_IF_IN_RANGE         31
?UC_JMP_IF_VALUE            22
?UC_SWITCH_DENSE            54
?UC_SWITCH_SPARSE           42
?UL_DIV_MOD                112
?XSTACK_DISP8               10
?cexit                       5
?cmain                      72
?low_level_init              3
?strcat                     54
?strlen                     28
?strncpy                   103
CSTARTUP                    15
  + common                   3
VIRTUAL_REGISTERS                   2                           8
hal_int                  5 329            41    500
  + common                 134
N/A (command line)                 16           256     64
N/A (alignment)
----------               -----     --     --    ---     --      -
Total:                   6 717     18     41    756     64      8
  + common                 134


                ****************************************
                *                                      *
                *      SEGMENTS IN ADDRESS ORDER       *
                *                                      *
                ****************************************


SEGMENT              SPACE    START ADDRESS   END ADDRESS     SIZE  TYPE  ALIGN
=======              =====    =============   ===========     ====  ====  =====
INTVEC               CODE          ******** - 00000085          86   com    0
CSTART               CODE          00000086 - 000000E4          5F   rel    0
IDATA_ID             CODE               000000E5                     dse    0
BIT_ID               CODE               000000E5                     dse    0
BDATA_ID             CODE               000000E5                     dse    0
IXDATA_ID            CODE               000000E5                     dse    0
PDATA_ID             CODE               000000E5                     dse    0
DATA_ID              CODE               000000E5                     dse    0
XDATA_ID             CODE          000000E5 - 000001F1         10D   rel    0
BANK_RELAYS          CODE               000001F2                     dse    0
RCODE                CODE          000001F2 - ********         454   rel    0
DIFUNCT              CODE               ********                     dse    0
CODE_C               CODE          ******** - 00000C45         600   rel    0
CODE_N               CODE               00000C46                     dse    0
NEAR_CODE            CODE          00000C46 - 00001AC2         E7D   rel    0
REGISTERS            DATA          ******** - ********           8   rel    0
VREG                 DATA          ******** - 0000000F           8   rel    0
PSP                  DATA               ********                     dse    0
XSP                  DATA          ******** - ********           2   rel    0
BREG                 BIT        ********.0  -  ********.7        8   rel    0
SFR_AN               DATA          ******** - ********           1   rel    0
                     DATA          ******** - 0000008D           6 
                     DATA          0000008F - 00000090           2 
                     DATA          0000009A - 0000009B           2 
                     DATA          0000009D - 0000009E           2 
                     DATA          000000A0 - 000000A0           1 
                     DATA          000000A8 - 000000A8           1 
                     DATA          000000AB - 000000AC           2 
                     DATA          000000B6 - 000000B6           1 
                     DATA          000000B8 - 000000B8           1 
                     DATA          000000BA - 000000BB           2 
                     DATA          000000BE - 000000BE           1 
                     DATA          000000C0 - 000000C0           1 
                     DATA          000000C6 - 000000C6           1 
                     DATA          000000E1 - 000000E1           1 
                     DATA          000000E8 - 000000E9           2 
                     DATA          000000F1 - 000000F5           5 
                     DATA          000000F7 - 000000FF           9 
XSTACK               XDATA         ******** - ********         100   rel    0
XDATA_Z              XDATA         00000101 - 000001E7          E7   rel    0
XDATA_I              XDATA         000001E8 - 000002F4         10D   rel    0
ISTACK               IDATA         000000C0 - 000000FF          40   rel    0

                ****************************************
                *                                      *
                *        END OF CROSS REFERENCE        *
                *                                      *
                ****************************************

 6 851 bytes of CODE  memory
    18 bytes of DATA  memory (+ 41 absolute )
   756 bytes of XDATA memory
    64 bytes of IDATA memory
     8 bits  of BIT   memory

Errors: none
Warnings: none

