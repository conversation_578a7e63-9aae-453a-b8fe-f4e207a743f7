<?xml version="1.0" encoding="iso-8859-1"?>

<project>
  <fileVersion>2</fileVersion>
  <configuration>
    <name>srf05_cc2530_91</name>
    <toolchain>
      <name>8051</name>
    </toolchain>
    <debug>1</debug>
    <settings>
      <name>General</name>
      <archiveVersion>1</archiveVersion>
      <data>
        <version>3</version>
        <wantNonLocal>1</wantNonLocal>
        <debug>1</debug>
        <option>
          <name>DerivativeDescriptionFile</name>
          <state>$TOOLKIT_DIR$\config\devices\Texas Instruments\CC2530.i51</state>
        </option>
        <option>
          <name>Previous Derivative File</name>
          <state>$TOOLKIT_DIR$\config\devices\Texas Instruments\CC2530.i51</state>
        </option>
        <option>
          <name>Showed Derivative</name>
          <state>CC2530</state>
        </option>
        <option>
          <name>CPU Core</name>
          <version>1</version>
          <state>1</state>
        </option>
        <option>
          <name>CPU Core Slave</name>
          <version>1</version>
          <state>1</state>
        </option>
        <option>
          <name>Code Memory Model</name>
          <version>1</version>
          <state>1</state>
        </option>
        <option>
          <name>Code Memory Model slave</name>
          <version>1</version>
          <state>1</state>
        </option>
        <option>
          <name>Data Memory Model</name>
          <version>0</version>
          <state>2</state>
        </option>
        <option>
          <name>Data Memory Model slave</name>
          <version>0</version>
          <state>2</state>
        </option>
        <option>
          <name>Use extended stack</name>
          <state>0</state>
        </option>
        <option>
          <name>Use extended stack slave</name>
          <state>0</state>
        </option>
        <option>
          <name>Start of extended stack</name>
          <state>0x002000</state>
        </option>
        <option>
          <name>Calling convention</name>
          <version>0</version>
          <state>4</state>
        </option>
        <option>
          <name>Workseg Size</name>
          <version>0</version>
          <state>0</state>
        </option>
        <option>
          <name>Constant Placement</name>
          <state>0</state>
        </option>
        <option>
          <name>Datapointer Size</name>
          <state>0</state>
        </option>
        <option>
          <name>Nr of Datapointers</name>
          <version>0</version>
          <state>0</state>
        </option>
        <option>
          <name>Switch Method</name>
          <state>0</state>
        </option>
        <option>
          <name>Mask Value</name>
          <state>0x00</state>
        </option>
        <option>
          <name>DPS Address</name>
          <state></state>
        </option>
        <option>
          <name>Sfr Visibility</name>
          <state>0</state>
        </option>
        <option>
          <name>DPTR Addresses</name>
          <state></state>
        </option>
        <option>
          <name>CodeBankReg</name>
          <state>0x9F</state>
        </option>
        <option>
          <name>CodeBankStart</name>
          <state>0x8000</state>
        </option>
        <option>
          <name>CodeBankSize</name>
          <state>0xFFFF</state>
        </option>
        <option>
          <name>ExePath</name>
          <state></state>
        </option>
        <option>
          <name>ObjPath</name>
          <state>temp\per_test</state>
        </option>
        <option>
          <name>ListPath</name>
          <state>temp</state>
        </option>
        <option>
          <name>GOutputBinary</name>
          <state>0</state>
        </option>
        <option>
          <name>GRuntimeLibSelect</name>
          <version>0</version>
          <state>4</state>
        </option>
        <option>
          <name>GRuntimeLibSelectSlave</name>
          <version>0</version>
          <state>4</state>
        </option>
        <option>
          <name>RTDescription</name>
          <state>Use the legacy C runtime library.</state>
        </option>
        <option>
          <name>RTConfigPath</name>
          <state></state>
        </option>
        <option>
          <name>RTLibraryPath</name>
          <state>$TOOLKIT_DIR$\LIB\CLIB\cl-pli-nlxd-1e16x01.r51</state>
        </option>
        <option>
          <name>Input variant</name>
          <version>0</version>
          <state>2</state>
        </option>
        <option>
          <name>Input description</name>
          <state>No float.</state>
        </option>
        <option>
          <name>Output variant</name>
          <version>0</version>
          <state>2</state>
        </option>
        <option>
          <name>Output description</name>
          <state>No float.</state>
        </option>
        <option>
          <name>GeneralEnableMisra</name>
          <state>0</state>
        </option>
        <option>
          <name>GeneralMisraVerbose</name>
          <state>0</state>
        </option>
        <option>
          <name>General Idata Stack Size</name>
          <state>0x40</state>
        </option>
        <option>
          <name>General Pdata Stack Size</name>
          <state>0x80</state>
        </option>
        <option>
          <name>General Xdata Stack Size</name>
          <state>0x100</state>
        </option>
        <option>
          <name>General Ext Stack Size</name>
          <state>0x3FF</state>
        </option>
        <option>
          <name>General Xdata Heap Size</name>
          <state>0xFF</state>
        </option>
        <option>
          <name>General Far Heap Size</name>
          <state>0xFFF</state>
        </option>
        <option>
          <name>General Huge Heap Size</name>
          <state>0xFFF</state>
        </option>
        <option>
          <name>CodeBankNrOfs</name>
          <state>0x07</state>
        </option>
        <option>
          <name>CodeBankRegMask</name>
          <state>0x07</state>
        </option>
        <option>
          <name>GeneralMisraRules98</name>
          <version>0</version>
          <state>1000111110110101101110011100111111101110011011000101110111101101100111111111111100110011111001110111001111111111111111111111111</state>
        </option>
      </data>
    </settings>
    <settings>
      <name>ICC8051</name>
      <archiveVersion>3</archiveVersion>
      <data>
        <version>6</version>
        <wantNonLocal>1</wantNonLocal>
        <debug>1</debug>
        <option>
          <name>CCOptSizeSpeedSlave</name>
          <state>0</state>
        </option>
        <option>
          <name>CCOptimizationSlave</name>
          <version>0</version>
          <state>0</state>
        </option>
        <option>
          <name>OutputFile</name>
          <state>$FILE_BNAME$.r51</state>
        </option>
        <option>
          <name>CCDefines</name>
          <state>chip=2530</state>
          <state>INCLUDE_PA=2591</state>
        </option>
        <option>
          <name>CCPreprocFile</name>
          <state>0</state>
        </option>
        <option>
          <name>CCPreprocComments</name>
          <state>0</state>
        </option>
        <option>
          <name>CCPreprocLine</name>
          <state>0</state>
        </option>
        <option>
          <name>CCListCFile</name>
          <state>0</state>
        </option>
        <option>
          <name>CCListCMnemonics</name>
          <state>0</state>
        </option>
        <option>
          <name>CCListCMessages</name>
          <state>0</state>
        </option>
        <option>
          <name>CCListAssFile</name>
          <state>0</state>
        </option>
        <option>
          <name>CCListAssSource</name>
          <state>0</state>
        </option>
        <option>
          <name>CCEnableRemarks</name>
          <state>0</state>
        </option>
        <option>
          <name>CCDiagSuppress</name>
          <state>Pe001,Pa084</state>
        </option>
        <option>
          <name>CCDiagRemark</name>
          <state></state>
        </option>
        <option>
          <name>CCDiagWarning</name>
          <state></state>
        </option>
        <option>
          <name>CCDiagError</name>
          <state></state>
        </option>
        <option>
          <name>CCObjPrefix</name>
          <state>1</state>
        </option>
        <option>
          <name>LangConform</name>
          <state>0</state>
        </option>
        <option>
          <name>CharIs</name>
          <state>1</state>
        </option>
        <option>
          <name>CCRequirePrototypes</name>
          <state>0</state>
        </option>
        <option>
          <name>CCMultibyteSupport</name>
          <state>0</state>
        </option>
        <option>
          <name>CCMigrationPreprocExtentions</name>
          <state>0</state>
        </option>
        <option>
          <name>CCAllowList</name>
          <version>1</version>
          <state>11101</state>
        </option>
        <option>
          <name>CCObjUseModuleName</name>
          <state>0</state>
        </option>
        <option>
          <name>CCObjModuleName</name>
          <state></state>
        </option>
        <option>
          <name>CCDebugInfo</name>
          <state>1</state>
        </option>
        <option>
          <name>OCCProcessorVariant</name>
          <state>1</state>
        </option>
        <option>
          <name>OCCDptr</name>
          <state>1</state>
        </option>
        <option>
          <name>OCCDataMemoryModel</name>
          <state>1</state>
        </option>
        <option>
          <name>OCCCodeMemoryModel</name>
          <state>1</state>
        </option>
        <option>
          <name>OCCCallingConvention</name>
          <state>1</state>
        </option>
        <option>
          <name>OCCConstantPlacement</name>
          <state>1</state>
        </option>
        <option>
          <name>OCCNrOfVirtualRegisters</name>
          <state>1</state>
        </option>
        <option>
          <name>Extended stack</name>
          <state>1</state>
        </option>
        <option>
          <name>CCDiagWarnAreErr</name>
          <state>0</state>
        </option>
        <option>
          <name>CCCompilerRuntimeInfo</name>
          <state>0</state>
        </option>
        <option>
          <name>RomMonBpPadding</name>
          <state>0</state>
        </option>
        <option>
          <name>PreInclude</name>
          <state></state>
        </option>
        <option>
          <name>CCLibConfigHeader</name>
          <state>1</state>
        </option>
        <option>
          <name>CCLangSelect</name>
          <state>0</state>
        </option>
        <option>
          <name>NoUBROFMessages</name>
          <state>0</state>
        </option>
        <option>
          <name>CompilerMisraOverride</name>
          <state>0</state>
        </option>
        <option>
          <name>Compiler Extra Options Check</name>
          <state>0</state>
        </option>
        <option>
          <name>Compiler Extra Options Edit</name>
          <state></state>
        </option>
        <option>
          <name>CCIncludePath2</name>
          <state>$PROJ_DIR$/../../../source/Components/utils</state>
          <state>$PROJ_DIR$/../../../source/Components/common</state>
          <state>$PROJ_DIR$/../../../source/Components/basicrf</state>
          <state>$PROJ_DIR$/../../../source/Components/targets/interface</state>
          <state>$PROJ_DIR$/../../../source/Components/assembly</state>
          <state>$PROJ_DIR$/../../../source/Components/common/cc8051</state>
          <state>$PROJ_DIR$/../../../source/Components/targets/srf05_soc</state>
          <state>$PROJ_DIR$/../../../source/Components/radios/cc2530</state>
        </option>
        <option>
          <name>CCStdIncCheck</name>
          <state>0</state>
        </option>
        <option>
          <name>CCStdIncludePath</name>
          <state>$TOOLKIT_DIR$\INC\</state>
          <state>$TOOLKIT_DIR$\INC\CLIB\</state>
        </option>
        <option>
          <name>CompilerMisraRules98</name>
          <version>0</version>
          <state>1000111110110101101110011100111111101110011011000101110111101101100111111111111100110011111001110111001111111111111111111111111</state>
        </option>
        <option>
          <name>CCOverrideModuleTypeDefault</name>
          <state>0</state>
        </option>
        <option>
          <name>CCRadioModuleType</name>
          <state>0</state>
        </option>
        <option>
          <name>CCRadioModuleTypeSlave</name>
          <state>1</state>
        </option>
        <option>
          <name>CCOptLevel</name>
          <state>3</state>
        </option>
        <option>
          <name>CCOptStrategy</name>
          <version>0</version>
          <state>1</state>
        </option>
        <option>
          <name>CCOptLevelSlave</name>
          <state>3</state>
        </option>
      </data>
    </settings>
    <settings>
      <name>A8051</name>
      <archiveVersion>2</archiveVersion>
      <data>
        <version>4</version>
        <wantNonLocal>1</wantNonLocal>
        <debug>1</debug>
        <option>
          <name>OAProcessorVariant</name>
          <state>1</state>
        </option>
        <option>
          <name>Generated Preproc defines</name>
          <state>0</state>
        </option>
        <option>
          <name>AObjPrefix</name>
          <state>1</state>
        </option>
        <option>
          <name>OutputFile</name>
          <state>$FILE_BNAME$.r51</state>
        </option>
        <option>
          <name>ACaseSensitivity</name>
          <state>1</state>
        </option>
        <option>
          <name>MacroChars</name>
          <version>0</version>
          <state>0</state>
        </option>
        <option>
          <name>Asm multibyte support</name>
          <state>0</state>
        </option>
        <option>
          <name>Debug</name>
          <state>1</state>
        </option>
        <option>
          <name>AList</name>
          <state>0</state>
        </option>
        <option>
          <name>AListHeader</name>
          <state>1</state>
        </option>
        <option>
          <name>AListing</name>
          <state>1</state>
        </option>
        <option>
          <name>Includes</name>
          <state>0</state>
        </option>
        <option>
          <name>MacDefs</name>
          <state>0</state>
        </option>
        <option>
          <name>MacExps</name>
          <state>1</state>
        </option>
        <option>
          <name>MacExec</name>
          <state>0</state>
        </option>
        <option>
          <name>OnlyAssed</name>
          <state>0</state>
        </option>
        <option>
          <name>MultiLine</name>
          <state>0</state>
        </option>
        <option>
          <name>NoStruct</name>
          <state>1</state>
        </option>
        <option>
          <name>PageLengthCheck</name>
          <state>0</state>
        </option>
        <option>
          <name>PageLength</name>
          <state>80</state>
        </option>
        <option>
          <name>TabSpacing</name>
          <state>8</state>
        </option>
        <option>
          <name>AXRef</name>
          <state>0</state>
        </option>
        <option>
          <name>AXRefDefines</name>
          <state>0</state>
        </option>
        <option>
          <name>AXRefInternal</name>
          <state>0</state>
        </option>
        <option>
          <name>AXRefDual</name>
          <state>0</state>
        </option>
        <option>
          <name>AIncludes</name>
          <state>$TOOLKIT_DIR$\INC\</state>
          <state>$PROJ_DIR$\components\radios\cc2530\</state>
        </option>
        <option>
          <name>ADefines</name>
          <state></state>
        </option>
        <option>
          <name>AWarnEnable</name>
          <state>0</state>
        </option>
        <option>
          <name>AWarnWhat</name>
          <state>0</state>
        </option>
        <option>
          <name>AWarnOne</name>
          <state></state>
        </option>
        <option>
          <name>AWarnRange1</name>
          <state></state>
        </option>
        <option>
          <name>AWarnRange2</name>
          <state></state>
        </option>
        <option>
          <name>Assembler Extra Options Check</name>
          <state>0</state>
        </option>
        <option>
          <name>Assembler Extra Options Edit</name>
          <state></state>
        </option>
        <option>
          <name>AMaxErrOn</name>
          <state>0</state>
        </option>
        <option>
          <name>AMaxErrNum</name>
          <state>100</state>
        </option>
      </data>
    </settings>
    <settings>
      <name>CUSTOM</name>
      <archiveVersion>3</archiveVersion>
      <data>
        <extensions></extensions>
        <cmdline></cmdline>
      </data>
    </settings>
    <settings>
      <name>BICOMP</name>
      <archiveVersion>0</archiveVersion>
      <data/>
    </settings>
    <settings>
      <name>BUILDACTION</name>
      <archiveVersion>1</archiveVersion>
      <data>
        <prebuild></prebuild>
        <postbuild></postbuild>
      </data>
    </settings>
    <settings>
      <name>XLINK</name>
      <archiveVersion>3</archiveVersion>
      <data>
        <version>16</version>
        <wantNonLocal>1</wantNonLocal>
        <debug>1</debug>
        <option>
          <name>XOutOverride</name>
          <state>0</state>
        </option>
        <option>
          <name>OutputFile</name>
          <state>per_test.d51</state>
        </option>
        <option>
          <name>OutputFormat</name>
          <version>11</version>
          <state>23</state>
        </option>
        <option>
          <name>FormatVariant</name>
          <version>8</version>
          <state>12</state>
        </option>
        <option>
          <name>SecondaryOutputFile</name>
          <state>(None for the selected format)</state>
        </option>
        <option>
          <name>XDefines</name>
          <state></state>
        </option>
        <option>
          <name>AlwaysOutput</name>
          <state>0</state>
        </option>
        <option>
          <name>OverlapWarnings</name>
          <state>0</state>
        </option>
        <option>
          <name>NoGlobalCheck</name>
          <state>0</state>
        </option>
        <option>
          <name>XList</name>
          <state>1</state>
        </option>
        <option>
          <name>SegmentMap</name>
          <state>1</state>
        </option>
        <option>
          <name>ListSymbols</name>
          <state>2</state>
        </option>
        <option>
          <name>PageLengthCheck</name>
          <state>0</state>
        </option>
        <option>
          <name>PageLength</name>
          <state>80</state>
        </option>
        <option>
          <name>XIncludes</name>
          <state>$TOOLKIT_DIR$\LIB\</state>
        </option>
        <option>
          <name>ModuleStatus</name>
          <state>0</state>
        </option>
        <option>
          <name>XclOverride</name>
          <state>1</state>
        </option>
        <option>
          <name>XclFile</name>
          <state>lnk51ew_cc2530b.xcl</state>
        </option>
        <option>
          <name>XclFileSlave</name>
          <state></state>
        </option>
        <option>
          <name>Linker Code Banking</name>
          <state>1</state>
        </option>
        <option>
          <name>Config Include Dir</name>
          <state>1</state>
        </option>
        <option>
          <name>XLink Dptr Switch mask</name>
          <state>1</state>
        </option>
        <option>
          <name>OHXNrOfVirtualRegisters</name>
          <state>1</state>
        </option>
        <option>
          <name>OHX DPS Address</name>
          <state>1</state>
        </option>
        <option>
          <name>XLINK Dptr Addresses</name>
          <state>1</state>
        </option>
        <option>
          <name>OXLibIOConfig</name>
          <state>1</state>
        </option>
        <option>
          <name>XInfineonPFlashCacheBug</name>
          <state>0</state>
        </option>
        <option>
          <name>DoFill</name>
          <state>0</state>
        </option>
        <option>
          <name>FillerByte</name>
          <state>0xFF</state>
        </option>
        <option>
          <name>DoCrc</name>
          <state>0</state>
        </option>
        <option>
          <name>CrcSize</name>
          <version>0</version>
          <state>1</state>
        </option>
        <option>
          <name>CrcAlgo</name>
          <state>1</state>
        </option>
        <option>
          <name>CrcPoly</name>
          <state>0x11021</state>
        </option>
        <option>
          <name>CrcCompl</name>
          <version>0</version>
          <state>0</state>
        </option>
        <option>
          <name>RangeCheckAlternatives</name>
          <state>0</state>
        </option>
        <option>
          <name>SuppressAllWarn</name>
          <state>0</state>
        </option>
        <option>
          <name>SuppressDiags</name>
          <state></state>
        </option>
        <option>
          <name>TreatAsWarn</name>
          <state></state>
        </option>
        <option>
          <name>TreatAsErr</name>
          <state></state>
        </option>
        <option>
          <name>ModuleLocalSym</name>
          <version>0</version>
          <state>0</state>
        </option>
        <option>
          <name>CrcBitOrder</name>
          <version>0</version>
          <state>0</state>
        </option>
        <option>
          <name>IncludeSuppressed</name>
          <state>0</state>
        </option>
        <option>
          <name>ModuleSummary</name>
          <state>1</state>
        </option>
        <option>
          <name>xcProgramEntryLabel</name>
          <state>__program_start</state>
        </option>
        <option>
          <name>DebugInformation</name>
          <state>0</state>
        </option>
        <option>
          <name>RuntimeControl</name>
          <state>1</state>
        </option>
        <option>
          <name>IoEmulation</name>
          <state>1</state>
        </option>
        <option>
          <name>AllowExtraOutput</name>
          <state>1</state>
        </option>
        <option>
          <name>GenerateExtraOutput</name>
          <state>1</state>
        </option>
        <option>
          <name>XExtraOutOverride</name>
          <state>1</state>
        </option>
        <option>
          <name>ExtraOutputFile</name>
          <state>per_test.hex</state>
        </option>
        <option>
          <name>ExtraOutputFormat</name>
          <version>11</version>
          <state>23</state>
        </option>
        <option>
          <name>ExtraFormatVariant</name>
          <version>8</version>
          <state>2</state>
        </option>
        <option>
          <name>xcOverrideProgramEntryLabel</name>
          <state>0</state>
        </option>
        <option>
          <name>xcProgramEntryLabelSelect</name>
          <state>0</state>
        </option>
        <option>
          <name>ListOutputFormat</name>
          <state>0</state>
        </option>
        <option>
          <name>BufferedTermOutput</name>
          <state>0</state>
        </option>
        <option>
          <name>OverlaySystemMap</name>
          <state>0</state>
        </option>
        <option>
          <name>RawBinaryFile</name>
          <state></state>
        </option>
        <option>
          <name>RawBinarySymbol</name>
          <state></state>
        </option>
        <option>
          <name>RawBinarySegment</name>
          <state></state>
        </option>
        <option>
          <name>RawBinaryAlign</name>
          <state></state>
        </option>
        <option>
          <name>XLinkMisraHandler</name>
          <state>0</state>
        </option>
        <option>
          <name>XcRTLibraryFile</name>
          <state>1</state>
        </option>
        <option>
          <name>Linker Idata Stack Size</name>
          <state>1</state>
        </option>
        <option>
          <name>Linker Ext Stack Size</name>
          <state>1</state>
        </option>
        <option>
          <name>Linker Pdata Stack Size</name>
          <state>1</state>
        </option>
        <option>
          <name>Linker Xdata Stack Size</name>
          <state>1</state>
        </option>
        <option>
          <name>Linker Xdata Heap Size</name>
          <state>1</state>
        </option>
        <option>
          <name>Linker Far Heap Size</name>
          <state>1</state>
        </option>
        <option>
          <name>Linker Huge Heap Size</name>
          <state>1</state>
        </option>
        <option>
          <name>Linker Extra Options Check</name>
          <state>0</state>
        </option>
        <option>
          <name>Linker Extra Options Edit</name>
          <state></state>
        </option>
        <option>
          <name>CrcAlign</name>
          <state>1</state>
        </option>
        <option>
          <name>CrcInitialValue</name>
          <state>0x0</state>
        </option>
      </data>
    </settings>
    <settings>
      <name>XAR</name>
      <archiveVersion>1</archiveVersion>
      <data>
        <version>0</version>
        <wantNonLocal>1</wantNonLocal>
        <debug>1</debug>
        <option>
          <name>XARInputs</name>
          <state></state>
        </option>
        <option>
          <name>XAROverride</name>
          <state>0</state>
        </option>
        <option>
          <name>XAR Standard name</name>
          <state>0</state>
        </option>
        <option>
          <name>XAROutput</name>
          <state>###Uninitialized###</state>
        </option>
      </data>
    </settings>
    <settings>
      <name>BILINK</name>
      <archiveVersion>0</archiveVersion>
      <data/>
    </settings>
  </configuration>
  <group>
    <name>application</name>
    <file>
      <name>$PROJ_DIR$\..\..\..\source\apps\PER_test\per_test.c</name>
    </file>
    <file>
      <name>$PROJ_DIR$\..\..\..\source\apps\PER_test\per_test.h</name>
    </file>
    <file>
      <name>$PROJ_DIR$\..\..\..\source\apps\PER_test\per_test_menu.c</name>
    </file>
    <file>
      <name>$PROJ_DIR$\..\..\..\source\apps\PER_test\per_test_menu.h</name>
    </file>
  </group>
  <group>
    <name>basic rf</name>
    <file>
      <name>$PROJ_DIR$\..\..\..\source\Components\basicrf\basic_rf.c</name>
    </file>
    <file>
      <name>$PROJ_DIR$\..\..\..\source\Components\basicrf\basic_rf.h</name>
    </file>
  </group>
  <group>
    <name>hal</name>
    <group>
      <name>common</name>
      <file>
        <name>$PROJ_DIR$\..\..\..\source\Components\common\cc8051\hal_cc8051.h</name>
      </file>
      <file>
        <name>$PROJ_DIR$\..\..\..\source\Components\common\hal_defs.h</name>
      </file>
      <file>
        <name>$PROJ_DIR$\..\..\..\source\Components\common\hal_int.c</name>
      </file>
      <file>
        <name>$PROJ_DIR$\..\..\..\source\Components\targets\common\hal_lcd_srf05.c</name>
      </file>
      <file>
        <name>$PROJ_DIR$\..\..\..\source\Components\common\hal_types.h</name>
      </file>
    </group>
    <group>
      <name>interface</name>
      <file>
        <name>$PROJ_DIR$\..\..\..\source\Components\targets\interface\hal_assert.h</name>
      </file>
      <file>
        <name>$PROJ_DIR$\..\..\..\source\Components\targets\interface\hal_button.h</name>
      </file>
      <file>
        <name>$PROJ_DIR$\..\..\..\source\Components\targets\interface\hal_digio.h</name>
      </file>
      <file>
        <name>$PROJ_DIR$\..\..\..\source\Components\targets\interface\hal_int.h</name>
      </file>
      <file>
        <name>$PROJ_DIR$\..\..\..\source\Components\targets\interface\hal_joystick.h</name>
      </file>
      <file>
        <name>$PROJ_DIR$\..\..\..\source\Components\targets\interface\hal_lcd.h</name>
      </file>
      <file>
        <name>$PROJ_DIR$\..\..\..\source\Components\targets\interface\hal_led.h</name>
      </file>
      <file>
        <name>$PROJ_DIR$\..\..\..\source\Components\targets\interface\hal_mcu.h</name>
      </file>
      <file>
        <name>$PROJ_DIR$\..\..\..\source\Components\targets\interface\hal_timer_32k.h</name>
      </file>
      <file>
        <name>$PROJ_DIR$\..\..\..\source\Components\targets\interface\hal_uart.h</name>
      </file>
    </group>
    <group>
      <name>rf</name>
      <group>
        <name>cc2530</name>
        <file>
          <name>$PROJ_DIR$\..\..\..\source\Components\radios\cc2530\adc.c</name>
        </file>
        <file>
          <name>$PROJ_DIR$\..\..\..\source\Components\radios\cc2530\adc.h</name>
        </file>
        <file>
          <name>$PROJ_DIR$\..\..\..\source\Components\radios\cc2530\clock.c</name>
        </file>
        <file>
          <name>$PROJ_DIR$\..\..\..\source\Components\radios\cc2530\clock.h</name>
        </file>
        <file>
          <name>$PROJ_DIR$\..\..\..\source\Components\radios\cc2530\hal_mcu.c</name>
        </file>
        <file>
          <name>$PROJ_DIR$\..\..\..\source\Components\radios\cc2530\hal_rf.c</name>
        </file>
        <file>
          <name>$PROJ_DIR$\..\..\..\source\Components\radios\cc2530\hal_timer_32k.c</name>
        </file>
      </group>
      <file>
        <name>$PROJ_DIR$\..\..\..\source\Components\targets\interface\hal_rf.h</name>
      </file>
    </group>
    <group>
      <name>srf05_soc</name>
      <file>
        <name>$PROJ_DIR$\..\..\..\source\Components\targets\srf05_soc\hal_assert.c</name>
      </file>
      <file>
        <name>$PROJ_DIR$\..\..\..\source\Components\targets\srf05_soc\hal_board.c</name>
      </file>
      <file>
        <name>$PROJ_DIR$\..\..\..\source\Components\targets\srf05_soc\hal_board.h</name>
      </file>
      <file>
        <name>$PROJ_DIR$\..\..\..\source\Components\targets\srf05_soc\hal_button.c</name>
      </file>
      <file>
        <name>$PROJ_DIR$\..\..\..\source\Components\targets\srf05_soc\hal_digio.c</name>
      </file>
      <file>
        <name>$PROJ_DIR$\..\..\..\source\Components\targets\srf05_soc\hal_joystick.c</name>
      </file>
      <file>
        <name>$PROJ_DIR$\..\..\..\source\Components\targets\srf05_soc\hal_led.c</name>
      </file>
    </group>
  </group>
  <group>
    <name>utilities</name>
    <file>
      <name>$PROJ_DIR$\..\..\..\source\Components\utils\util.c</name>
    </file>
    <file>
      <name>$PROJ_DIR$\..\..\..\source\Components\utils\util.h</name>
    </file>
    <file>
      <name>$PROJ_DIR$\..\..\..\source\Components\utils\util_buffer.c</name>
    </file>
    <file>
      <name>$PROJ_DIR$\..\..\..\source\Components\utils\util_buffer.h</name>
    </file>
    <file>
      <name>$PROJ_DIR$\..\..\..\source\Components\utils\util_lcd.c</name>
    </file>
    <file>
      <name>$PROJ_DIR$\..\..\..\source\Components\utils\util_lcd.h</name>
    </file>
  </group>
</project>


