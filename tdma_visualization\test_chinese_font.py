#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
matplotlib中文字体测试脚本
用于验证和修复中文显示问题
"""

import sys
import os

def test_matplotlib_chinese():
    """测试matplotlib中文字体支持"""
    print("=" * 60)
    print("matplotlib中文字体测试")
    print("=" * 60)
    
    try:
        import matplotlib.pyplot as plt
        import matplotlib.font_manager as fm
        print("✓ matplotlib导入成功")
    except ImportError:
        print("✗ matplotlib未安装")
        print("请运行: pip install matplotlib")
        return False
    
    # 检查系统可用字体
    print("\n1. 检查系统可用的中文字体:")
    available_fonts = [f.name for f in fm.fontManager.ttflist]
    
    chinese_fonts = [
        'SimHei',           # 黑体 (Windows)
        'Microsoft YaHei',  # 微软雅黑 (Windows)
        'PingFang SC',      # 苹方 (macOS)
        'Hiragino Sans GB', # 冬青黑体 (macOS)
        'WenQuanYi Micro Hei', # 文泉驿微米黑 (Linux)
        'Noto Sans CJK SC', # 思源黑体 (Linux)
        'SimSun',           # 宋体 (Windows)
        'KaiTi',            # 楷体 (Windows)
    ]
    
    found_fonts = []
    for font in chinese_fonts:
        if font in available_fonts:
            found_fonts.append(font)
            print(f"  ✓ {font}")
        else:
            print(f"  ✗ {font}")
    
    if not found_fonts:
        print("  ⚠ 未找到常见的中文字体")
        print("  建议安装中文字体包")
    
    # 配置字体
    print("\n2. 配置matplotlib中文字体:")
    try:
        if found_fonts:
            selected_font = found_fonts[0]
            plt.rcParams['font.sans-serif'] = [selected_font] + chinese_fonts
            print(f"  ✓ 已选择字体: {selected_font}")
        else:
            plt.rcParams['font.sans-serif'] = chinese_fonts
            print("  ⚠ 使用默认配置，可能无法正确显示中文")
            
        plt.rcParams['axes.unicode_minus'] = False
        print("  ✓ 负号显示问题已修复")
    except Exception as e:
        print(f"  ✗ 字体配置失败: {e}")
        return False
    
    # 创建测试图表
    print("\n3. 创建测试图表:")
    try:
        import numpy as np
        
        # 创建测试数据
        x = np.linspace(0, 10, 100)
        y1 = np.sin(x)
        y2 = np.cos(x)
        
        # 创建图表
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(12, 5))
        
        # 第一个子图 - 温度趋势
        ax1.plot(x, y1 * 25 + 25, 'b-', label='节点1')
        ax1.plot(x, y2 * 25 + 25, 'r-', label='节点2')
        ax1.set_title('温度趋势图')
        ax1.set_xlabel('时间')
        ax1.set_ylabel('温度 (°C)')
        ax1.legend()
        ax1.grid(True, alpha=0.3)
        
        # 第二个子图 - 振动状态
        ax2.plot(x, np.abs(y1), 'g-', label='振动传感器')
        ax2.set_title('振动状态图')
        ax2.set_xlabel('时间')
        ax2.set_ylabel('振动值')
        ax2.legend()
        ax2.grid(True, alpha=0.3)
        
        plt.tight_layout()
        
        # 保存测试图片
        output_file = 'chinese_font_test.png'
        plt.savefig(output_file, dpi=150, bbox_inches='tight')
        print(f"  ✓ 测试图表已保存: {output_file}")
        
        # 显示图表
        print("  ✓ 正在显示测试图表...")
        plt.show()
        
        return True
        
    except Exception as e:
        print(f"  ✗ 创建测试图表失败: {e}")
        return False

def get_font_recommendations():
    """获取字体安装建议"""
    import platform
    system = platform.system()
    
    print("\n" + "=" * 60)
    print("字体安装建议")
    print("=" * 60)
    
    if system == "Windows":
        print("Windows系统建议:")
        print("1. 系统通常已包含 SimHei (黑体) 和 Microsoft YaHei (微软雅黑)")
        print("2. 如果字体缺失，可以:")
        print("   - 从其他Windows系统复制字体文件到 C:\\Windows\\Fonts\\")
        print("   - 下载并安装思源黑体: https://github.com/adobe-fonts/source-han-sans")
        
    elif system == "Darwin":  # macOS
        print("macOS系统建议:")
        print("1. 系统通常已包含 PingFang SC (苹方) 和 Hiragino Sans GB")
        print("2. 如果需要更多字体，可以:")
        print("   - 安装思源黑体: brew install font-source-han-sans")
        print("   - 从App Store安装字体应用")
        
    elif system == "Linux":
        print("Linux系统建议:")
        print("1. 安装中文字体包:")
        print("   Ubuntu/Debian: sudo apt install fonts-wqy-microhei fonts-noto-cjk")
        print("   CentOS/RHEL: sudo yum install wqy-microhei-fonts")
        print("   Arch Linux: sudo pacman -S wqy-microhei noto-fonts-cjk")
        print("2. 刷新字体缓存: fc-cache -fv")
        
    else:
        print(f"未知系统: {system}")
        print("请查阅相关文档安装中文字体")

def main():
    """主函数"""
    print("TDMA传感器网络 - matplotlib中文字体测试工具")
    
    # 测试matplotlib中文支持
    success = test_matplotlib_chinese()
    
    if not success:
        get_font_recommendations()
        print("\n建议:")
        print("1. 安装中文字体后重新运行此测试")
        print("2. 如果问题持续，可以使用英文界面版本")
        return False
    
    print("\n" + "=" * 60)
    print("测试完成!")
    print("如果图表中的中文显示正常，说明配置成功")
    print("如果显示为方块或乱码，请按照建议安装中文字体")
    print("=" * 60)
    
    return True

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n测试被用户中断")
    except Exception as e:
        print(f"\n测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
    
    input("\n按回车键退出...")
