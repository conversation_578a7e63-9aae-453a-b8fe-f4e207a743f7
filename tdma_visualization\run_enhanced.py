#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
TDMA增强监控系统启动脚本
包含数据库存储和图形化显示功能
"""

import sys

def main():
    print("=" * 60)
    print("TDMA传感器网络增强监控系统")
    print("包含数据库存储和图形化显示功能")
    print("=" * 60)
    print()
    
    try:
        # 检查Python版本
        if sys.version_info < (3, 6):
            print("错误: 需要Python 3.6或更高版本")
            print(f"当前版本: {sys.version}")
            input("按回车键退出...")
            return
        
        print(f"Python版本: {sys.version_info.major}.{sys.version_info.minor}.{sys.version_info.micro}")
        print()
        
        # 检查依赖包
        missing_packages = []
        
        # 检查pyserial
        try:
            import serial
            print("✓ 检测到pyserial，支持真实串口连接")
        except ImportError:
            print("⚠ 未检测到pyserial，将使用演示模式")
            print("  如需连接真实串口，请运行: pip install pyserial")
            missing_packages.append("pyserial")
        
        # 检查matplotlib
        try:
            import matplotlib
            print("✓ 检测到matplotlib，支持图表显示")
        except ImportError:
            print("⚠ 未检测到matplotlib，图表功能将被禁用")
            print("  如需图表功能，请运行: pip install matplotlib")
            missing_packages.append("matplotlib")
            
        # 检查sqlite3（Python内置）
        try:
            import sqlite3
            print("✓ 检测到sqlite3，支持数据库存储")
        except ImportError:
            print("✗ sqlite3不可用，这不应该发生")
            
        print()
        
        if missing_packages:
            print("建议安装缺失的包以获得完整功能:")
            for pkg in missing_packages:
                print(f"  pip install {pkg}")
            print()
            
        print("启动增强监控系统...")
        
        # 启动增强版本
        import enhanced_monitor
        enhanced_monitor.main()
        
    except ImportError as e:
        print(f"导入错误: {e}")
        print("\n可能的解决方案:")
        print("1. 安装缺失的依赖包: pip install pyserial matplotlib")
        print("2. 使用简化版本: python simple_monitor.py")
        input("按回车键退出...")
    except Exception as e:
        print(f"程序运行错误: {e}")
        input("按回车键退出...")

if __name__ == "__main__":
    main()
